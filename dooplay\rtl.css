.sbox .sdata h1,
.sheader .data h1 {
    font-size: 20px
}
.page_user header.user .box .contenido .info span {
    border-left: solid 1px #e1e5ea;
    border-right: none
}
.dooplay_player {
    float: right
}
.dooplay_player h2 {
    float: right
}
.dooplay_player h2 span {
    float: left
}
.dooplay_player .options {
    float: right
}
.dooplay_player .options.onload:before {
    right: 0;
    left: auto
}
.dooplay_player .options ul {
    float: right
}
.dooplay_player .options ul li {
    float: right
}
.dooplay_player .options ul li i {
    margin-left: 15px;
    margin-right: 0;
    float: right
}
.dooplay_player .options ul li i.yt {
    margin-left: 0
}
.dooplay_player .options ul li span.title {
    float: right
}
.dooplay_player .options ul li span.server {
    margin-right: 15px;
    margin-left: 0
}
.dooplay_player .options ul li span.loader {
    float: left;
    right: auto;
    left: auto;
    margin-left: 20px;
    margin-right: 0
}
.dooplay_player .options ul li span.flag {
    float: left
}
.dooplay_player .play {
    float: right
}
.dooplay_player .play .asgdc {
    right: 50%;
    left: auto;
    margin-right: -150px;
    margin-left: 0
}
.dooplay_player .play .pframe iframe {
    right: 0;
    left: auto
}
#single .content {
    border-right: none;
    border-left: solid 1px #ECEFF5
}
.post-comments li.comment .scontent {
    border-left: none;
    border-right: solid 1px
}
.module_home_ads #resultado_link_form .msg {
    font-size: 14px
}
.head-main-nav ul.main-header li ul.sub-menu li a:before {
    content: "\f0d9"
}
#message .sms .sent {
    border-right: solid 5px;
    border-left: none
}
body {
    direction: rtl;
    unicode-bidi: embed;
    font-family: tahoma
}
.ni-ellipsis,
.ni-widget-header-span,
button {
    font-family: tahoma
}
.tleft {
    padding-left: 25px;
    border-left: solid 1px #eceff5;
    border-right: none
}
.tax_post .tax_box .links a {
    padding: 6px;
    font-size: 11px;
    margin: 2px 0 2px 2px !important;
    border-radius: 2px;
    display: inline-block
}
.posts header.pos h1.titl {
    font-size: 25px
}
.wp-content p {
    font-size: 14.4px;
    line-height: 27px
}
.movieads {
    width: 100%;
    height: auto;
    padding-top: 10px
}
article.post .information {
    height: 121px
}
.drive-viewer-toolstrip {
    display: none
}
.runtime {
    direction: ltr
}
.title {
    direction: rtl
}
header.main .hbox .search form input[type=text],
p.form-submit input[type=submit],
.login_box .box input[type="submit"],
input {
    font-family: tahoma
}
.dtinfo .genres .mta {
    height: 13px
}
.icon-angle-left {
    padding-right: 10px
}
.dt_mainmeta nav.genres ul li a:before {
    content: "\f0d9"
}
.icon-chevron-left:before {
    content: "\f054"
}
.icon-chevron-right:before {
    content: "\f053"
}
#archive-content {
    direction: ltr
}
.tax_post .tax_box .title {
    border-left: solid 6px;
    border-right: none
}
.texto {
    text-align: right;
    direction: rtl
}
.module .content .items .episodes .poster span.serie {
    direction: rtl
}
.form_dt_user fieldset.min {
    float: right
}
.fix {
    margin-left: 4%;
    margin-right: 0
}
.dtinfo .genres .mta a {
    border-left: solid 1px;
    border-right: none
}
.dtuser a.clicklogin {
    border-right: solid 2px;
    border-left: none
}
.sgeneros a {
    border-right: solid 1px;
    border-left: none
}
.tooltip-right::after {
    border-color: transparent transparent transparent #111
}
.user_control a {
    border-right: solid 1px #f5f7fa;
    border-left: none
}
.module .content header h1,
.module .content header h2 {
    border-right: solid 3px;
    border-left: none
}
.icon-caret-left:before {
    content: "\f0da"
}
.icon-caret-right:before {
    content: "\f0d9"
}
.data h3 {
    text-align: right
}
.slider article.item .image .data {
    text-align: right
}
.owl-carousel .owl-wrapper-outer {
    direction: ltr
}
#dt-episodes .item,
#dt-movies .item,
#dt-seasons .item,
#dt-tvshows .item,
#featured-titles .item,
.owl-carousel .owl-item,
.slider {
    float: left
}
.alignnone {
    margin: 5px 0 20px 20px
}
.alignright {
    float: left;
    margin: 5px 20px 20px 0
}
.alignleft {
    float: right;
    margin: 5px 0 20px 20px
}
a img.alignright {
    float: left;
    margin: 5px 20px 20px 0
}
a img.alignnone {
    margin: 5px 0 20px 20px
}
a img.alignleft {
    float: right;
    margin: 5px 0 20px 20px
}
.wp-caption.alignnone {
    margin: 5px 0 20px 20px
}
.wp-caption.alignleft {
    margin: 5px 0 20px 20px
}
.wp-caption.alignright {
    margin: 5px 20px 20px 0
}
.screen-reader-text:focus {
    left: auto;
    right: 5px
}
#dt_contenedor {
    float: right
}
header.main {
    float: right
}
header.main .hbox .logo {
    float: right;
    margin-right: 0;
    margin-left: 17px
}
header.main .hbox .search {
    float: right
}
header.main .hbox .search form button[type=submit] {
    right: auto;
    left: 0
}
.module {
    float: right
}
.module .sidebar {
    float: left;
    right: auto;
    left: 0
}
.module .content {
    margin-right: 0;
    margin-left: 320px;
    float: right;
    border-left: solid 1px #ECEFF5;
    border-right: 0
}
.module .content .items {
    float: right
}
.module .content header {
    float: right
}
.module .content header h1,
.module .content header h2 {
    float: right;
    padding-left: 0;
    padding-right: 10px
}
.module .content header span {
    float: left
}
.module .content header span a.see-all {
    margin-left: 0;
    margin-right: 6px
}
.module .content .items .item {
    float: right
}
.module .content .items .item .poster {
    float: right
}
.module .content .items .item .poster a .see {
    right: auto;
    left: 0
}
.module .content .items .item .poster .rating {
    right: auto;
    left: 0
}
.module .content .items .item .data {
    float: right;
    direction: rtl
}
.module .content .items .item .data h3 {
    float: right
}
.module .content .items .item .data span {
    float: right
}
.module .content .items .item .left {
    right: auto;
    left: -326px
}
.module .content .items .item .right {
    left: auto;
    right: -326px
}
.sidemenu {
    float: right
}
.sidemenu ul.genres li {
    float: right
}
.sidemenu ul.genres li i {
    float: left;
    right: auto;
    left: 0
}
.sidemenu ul.genres li a:before {
    margin-right: 0;
    margin-left: 10px
}
.sidemenu ul.genres li a {
    float: right
}
footer.main {
    float: right
}
footer.main .fbox .fmenu {
    float: left
}
footer.main .fbox .fmenu ul li {
    float: right
}
footer.main .fbox .fmenu ul li:first-child {
    border-right: 0;
    padding-right: 0
}
footer.main .fbox .fmenu ul li:last-child {
    border-left: 0
}
footer.main .fbox .copy {
    float: right
}
.pagination {
    float: right
}
.pagination a,
.pagination span {
    float: right
}
.sidemenu ul.year li {
    float: right
}
.sidemenu ul.year li a {
    float: right
}
.estadisticas {
    left: auto;
    right: 0
}
body.page-template-top_imdb .module .content .items .item .poster .rating {
    left: auto;
    right: 0
}
#serie_contenido {
    float: right
}
#seasons,
#seasons .se-c {
    float: right
}
#seasons .se-c .se-q {
    float: right;
    padding-left: 0;
    padding-right: 105px
}
#seasons .se-c .se-q span.title {
    float: right
}
#seasons .se-c .se-q span.title i {
    margin-left: 0;
    margin-right: 10px
}
#seasons .se-c .se-q span.se-t {
    float: right;
    left: auto;
    right: 0
}
#seasons .se-c .se-a,
#seasons .se-c .se-a ul.episodios {
    float: right
}
#seasons .se-c .se-a ul.episodios li {
    float: right
}
#seasons .se-c .se-a ul.episodios li .numerando {
    float: right;
    border-left: solid 1px #F5F7FA
}
#seasons .se-c .se-a ul.episodios li .imagen {
    float: right
}
#seasons .se-c .se-a ul.episodios li .episodiotitle {
    float: right;
    padding-left: 0;
    padding-right: 190px
}
#seasons .se-c .se-a ul.episodios li .episodiotitle a {
    float: right
}
#seasons .se-c .se-a ul.episodios li .episodiotitle span.date {
    float: right
}
i.delete {
    right: auto;
    left: 0
}
#linking {
    float: right
}
#single,
.box_links,
.links_table,
.links_table table {
    float: right
}
.links_table table thead tr th {
    text-align: right
}
.links_table table tbody tr td img {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
a.link_a i {
    margin-right: 0;
    margin-left: 5px
}
.links_table a.addlink {
    float: right
}
.dtsingle .content {
    float: right
}
#single .content .sbox {
    float: right
}
.dtsingle .sidebar {
    float: right;
    right: auto;
    left: 0
}
.sbox h2,
.sbox h1 {
    float: right
}
.sbox h2 a.addlink {
    float: left
}
a.a_f_sbox {
    float: right
}
a.a_f_sbox i {
    float: left;
    margin-left: 0;
    margin-right: 10px
}
.se .poster .season_m {
    left: auto;
    right: 0
}
.se .poster .season_m span {
    float: right
}
.nav_items_module {
    float: left
}
.nav_items_module a.btn {
    padding-left: 0;
    padding-right: 10px;
    float: left
}
.slider {
    float: right
}
.slider article.item {
    float: right
}
.slider article.item .image .data {
    float: right;
    left: auto;
    right: 0
}
.player {
    float: right
}
.mepo {
    left: auto;
    right: 5px
}
.mepo span.quality {
    float: right;
    margin-right: 0;
    margin-left: 5px
}
.module .content .items .item .data h3 span.flag {
    float: right;
    margin-right: 0;
    margin-left: 5px
}
.sbox .sposter {
    float: right;
    margin-left: 20px
}
.sbox .sdata {
    float: right
}
.likebox {
    right: 0
}
.sheader {
    float: right
}
.sheader .poster {
    float: right
}
.sheader .poster img {
    float: right
}
.sheader .data {
    margin-left: 0;
    margin-right: 160px
}
.sidebar aside.widget {
    float: right
}
.sidebar aside.widget h2.widget-title {
    float: right
}
.sidebar aside.widget ul {
    float: right
}
.sidebar aside.widget ul li {
    float: right
}
.sidebar aside.widget ul li a {
    text-align: right
}
.sidebar aside.widget ul li:before {
    margin-right: 0;
    margin-left: 10px;
    float: right
}
.calendar_wrap {
    float: right
}
form.search-form {
    float: right
}
form.search-form button[type=submit] {
    right: auto;
    left: 0
}
.tagcloud {
    float: right
}
.tagcloud a {
    float: right
}
.widget_rss span.rss-date {
    float: right
}
.widget_rss cite {
    float: right
}
.textwidget {
    float: right
}
span.post-date {
    float: right
}
.w_item_a {
    float: right
}
.w_item_a .image {
    float: right
}
.w_item_a .image .data {
    left: auto;
    right: 0;
    float: right
}
.w_item_b {
    float: right
}
.w_item_b .image {
    left: auto;
    right: 0
}
.w_item_b .data {
    margin-left: 0;
    margin-right: 80px;
    float: right
}
.w_item_b .data span.wdate {
    float: right
}
.w_item_b .data span.wextra {
    float: right
}
.w_item_b .data span.wextra b {
    float: right
}
.w_item_c {
    float: right
}
.w_item_c .rating {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
.w_item_c .data {
    float: right
}
.widget-social ul.social-links li.dtl a {
    float: right
}
.widget-social ul.social-links li.dtl i.dt-icon {
    margin-right: 0;
    margin-left: 5px;
    float: right
}
.resppages {
    float: right
}
.resppages a {
    float: right
}
header.responsive {
    float: right
}
header.responsive .nav {
    float: right
}
header.responsive .search {
    float: left
}
form.form-resp-ab {
    float: right
}
form.form-resp-ab button[type=submit] {
    right: auto;
    left: 0
}
.load_modules {
    float: right
}
.alert_dt_ee,
.galeria {
    float: right
}
.video-frame {
    left: auto;
    right: 50%
}
.sheader .poster .complex {
    float: right
}
.sheader .poster .complex:before {
    margin-right: 0;
    margin-left: 5px
}
.galeria .g-item {
    float: right
}
.single_tabs {
    float: right
}
ul.smenu {
    float: right
}
ul.smenu li {
    float: right
}
ul.smenu li a {
    float: right
}
.dt_mega_menu {
    float: right
}
.dt_mega_menu .box ul li:first-child {
    padding-right: 0;
    border-right: 0
}
.dt_mega_menu .box ul li:last-child {
    border-left: 0;
    padding-left: 0
}
.dt_mega_menu .box ul li {
    float: right
}
.link_sharing p a.addlink {
    float: left
}
.extra {
    float: right
}
.extra span {
    float: right;
    padding-right: 0;
    padding-left: 15px
}
.extra span a {
    margin-right: 0;
    margin-left: 15px
}
.srating {
    float: right
}
.srating .promedio {
    float: right
}
.srating .rdata {
    float: right;
    margin-left: 0;
    margin-right: 10px
}
.srating .rdata .stars {
    float: right
}
.srating .rdata .stars span.rating-stars-b {
    left: auto;
    right: 0
}
.srating .rdata .votes {
    float: right
}
.sgeneros {
    float: right
}
.sgeneros a:first-child {
    padding-left: 10px;
    padding-right: 0;
    border-right: 0
}
.sgeneros a {
    float: right
}
.videobox {
    float: right
}
.videobox .embed iframe {
    left: auto;
    right: 0
}
.sbox .custom_fields {
    float: right
}
.sbox .custom_fields b.variante {
    float: right
}
.sbox .custom_fields span.valor {
    float: right
}
.sbox .custom_fields span.valor strong {
    padding: 3px 25px 2px 10px;
    margin-right: 0;
    margin-left: 10px
}
.sbox .custom_fields span.valor strong:before {
    float: right;
    left: auto;
    right: 8px
}
.wp-content {
    float: right
}
.wp-content blockquote {
    padding: 10px 20px 10px 0
}
.wp-content ul,
.wp-content ol {
    padding-left: 0;
    padding-right: 30px
}
.comments-area {
    float: right
}
.comments-area h2 {
    float: right
}
.comments-area h2 i {
    float: right;
    margin-right: 0;
    margin-left: 5px
}
.post-comments {
    float: right
}
.post-comments li.comment {
    padding-left: 0;
    padding-right: 80px
}
.post-comments li.comment .comment-avatar {
    left: auto;
    right: 0
}
.post-comments li.comment .scontent {
    padding: 5px 15px 5px 5px
}
.post-comments ul li.comment .comment-avatar {
    left: auto;
    right: 15px
}
.post-comments ul li.comment .scontent {
    padding: 15px 70px 15px 10px
}
.post-comments .comment-time {
    margin-left: 0;
    margin-right: 10px
}
.post-comments .comment-reply-link {
    margin-left: 0;
    margin-right: 5px
}
.comment-respond h3:before {
    float: right;
    margin-right: 0;
    margin-left: 5px
}
.comment-respond h3 small {
    margin-left: 0;
    margin-right: 10px
}
.comment-form-comment .form-label {
    float: right
}
.grid-container {
    padding-right: 0;
    padding-left: 15px;
    float: right
}
.comment-navigation {
    float: right
}
.comment-navigation .nav-previous {
    float: right
}
.comment-navigation .nav-next {
    float: left
}
div.no-comments {
    float: right
}
.fix-grid {
    padding-left: 0;
    padding-right: 15px
}
div.post-like {
    float: right
}
div.post-like a,
div.post-like span.alreadyvoted {
    margin-right: 0;
    margin-left: 10px
}
.starstruck-wrap {
    float: right
}
.starstruck-rating-wrap {
    margin-left: 0;
    margin-right: 10px
}
.starstruck-rating i {
    float: right;
    margin-right: 0;
    margin-left: 5px
}
.starstruck-wrap .dt_rating_vgs {
    float: right;
    left: auto;
    right: 0
}
.starstruck-wrap .dt_rating_data {
    float: right;
    padding: 5px 70px 5px 0
}
.ads_l_single {
    float: right
}
.dt_nodata {
    float: right
}
#single .content .srelacionados {
    float: right
}
.srelacionados article {
    float: right
}
a.button {
    float: right
}
.dtloadpage {
    left: auto;
    right: 0
}
.dtloadpage .dtloadbox span {
    float: right
}
.dtloadpage .dtloadbox p {
    float: right
}
.se_rating {
    float: left
}
.se_rating .se_rating_valor {
    left: auto;
    right: 0
}
.se_rating:before {
    float: right;
    margin-left: 0;
    margin-right: -20px
}
.fixedform {
    padding-left: 0
}
#single_relacionados {
    float: right
}
.dt_social_single {
    float: right
}
.dt_social_single span {
    float: right;
    padding-right: 0;
    padding-left: 12px;
    margin-right: 0;
    margin-left: 15px;
    border-right: none
}
.dt_social_single b#social_count {
    float: left;
    margin-left: 0;
    margin-right: 10px
}
.dt_social_single a {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
.dt_social_single a b {
    margin-left: 0;
    margin-right: 7px
}
.link_sharing p strong {
    margin-right: 0;
    margin-left: 10px
}
.sl-wrapper a.liked:before,
.sl-wrapper a:before,
.sl-wrapper:hover>a.liked:before {
    margin-right: 0;
    margin-left: 5px;
    float: right
}
.sl-wrapper a b {
    margin-left: 0;
    margin-right: 5px
}
.sl-button {
    float: right
}
.sl-icon {
    margin-right: 0;
    margin-left: .3125em
}
span.sl-wrapper {
    float: left
}
.loader:before {
    left: auto;
    right: -.375em
}
.loader {
    left: auto;
    right: -16px
}
.loader:after {
    left: auto;
    right: .375em
}
article.simple {
    float: right
}
.no_fav {
    float: right
}
.list {
    float: right
}
.list .row {
    float: right
}
.list .row span.field {
    float: right
}
.extcom {
    float: right
}
.module .content .items .episodes .poster span.serie {
    left: auto;
    right: 0
}
ul.main_links li {
    float: right
}
ul.main_links li a {
    float: right
}
ul.main_links li a i {
    margin-right: 0;
    margin-left: 10px
}
span.item_type {
    right: auto;
    left: 0
}
span.s_trending a.m_trending {
    float: right
}
.contact {
    float: right
}
.contact .wrapper {
    float: right
}
.contact .wrapper fieldset {
    float: right
}
.contact .wrapper fieldset.fix {
    margin-left: 0;
    margin-right: 4%
}
.contact .wrapper fieldset label {
    float: right
}
.contact .wrapper fieldset p {
    float: right
}
.g-recaptcha {
    float: right
}
.loguser {
    float: right
}
.loguser a {
    float: right
}
form.update_profile {
    float: right
}
form.update_profile fieldset {
    float: right
}
form.update_profile fieldset.fix {
    margin-right: 0;
    margin-left: 2%
}
form.update_profile fieldset label {
    float: right
}
form.update_profile .updated {
    float: right
}
form.update_profile .error {
    float: right
}
form.update_profile .warning {
    float: right
}
.account {
    float: right
}
.account .sidebar {
    float: right;
    left: auto;
    right: 0
}
.account .content {
    margin-left: 0;
    margin-right: 219px;
    float: right
}
.single-page {
    float: right
}
.single-page h1.head {
    float: right
}
.sbackdrop h1 {
    left: auto;
    right: 0
}
.pag_episodes {
    float: right
}
.pag_episodes .item {
    float: right
}
.pag_episodes .item:last-child {
    border-left: 0
}
.pag_episodes .item a {
    float: right
}
.fix-table {
    float: right
}
.single_menu {
    float: right
}
.single_menu ul.main_dt_menu {
    float: right
}
.single_menu ul.main_dt_menu li {
    float: right
}
.single_menu ul.main_dt_menu li a {
    float: right
}
.module_home_ads {
    float: right
}
.module_single_ads {
    float: right
}
.video_player_enable {
    float: right
}
.video_player_enable .box {
    float: right
}
.video_player_enable .box .play {
    left: auto;
    right: 50%;
    margin-left: 0;
    margin-right: -23px
}
.video_player_enable .box span.quality {
    left: auto;
    right: 10px
}
.epiheader {
    float: right
}
.epiheader .epiposter {
    float: right
}
.epiheader .epidata {
    margin-left: 0;
    margin-right: 110px
}
.epiheader .epidata span.data {
    float: right
}
.epiheader .epidata span.data b {
    float: right
}
.epiheader .epidata span.data p {
    margin-left: 0;
    margin-right: 90px
}
.posts {
    float: right
}
.posts .meta {
    float: right
}
.posts .meta span {
    margin-right: 0;
    margin-left: 10px
}
.posts .meta .views {
    float: left
}
.posts header.pos {
    float: right
}
.post-entry {
    float: right
}
.home-blog-post {
    padding-left: 0;
    padding-right: 86px
}
.home-blog-post .entry-date {
    left: auto;
    right: 0
}
.top-imdb-list {
    float: right
}
.top-imdb-list h3 {
    float: right;
    padding-left: 0;
    padding-right: 10px;
    border-right: solid 3px;
    border-left: none
}
.top-imdb-item {
    padding-left: 0;
    padding-right: 130px;
    float: right
}
.top-imdb-item .puesto {
    left: auto;
    right: 54px
}
.top-imdb-item .image {
    left: auto;
    right: 0
}
.top-imdb-item .image .poster {
    float: right
}
.top-imdb-item .rating {
    left: auto;
    right: 85px
}
.top-imdb-item .title {
    float: right
}
.blog-list-items {
    float: right
}
.blog-list-items .entry {
    float: right
}
.blog-list-items .entry article.post {
    float: right
}
article.post .images {
    float: right
}
article.post .images .background_over_image {
    left: auto;
    right: 0
}
article.post .information {
    float: right
}
article.post .information .meta span.autor {
    margin-right: 0;
    margin-left: 10px
}
h1.top-imdb-h1 {
    text-align: right
}
.search-page .result-item {
    float: right
}
.result-item article {
    padding-left: 0;
    padding-right: 130px
}
.result-item article .image {
    left: auto;
    right: 0
}
.result-item article .details .meta {
    float: right
}
.result-item article .details .meta span.rating {
    margin-right: 0;
    margin-left: 10px;
    float: right
}
.result-item article .details .meta span.year {
    float: right;
    margin-right: 0;
    margin-left: 7px
}
.result-item article .details .meta span.flag {
    float: right;
    margin-right: 0;
    margin-left: 7px
}
.no-result {
    float: right
}
.no-result h2 {
    float: right
}
.no-result ul {
    margin: 20px 30px 20px 0
}
.tax_post {
    float: right
}
.tax_post .tax_box {
    padding-left: 0;
    padding-right: 110px;
    float: right
}
.tax_post .tax_box .title {
    left: auto;
    right: 0;
    text-align: left
}
.wp-content .video iframe {
    left: auto;
    right: 0
}
.player_sist {
    float: right
}
.player_sist .playex {
    float: right
}
.player_sist .playex .play-box-iframe iframe {
    left: auto;
    right: 0
}
.player_sist .control {
    float: right
}
.player_sist .control nav.player {
    float: right
}
.player_sist .control nav.player ul.options {
    float: right
}
.player_sist .control nav.player ul.options li {
    float: right
}
.player_sist .control nav.player ul.options li a {
    float: right;
    padding-left: 0;
    padding-right: 40px
}
.player_sist .control nav.player ul.options li a i {
    left: auto;
    right: 14px
}
.player_sist .control nav.player ul.options li ul {
    padding-right: 0;
    padding-left: 17px
}
.player_sist .control nav.player ul.options li ul li {
    text-align: right;
    float: right
}
.player_sist .control nav.controles {
    float: left
}
.player_sist .control nav.controles ul.list li {
    float: right
}
.player_sist .control nav.controles ul.list li.contadorads i {
    margin-left: 0;
    margin-right: 5px
}
.player_sist .control nav.controles ul.list li a {
    float: right
}
.player_sist .control span.views {
    float: right
}
.player_sist .control span.qualityx {
    float: right
}
.report-video-form p.desc {
    float: right
}
.report-video-form fieldset {
    float: right
}
.report-video-form fieldset.fixing {
    margin-left: 0;
    margin-right: 4%
}
.report-video-form fieldset label {
    float: right
}
.report-video-form fieldset textarea {
    float: right
}
.report-video-form fieldset input[type="submit"] {
    float: right
}
.report-video-active {
    left: auto;
    right: 50%;
    margin-left: 0;
    margin-right: -190px
}
.confirm_report {
    float: right
}
.error_report {
    float: right
}
#oscuridad {
    left: auto;
    right: 0
}
#single .episodes_cont {
    border-left: 0!important;
    float: right
}
.maindrop_ep {
    float: right
}
.maindrop_ep img {
    left: auto;
    right: 0
}
.fix_playerx {
    float: right
}
.ads_player {
    left: auto;
    right: 0
}
.ads_player .ads_box .ads {
    left: auto;
    right: 50%;
    margin-left: 0;
    margin-right: -150px
}
.ads_player .ads_box .ads span.notice {
    float: right
}
.headitems {
    float: left;
    margin-left: 0;
    margin-right: 20px
}
.headitems .elements {
    float: left
}
.head-main-nav {
    float: right
}
.head-main-nav ul.main-header {
    float: right
}
.head-main-nav ul.main-header li {
    float: right
}
.head-main-nav ul.main-header li a {
    float: right
}
.head-main-nav ul.main-header li.menu-item-has-children>a:after {
    margin-left: 0;
    margin-right: 5px;
    float: left
}
.head-main-nav ul.main-header li a i {
    margin-left: 0;
    margin-right: 5px;
    float: left
}
.head-main-nav ul.main-header li ul.sub-menu {
    padding-right: 0
}
.search_page_form {
    float: right
}
.search_page_form form {
    float: right
}
.search_page_form form input[type="text"] {
    float: right
}
.search_page_form form button {
    right: auto;
    left: 0
}
.reports_notice_admin {
    right: auto;
    left: 40px
}
.reports_notice_admin span {
    float: right
}
.reports_notice_admin span a.delete_notice {
    float: right
}
.persons {
    float: right
}
.person {
    padding-left: 0;
    padding-right: 73px;
    float: right
}
.person .img {
    left: auto;
    right: 0
}
.person .data {
    padding-right: 0;
    padding-left: 20px
}
.person .data .name {
    float: right
}
.person .data .caracter {
    float: right;
    line-height: 15px
}
span.dt_flag {
    float: left
}
.layout3 .sidebar {
    right: auto;
    left: 0
}
.layout3 .content {
    margin-right: 0;
    margin-left: 219px
}
.ds1 .content {
    margin-left: 0;
    margin-right: 340px
}
.ds1 .sidebar {
    left: auto;
    right: 0
}
.send_link {
    float: right
}
.mensaje_report {
    float: right
}
article.simple .poster {
    float: right
}
article.simple .data {
    float: right
}
article.simple .data h3 {
    float: right
}
ul.abc {
    float: right
}
ul.abc li {
    float: right
}
ul.abc li a {
    float: right
}
ul.abc li:first-child>a {
    margin-right: 0
}
ul.abc li:last-child>a {
    margin-left: 0
}
.desc_category {
    float: right
}
.form_post_lik {
    float: right
}
.form_post_lik .table {
    float: right
}
.form_post_lik .table table.post_table {
    float: right
}
.form_post_lik .table table th {
    text-align: right
}
.form_post_lik .control {
    float: right
}
.form_post_lik .control .left {
    float: right
}
.form_post_lik .control .left a.add_row {
    float: right
}
.form_post_lik .control .right {
    float: left
}
.tright {
    padding-left: 0;
    padding-right: 25px
}
.top-imdb-list h3 a {
    float: left
}
.live-search {
    margin-left: 0;
    margin-right: -1px;
    float: right
}
.live-search ul {
    float: right
}
.live-search ul li {
    float: right
}
.live-search ul li .poster {
    float: right;
    margin-right: 0;
    margin-left: 20px
}
.live-search ul li .release {
    margin-left: 0;
    margin-right: 5px
}
.live-search ul li .imdb span.icon-star {
    margin-right: 0;
    margin-left: 3px
}
.live-search ul li a.more {
    float: right
}
.adv_slider {
    float: right
}
.adv_slider .slider_box {
    float: right
}
.mensaje_ot {
    border-right: solid 3px;
    border-left: none
}
a.report-video span {
    float: left
}
a.mtoc {
    right: auto;
    left: 0
}
.player_sist .control nav.player ul.options li ul li a b {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
#sticky.stick {
    border-radius: 0 0 .5em .5em
}
.linktabs {
    float: right
}
.linktabs h2 {
    float: right;
    margin-right: 0;
    margin-left: 15px
}
.linktabs ul {
    float: right
}
.linktabs ul li {
    float: right
}
.linktabs ul li a {
    float: right
}
.mensaje_report i {
    float: right
}
.dt_social_single a.google i,
.dt_social_single a.pinterest i {
    margin-left: 0
}
.search_responsive {
    float: right
}
.search_responsive .live-search {
    float: right
}
.menuresp {
    float: right
}
.menuresp .menu {
    left: auto;
    right: 0
}
.menuresp .menu .user {
    float: right
}
.menuresp .menu .user .gravatar {
    float: right
}
.menuresp .menu .user .gravatar img {
    float: right
}
.menuresp .menu .user .gravatar span {
    float: right
}
.menuresp .menu .user .logout {
    float: left
}
.menuresp .menu .user a.ctgs {
    float: right
}
.menuresp .menu ul.resp {
    float: right
}
.menuresp .menu ul.resp li {
    float: right
}
.menuresp .menu ul.resp li a {
    float: right
}
.menuresp .menu ul.resp li a i {
    margin-left: 0;
    margin-right: 5px
}
.menuresp .menu ul.resp li ul.sub-menu {
    float: right
}
.menuresp .menu ul.resp li ul.sub-menu li a:before {
    margin-left: 0;
    margin-left: 10px
}
.page_user {
    float: right
}
.page_user header.user {
    float: right
}
.page_user header.user .box {
    float: right
}
.page_user header.user .box .gravatar {
    float: right
}
.page_user header.user .box .gravatar img {
    float: right
}
.page_user header.user .box .contenido {
    float: right;
    padding-left: 0;
    padding-right: 90px
}
.page_user header.user .box .contenido .name {
    float: right
}
.page_user header.user .box .contenido .info {
    float: left
}
.page_user header.user .box .contenido .info span {
    float: right
}
.page_user header.user .box .contenido .info span:last-child {
    border-left: 0
}
.page_user header.user .box .contenido .info span b.num {
    float: right
}
.page_user nav.user {
    float: right
}
.page_user nav.user ul li {
    float: right
}
.page_user nav.user ul li.rrt {
    float: left
}
.page_user nav.user ul li a {
    float: right
}
.page_user .content {
    float: right
}
.page_user .content .upge h2 {
    float: right
}
.page_user .content .upge h2 span.pending {
    float: left
}
.page_user .content .upge h2 span.pending i {
    float: left;
    margin-left: 0;
    margin-right: 10px
}
.user_edit_control {
    float: right
}
.user_edit_control ul li {
    float: right
}
.user_edit_control ul li a {
    float: right
}
.user_edit_control ul li a.selected:before {
    margin-left: 0;
    margin-right: -15px;
    left: auto;
    right: 50%
}
#message .sms {
    float: right
}
#message .sms .updating i {
    float: right;
    margin-right: 0;
    margin-left: 8px
}
#message .sms .error i {
    float: right;
    margin-right: 0;
    margin-left: 8px
}
#message .sms .sent i {
    float: right;
    margin-right: 0;
    margin-left: 8px
}
.page_user .content .paged {
    float: right
}
.page_user .content .paged a.load_more {
    float: right
}
#items_movies,
#items_tvshows {
    float: right
}
table.account_links {
    float: right
}
table.account_links thead th {
    text-align: right
}
table.account_links tbody td img {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
#edit_link {
    float: right
}
#edit_link .box {
    left: auto;
    right: 50%;
    margin-left: 0;
    margin-right: -200px
}
#edit_link .box .form_edit {
    float: right
}
#edit_link .box .form_edit .cerrar {
    right: auto;
    left: 0;
    float: left
}
#edit_link .box .form_edit .cerrar a {
    float: right
}
#edit_link .box .form_edit .ready {
    float: right
}
#edit_link .box .form_edit .ready i {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
#edit_link .box .form_edit fieldset {
    float: right
}
#edit_link .box .form_edit fieldset h3 {
    float: right
}
#edit_link .box .form_edit fieldset h3 i {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
.dtuser {
    float: right;
    margin-left: 0;
    margin-right: 20px
}
.dtuser .gravatar {
    float: right;
    padding-left: 0;
    padding-right: 19px;
    border-right: solid 2px rgba(255, 255, 255, 0.2);
    border-left: none
}
.dtuser .gravatar span {
    right: auto;
    left: -5px
}
.dtuser .gravatar img {
    float: right
}
.login_box {
    left: auto;
    right: 50%;
    margin-left: 0;
    margin-right: -200px
}
.login_box .box {
    float: right
}
.login_box .box a#c_loginbox {
    right: auto;
    left: 0
}
.login_box .box h3 {
    float: right
}
.login_box .box fieldset {
    float: right
}
.login_box .box label {
    float: right
}
.login_box .box input[type="checkbox"] {
    float: right
}
.login_box .box a.register {
    float: right
}
#resultado_link_form {
    float: right
}
#resultado_link_form .msg i {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
a.pteks {
    float: right
}
a#update_imdb_rating {
    float: left
}
.dt_mainmeta {
    float: right
}
.dt_mainmeta nav {
    float: right
}
.dt_mainmeta nav h2 {
    float: right
}
.dt_mainmeta nav h2:before {
    float: left
}
.dt_mainmeta nav.genres ul {
    float: right
}
.dt_mainmeta nav.genres ul li {
    float: right
}
.dt_mainmeta nav.genres ul li a:before {
    margin-right: 0;
    margin-left: 10px;
    float: right;
}
.dt_mainmeta nav.genres ul li i {
    float: left
}
.dt_mainmeta nav.releases ul {
    float: right
}
.dt_mainmeta nav.releases ul li {
    float: right
}
.dt_mainmeta nav.releases ul li a {
    float: right
}
.head-main-nav ul.main-header li ul.sub-menu li a:before {
    margin-right: 0;
    margin-left: 9px;
    float: right
}
.dtinfo .title {
    float: right
}
.dtinfo .title h4 {
    float: right;
    padding-right: 13px;
    padding-left: 50px;
    text-align: right
}
.dtinfo .title span.flags {
    padding-right: 0;
    padding-left: 10px;
    right: auto;
    left: 0
}
.dtinfo .title span.flags .flag {
    margin-left: 0;
    margin-right: 7px;
    float: right
}
.dtinfo .metadata {
    float: right
}
.dtinfo .metadata span {
    float: right
}
.dtinfo .texto {
    float: right
}
.dtinfo .genres {
    float: right
}
.dtinfo .genres .mta {
    float: right
}
.dtinfo .genres .mta a {
    float: right
}
.dtinfo .genres .mta a:last-child {
    border-left: 0
}
td.cal a {
    float: right
}
.featu {
    left: auto;
    right: 0
}
.module .content .items .item .dfeatur {
    padding-left: 0;
    padding-right: 34px
}
.module .content .items .item .dfeatur .mark {
    left: auto;
    right: 0
}
.user_control {
    float: left;
    margin-right: 0;
    margin-left: -25px
}
.user_control a {
    float: right
}
.user_control a span {
    float: left;
    margin-left: 0;
    margin-right: 10px
}
.tooltip-right {
    left: auto;
    right: 110%
}
.tooltip-right::after {
    right: auto;
    left: 100%
}
article.simple .poster .profile_control {
    left: auto;
    right: 0
}
article.simple .poster .profile_control span {
    float: right
}
article.simple .poster .profile_control span a {
    float: right
}
.requests {
    float: right
}
.requests nav {
    float: right
}
.requests nav ul {
    float: left
}
.requests nav h1 {
    float: right
}
.requests nav h1 span {
    margin-left: 0;
    margin-right: 10px
}
.requests nav a.add_request {
    float: left;
    margin-left: 0;
    margin-right: 20px
}
.requests nav ul li {
    float: right
}
.requests nav ul li a {
    float: right
}
.requests .content {
    float: right
}
.requests .content .tabox {
    float: right
}
.requests .content .items {
    float: right
}
.requests .content .items .item {
    float: right
}
.requests .content .items .item .box {
    float: right
}
.requests .content .items .item .box .poster {
    float: right
}
.requests .content .items .item .box .poster span {
    left: auto;
    right: 0
}
.requests .content .items .item .box h3 {
    float: right
}
.requests .content .items .item .box .data {
    float: right
}
.requests .content .items .item .box .data span {
    float: right
}
.requests .content .items .item .box .data span a.get_content_dbmovies {
    float: right
}
.requests .content .items .item .box .data span i.icon-plus2 {
    float: right;
    margin-left: 0;
    margin-right: 5px
}
.requests .content .items .item .box .data span i.icon-check {
    float: right;
    margin-left: 0;
    margin-right: 5px
}
.requests .discover {
    float: right
}
.requests .discover .fixbox {
    float: right
}
.requests .discover .fixbox .box {
    float: right
}
.requests .discover .fixbox .box form {
    float: right
}
.requests .discover .fixbox .box form input[type="text"] {
    float: right;
    font-family: tahoma
}
.requests .discover .fixbox .box form button.filter {
    right: auto;
    left: 0
}
.discover .resultinfo {
    float: right
}
.discover_results .metainfo {
    float: right
}
footer.main .fcmpbox {
    float: right
}
footer.main .fcmpbox .primary {
    float: right
}
footer.main .fcmpbox .primary .columenu {
    float: left
}
footer.main .fcmpbox .primary .columenu .item {
    float: right
}
footer.main .fcmpbox .primary .columenu .item ul {
    float: right
}
footer.main .fcmpbox .primary .columenu .item ul li a {
    float: right
}
footer.main .fcmpbox .primary .fotlogo {
    float: right
}
footer.main .fcmpbox .primary .fotlogo .logo {
    float: right
}
footer.main .fcmpbox .primary .fotlogo .text {
    float: right
}
span.top-page {
    float: left
}
span.top-page a {
    float: right
}
.dt-breadcrumb {
    float: right
}
.dt-breadcrumb ol li {
    float: right
}
.dt-breadcrumb ol li a {
    float: right
}
.dt-breadcrumb ol li span.icon-angle-right {
    padding-left: 0;
    padding-right: 10px;
    float: right
}
.letter_home {
    float: right
}
.letter_home ul.glossary {
    float: right
}
.letter_home ul.glossary li {
    float: right
}
.letter_home ul.glossary li a {
    float: right
}
.letter_home>.items_glossary {
    left: auto;
    right: 0
}
.items_glossary .items {
    float: right
}
.items_glossary .items .item {
    float: right
}
.items_glossary .items .item .poster {
    float: right
}
.items_glossary .items .item .poster .rating {
    right: auto;
    left: 0
}
.items_glossary .items .item .data {
    float: right
}
.items_glossary .items .item .data h3 {
    float: right
}
.items_glossary .items .item .data span {
    float: right
}
.post_request {
    left: auto;
    right: 50%;
    margin-left: 0;
    margin-right: -350px
}
.post_request .box_post {
    float: right
}
.post_request .box_post .backdrop {
    float: right
}
.post_request .box_post .backdrop span {
    right: auto;
    left: 0
}
.post_request .box_post .data {
    float: right
}
.post_request .box_post .data h3 {
    float: right
}
.post_request .box_post .data p {
    float: right
}
.post_request .box_post .load_event {
    float: right
}
@media only screen and (max-width: 768px) {
    #edit_link .box {
        left: auto;
        right: 0;
        margin-right: 0
    }
    .user_edit_control ul li a.selected:before {
        margin-left: 0;
        margin-right: -10px
    }
    .page_user header.user .box .contenido {
        padding-left: 0;
        padding-right: 60px
    }
    .ds1 .content {
        margin-right: 0
    }
    .module .content,
    .module .sidebar {
        margin-right: 0
    }
    ul.main_links li a.active {
        border-right: 0
    }
    #single .content {
        border-left: 0
    }
    #single .sidebar {
        border-right: 0;
        margin-right: 0
    }
    .nav_items_module {
        margin-left: 0;
        margin-right: 15px
    }
    .nav_items_module a.btn {
        padding-left: 10px
    }
}
@media only screen and (max-width: 667px) {
    .sheader .data {
        margin-left: 0;
        margin-right: 130px
    }
}
@media only screen and (max-width: 600px) {
    .link_sharing {
        margin-right: 0;
        left: auto;
        right: 0
    }
    .account .content {
        margin-left: 0;
        margin-right: 170px
    }
    .report-video-active {
        left: auto;
        right: 0;
        margin-right: 0
    }
}
@media only screen and (max-width: 480px) {
    .login_box {
        left: auto;
        right: 0;
        margin-right: 0
    }
    .account .content {
        margin-right: 0
    }
    .tleft {
        padding-left: 0;
        border-left: 0
    }
    .tright {
        padding-left: 0;
        padding-right: 10px
    }
    .result-item article {
        padding-left: 0;
        padding-right: 110px
    }
}
@media only screen and (max-width: 424px) {
    .dt_social_single a {
        margin-left: 0
    }
    .dt_social_single {
        left: auto;
        right: 0
    }
}
.mCustomScrollBox {
    direction: rtl
}
.mCSB_inside>.mCSB_container {
    margin-right: 0;
    margin-left: 30px
}
.mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden {
    margin-left: 0
}
.mCS-dir-rtl>.mCSB_inside>.mCSB_container {
    margin-left: 0;
    margin-right: 30px
}
.mCS-dir-rtl>.mCSB_inside>.mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden {
    margin-right: 0
}
.mCSB_scrollTools {
    right: auto;
    left: 0
}
.mCSB_outside+.mCSB_scrollTools {
    right: auto;
    left: -26px
}
.mCS-dir-rtl>.mCSB_inside>.mCSB_scrollTools,
.mCS-dir-rtl>.mCSB_outside+.mCSB_scrollTools {
    left: auto;
    right: 0
}
.mCS-dir-rtl>.mCSB_outside+.mCSB_scrollTools {
    left: auto;
    right: -26px
}
.mCSB_outside+.mCS-minimal.mCSB_scrollTools_vertical,
.mCSB_outside+.mCS-minimal-dark.mCSB_scrollTools_vertical {
    right: auto;
    left: 0;
    margin-right: 0;
    margin-left: -18px
}
.mCS-dir-rtl>.mCSB_outside+.mCS-minimal.mCSB_scrollTools_vertical,
.mCS-dir-rtl>.mCSB_outside+.mCS-minimal-dark.mCSB_scrollTools_vertical {
    right: 0;
    left: auto
}
table.dt_table_admin thead tr th {
    text-align: right
}
a.dt_activate {
    border-right: solid 1px;
    border-left: solid 1px
}
.dtloadpage {
    left: auto;
    right: 0
}
.dtloadpage .dtloadbox span {
    float: right
}
.dtloadpage .dtloadbox p {
    float: right
}
table.dt-options-table td.label {
    border-left: 1px solid
}
.mundothemes_box h2 {
    float: right
}
.mundothemes_box span.notas {
    float: right
}
img.mundothemes {
    float: left
}
#widget-list [id*='_dtw_'] .widget-top {
    border-color: rgba(0, 0.3) 0, 0,
}
#widget-list [id*='_dts_'] .widget-top {
    border-color: rgba(0, 0.3) 0, 0,
}
table.dt_table_admin tbody tr td.text_player {
    border-left: solid 1px
}
.about-wrap .changelog h2.dt_text_h2 {
    float: right
}
span.status_dt_license {
    float: right
}
table.edd_table tr td.title {
    padding-right: 0;
    padding-left: 20px
}
#widgets-home h2:before,
#widgets-home h3:before {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
#sidebar-movies h2:before,
#sidebar-tvshows h2:before,
#sidebar-seasons h2:before,
#sidebar-posts h2:before {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
#loading_api span.spinner {
    float: right;
    margin-right: 0;
    margin-left: 10px
}
.icon-angle-right:before {
    content: "\f104"
}
.fakeplayer .playbox section .controls .box i {
    float: right
}
.fakeplayer .playbox section .controls .box i.right {
    float: left
}
ul.wp-tags {
    float: right;
    padding-right: 0
}
ul.wp-tags li {
    float: right;
    margin-left: 7px
}
ul.wp-tags li a {
    float: right
}
.dtuser .gravatar .image span {
    right: auto;
    left: -5px
}
.module .content.rigth.csearch,
.module .content.right{
    border-right: 0;
    margin-right: 0;
    margin-left: 360px;
}
.module .sidebar.right{
    left: 0;
    right: unset;
}
a.report-video-error{
    float: left;
    margin-right: 0;
    margin-left: 10px;
}
@media (max-width: 800px) {
    .links_table td:nth-child(4),
    .links_table th:nth-child(4) {
        display: none
    }
}
@media only screen and (max-width: 540px) {
    .user_control {
        margin-left: 0!important
    }
}
.w_item_b .data .wextra b{
    float: right;
}
.w_item_b .data .wextra b:before{
    margin-right: 0;
    margin-left: 5px;
}
.w_item_b .data .wextra span.year{
    margin-left: 0;
    margin-right: 5px
}
