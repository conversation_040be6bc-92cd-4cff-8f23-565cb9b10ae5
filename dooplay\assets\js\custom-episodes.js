/**
 * Episode List with Inline Links
 * Provides episode links functionality
 */

jQuery(document).ready(function($) {

    // Initialize episode improvements with links
    initEpisodeWithLinks();

    function initEpisodeWithLinks() {
        // Improve season toggle with smooth animation
        $('.se-q').off('click').on('click', function(e) {
            e.preventDefault();

            const $seasonHeader = $(this);
            const $seasonContent = $seasonHeader.next('.se-a');
            const $allSeasons = $('.se-a');

            // Close all other seasons
            $allSeasons.not($seasonContent).slideUp(300);

            // Toggle current season
            if ($seasonContent.is(':visible')) {
                $seasonContent.slideUp(300);
            } else {
                $seasonContent.slideDown(300);
            }
        });

        // Links toggle functionality
        $('.links-toggle').on('click', function(e) {
            e.preventDefault();
            toggleEpisodeLinks($(this));
        });

        // Episode poster and title click to show links
        $('.episode-main-info .imagen, .episode-main-info .episodiotitle').on('click', function(e) {
            // Don't trigger if clicking on the episode title link itself
            if (!$(e.target).is('a')) {
                e.preventDefault();
                const $linksToggle = $(this).closest('.episode-with-links').find('.links-toggle');
                if ($linksToggle.length) {
                    toggleEpisodeLinks($linksToggle);
                }
            }
        });

        // Function to toggle episode links
        function toggleEpisodeLinks($toggle) {
            const $container = $toggle.next('.episode-links-container');

            if ($container.hasClass('show')) {
                $container.removeClass('show').slideUp(200);
            } else {
                // Close other open link containers
                $('.episode-links-container.show').removeClass('show').slideUp(200);
                $container.addClass('show').slideDown(200);
            }
        }

        // Add subtle hover effects
        $('#seasons .se-c .se-a ul.episodios li').hover(
            function() {
                $(this).addClass('episode-hover');
            },
            function() {
                $(this).removeClass('episode-hover');
            }
        );

        // Smooth scroll to active episode if exists
        scrollToActiveEpisode();
    }
    
    function scrollToActiveEpisode() {
        const $activeEpisode = $('.episode-with-links.active');
        if ($activeEpisode.length) {
            $('html, body').animate({
                scrollTop: $activeEpisode.offset().top - 100
            }, 800);
        }
    }

    // Global function for playing episodes
    window.playEpisode = function(streamUrl, title) {
        if (!streamUrl || streamUrl === '#') {
            alert('Stream URL not available');
            return;
        }

        // Create modal for video player
        const modalHtml = `
            <div id="episode-player-modal" class="episode-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <iframe src="${streamUrl}" width="100%" height="400" frameborder="0" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#episode-player-modal').remove();

        // Add modal to body
        $('body').append(modalHtml);

        // Show modal
        $('#episode-player-modal').fadeIn(300);

        // Close modal functionality
        $('.close-modal, #episode-player-modal').on('click', function(e) {
            if (e.target === this) {
                $('#episode-player-modal').fadeOut(300, function() {
                    $(this).remove();
                });
            }
        });

        // Prevent modal content click from closing modal
        $('.modal-content').on('click', function(e) {
            e.stopPropagation();
        });

        // ESC key to close modal
        $(document).on('keydown.episodeModal', function(e) {
            if (e.keyCode === 27) {
                $('#episode-player-modal').fadeOut(300, function() {
                    $(this).remove();
                });
                $(document).off('keydown.episodeModal');
            }
        });
    };

    // Add download tracking
    $('.download-btn').on('click', function() {
        const episodeTitle = $(this).closest('.episode-with-links').find('.episodiotitle a').text();
        console.log('Download started for:', episodeTitle);

        // You can add analytics tracking here
        if (typeof gtag !== 'undefined') {
            gtag('event', 'download', {
                'event_category': 'episode',
                'event_label': episodeTitle
            });
        }
    });
    
    function addKeyboardNavigation() {
        let currentIndex = 0;
        const $episodes = $('.episode-card');
        
        $(document).on('keydown', function(e) {
            if ($episodes.length === 0) return;
            
            switch(e.keyCode) {
                case 38: // Up arrow
                    e.preventDefault();
                    currentIndex = Math.max(0, currentIndex - 1);
                    focusEpisode(currentIndex);
                    break;
                case 40: // Down arrow
                    e.preventDefault();
                    currentIndex = Math.min($episodes.length - 1, currentIndex + 1);
                    focusEpisode(currentIndex);
                    break;
                case 13: // Enter
                    e.preventDefault();
                    $episodes.eq(currentIndex).find('.episode-title a')[0].click();
                    break;
            }
        });
        
        function focusEpisode(index) {
            $episodes.removeClass('keyboard-focus');
            const $targetEpisode = $episodes.eq(index);
            $targetEpisode.addClass('keyboard-focus');
            
            // Scroll to episode if needed
            const episodeTop = $targetEpisode.offset().top;
            const windowTop = $(window).scrollTop();
            const windowHeight = $(window).height();
            
            if (episodeTop < windowTop || episodeTop > windowTop + windowHeight - 100) {
                $('html, body').animate({
                    scrollTop: episodeTop - 100
                }, 300);
            }
        }
    }
    
    function addLazyLoading() {
        const $images = $('.episode-thumbnail img');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const $img = $(img);
                        
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            $img.addClass('loaded');
                        }
                        
                        observer.unobserve(img);
                    }
                });
            });
            
            $images.each(function() {
                imageObserver.observe(this);
            });
        }
    }
    
    function addProgressIndicator() {
        // Check for watched episodes from localStorage
        const watchedEpisodes = JSON.parse(localStorage.getItem('watchedEpisodes') || '[]');
        
        $('.episode-card').each(function() {
            const $card = $(this);
            const episodeUrl = $card.find('.episode-title a').attr('href');
            
            if (watchedEpisodes.includes(episodeUrl)) {
                $card.addClass('watched');
                $card.find('.episode-info').append('<div class="watched-indicator"><i class="fas fa-check"></i></div>');
            }
        });
        
        // Mark episode as watched when clicked
        $('.episode-card .episode-title a').on('click', function() {
            const episodeUrl = $(this).attr('href');
            let watchedEpisodes = JSON.parse(localStorage.getItem('watchedEpisodes') || '[]');
            
            if (!watchedEpisodes.includes(episodeUrl)) {
                watchedEpisodes.push(episodeUrl);
                localStorage.setItem('watchedEpisodes', JSON.stringify(watchedEpisodes));
            }
        });
    }
    
    // Add search functionality for episodes
    function addEpisodeSearch() {
        const searchHtml = `
            <div class="episode-search">
                <input type="text" placeholder="Search episodes..." class="episode-search-input">
                <i class="fas fa-search"></i>
            </div>
        `;
        
        $('#episodes h2').after(searchHtml);
        
        $('.episode-search-input').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            
            $('.episode-card').each(function() {
                const $card = $(this);
                const title = $card.find('.episode-title a').text().toLowerCase();
                const episodeNumber = $card.find('.episode-number').text().toLowerCase();
                
                if (title.includes(searchTerm) || episodeNumber.includes(searchTerm)) {
                    $card.show();
                } else {
                    $card.hide();
                }
            });
        });
    }
    
    // Initialize search if there are many episodes
    if ($('.episode-card').length > 10) {
        addEpisodeSearch();
    }
    
    // Add smooth animations on scroll
    function addScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    $(entry.target).addClass('animate-in');
                }
            });
        }, {
            threshold: 0.1
        });
        
        $('.episode-card').each(function() {
            observer.observe(this);
        });
    }
    
    if ('IntersectionObserver' in window) {
        addScrollAnimations();
    }
    
    // Add touch gestures for mobile
    if ('ontouchstart' in window) {
        let startY = 0;
        
        $('.episode-card').on('touchstart', function(e) {
            startY = e.touches[0].clientY;
        });
        
        $('.episode-card').on('touchend', function(e) {
            const endY = e.changedTouches[0].clientY;
            const diff = startY - endY;
            
            if (Math.abs(diff) > 50) {
                // Swipe detected
                if (diff > 0) {
                    // Swipe up - next episode
                    const $next = $(this).next('.episode-card');
                    if ($next.length) {
                        $next[0].scrollIntoView({ behavior: 'smooth' });
                    }
                } else {
                    // Swipe down - previous episode
                    const $prev = $(this).prev('.episode-card');
                    if ($prev.length) {
                        $prev[0].scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }
        });
    }
});
