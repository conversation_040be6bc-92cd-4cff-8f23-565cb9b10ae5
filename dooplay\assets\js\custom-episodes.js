/**
 * Enhanced Episode List JavaScript
 * Provides smooth animations and improved user experience
 */

jQuery(document).ready(function($) {
    
    // Initialize episode list enhancements
    initEpisodeList();
    
    function initEpisodeList() {
        // Add smooth toggle animation for seasons
        $('.se-q').off('click').on('click', function(e) {
            e.preventDefault();
            
            const $seasonHeader = $(this);
            const $seasonContent = $seasonHeader.next('.se-a');
            const $allSeasons = $('.se-a');
            const $allHeaders = $('.se-q');
            
            // Remove active class from all headers
            $allHeaders.removeClass('active');
            
            // Close all other seasons with smooth animation
            $allSeasons.not($seasonContent).slideUp(400, function() {
                $(this).css('display', 'none');
            });
            
            // Toggle current season
            if ($seasonContent.is(':visible')) {
                $seasonContent.slideUp(400, function() {
                    $(this).css('display', 'none');
                });
            } else {
                $seasonHeader.addClass('active');
                $seasonContent.slideDown(400, function() {
                    $(this).css('display', 'block');
                    
                    // Add stagger animation to episode cards
                    const $episodes = $seasonContent.find('.episode-card');
                    $episodes.each(function(index) {
                        $(this).css({
                            'opacity': '0',
                            'transform': 'translateY(20px)'
                        }).delay(index * 50).animate({
                            'opacity': '1'
                        }, 300).css('transform', 'translateY(0)');
                    });
                });
            }
        });
        
        // Add hover effects for episode cards
        $('.episode-card').hover(
            function() {
                $(this).addClass('hovered');
                $(this).find('.play-overlay').fadeIn(200);
            },
            function() {
                $(this).removeClass('hovered');
                $(this).find('.play-overlay').fadeOut(200);
            }
        );
        
        // Add click animation for episode cards
        $('.episode-card').on('click', function(e) {
            if (!$(e.target).is('a')) {
                const $link = $(this).find('.episode-title a');
                if ($link.length) {
                    // Add loading state
                    $(this).addClass('loading');
                    
                    // Navigate to episode
                    window.location.href = $link.attr('href');
                }
            }
        });
        
        // Add smooth scroll to active episode
        scrollToActiveEpisode();
        
        // Add keyboard navigation
        addKeyboardNavigation();
        
        // Add lazy loading for episode thumbnails
        addLazyLoading();
        
        // Add progress indicator for watched episodes
        addProgressIndicator();
    }
    
    function scrollToActiveEpisode() {
        const $activeEpisode = $('.episode-card.active');
        if ($activeEpisode.length) {
            $('html, body').animate({
                scrollTop: $activeEpisode.offset().top - 100
            }, 800);
        }
    }
    
    function addKeyboardNavigation() {
        let currentIndex = 0;
        const $episodes = $('.episode-card');
        
        $(document).on('keydown', function(e) {
            if ($episodes.length === 0) return;
            
            switch(e.keyCode) {
                case 38: // Up arrow
                    e.preventDefault();
                    currentIndex = Math.max(0, currentIndex - 1);
                    focusEpisode(currentIndex);
                    break;
                case 40: // Down arrow
                    e.preventDefault();
                    currentIndex = Math.min($episodes.length - 1, currentIndex + 1);
                    focusEpisode(currentIndex);
                    break;
                case 13: // Enter
                    e.preventDefault();
                    $episodes.eq(currentIndex).find('.episode-title a')[0].click();
                    break;
            }
        });
        
        function focusEpisode(index) {
            $episodes.removeClass('keyboard-focus');
            const $targetEpisode = $episodes.eq(index);
            $targetEpisode.addClass('keyboard-focus');
            
            // Scroll to episode if needed
            const episodeTop = $targetEpisode.offset().top;
            const windowTop = $(window).scrollTop();
            const windowHeight = $(window).height();
            
            if (episodeTop < windowTop || episodeTop > windowTop + windowHeight - 100) {
                $('html, body').animate({
                    scrollTop: episodeTop - 100
                }, 300);
            }
        }
    }
    
    function addLazyLoading() {
        const $images = $('.episode-thumbnail img');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const $img = $(img);
                        
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            $img.addClass('loaded');
                        }
                        
                        observer.unobserve(img);
                    }
                });
            });
            
            $images.each(function() {
                imageObserver.observe(this);
            });
        }
    }
    
    function addProgressIndicator() {
        // Check for watched episodes from localStorage
        const watchedEpisodes = JSON.parse(localStorage.getItem('watchedEpisodes') || '[]');
        
        $('.episode-card').each(function() {
            const $card = $(this);
            const episodeUrl = $card.find('.episode-title a').attr('href');
            
            if (watchedEpisodes.includes(episodeUrl)) {
                $card.addClass('watched');
                $card.find('.episode-info').append('<div class="watched-indicator"><i class="fas fa-check"></i></div>');
            }
        });
        
        // Mark episode as watched when clicked
        $('.episode-card .episode-title a').on('click', function() {
            const episodeUrl = $(this).attr('href');
            let watchedEpisodes = JSON.parse(localStorage.getItem('watchedEpisodes') || '[]');
            
            if (!watchedEpisodes.includes(episodeUrl)) {
                watchedEpisodes.push(episodeUrl);
                localStorage.setItem('watchedEpisodes', JSON.stringify(watchedEpisodes));
            }
        });
    }
    
    // Add search functionality for episodes
    function addEpisodeSearch() {
        const searchHtml = `
            <div class="episode-search">
                <input type="text" placeholder="Search episodes..." class="episode-search-input">
                <i class="fas fa-search"></i>
            </div>
        `;
        
        $('#episodes h2').after(searchHtml);
        
        $('.episode-search-input').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            
            $('.episode-card').each(function() {
                const $card = $(this);
                const title = $card.find('.episode-title a').text().toLowerCase();
                const episodeNumber = $card.find('.episode-number').text().toLowerCase();
                
                if (title.includes(searchTerm) || episodeNumber.includes(searchTerm)) {
                    $card.show();
                } else {
                    $card.hide();
                }
            });
        });
    }
    
    // Initialize search if there are many episodes
    if ($('.episode-card').length > 10) {
        addEpisodeSearch();
    }
    
    // Add smooth animations on scroll
    function addScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    $(entry.target).addClass('animate-in');
                }
            });
        }, {
            threshold: 0.1
        });
        
        $('.episode-card').each(function() {
            observer.observe(this);
        });
    }
    
    if ('IntersectionObserver' in window) {
        addScrollAnimations();
    }
    
    // Add touch gestures for mobile
    if ('ontouchstart' in window) {
        let startY = 0;
        
        $('.episode-card').on('touchstart', function(e) {
            startY = e.touches[0].clientY;
        });
        
        $('.episode-card').on('touchend', function(e) {
            const endY = e.changedTouches[0].clientY;
            const diff = startY - endY;
            
            if (Math.abs(diff) > 50) {
                // Swipe detected
                if (diff > 0) {
                    // Swipe up - next episode
                    const $next = $(this).next('.episode-card');
                    if ($next.length) {
                        $next[0].scrollIntoView({ behavior: 'smooth' });
                    }
                } else {
                    // Swipe down - previous episode
                    const $prev = $(this).prev('.episode-card');
                    if ($prev.length) {
                        $prev[0].scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }
        });
    }
});
