!function(n){var e,s=function(s,a){clearTimeout(e),e=setTimeout(s,a)},a=!1;function t(){var s=n("#term").val(),a=n("#page").val(),e=n("#type").val(),t=n("#nonce").val(),i=n("#action").val();n("#get_requests").find("span").addClass("fa-spinner-third").addClass("fa-spin"),n.ajax({url:dtAjax.url,type:"POST",data:{type:e,term:s,page:a,nonce:t,action:i},error:function(s){console.log(s)},success:function(s){n("#get_requests").find("span").removeClass("fa-spinner-third").removeClass("fa-spin"),n("#discover_results").html(s),n(".get_content_dbmovies").click(function(){var a,s=n(this).data("id"),e=n(this).data("type"),t=n(this).data("nonce");e=e,t=t,n("#tmdb-"+(a=s)).html('<div class="itm-exists">'+dtAjax.loading+"</div>"),n.ajax({url:dtAjax.url,type:"POST",data:{id:a,type:e,nonce:t,action:"dbmovies_post_requests"},error:function(s){console.log(s)},success:function(s){console.log(s),n("#tmdb-"+a).html('<div class="itm-exists">'+dtAjax.ready+"</div>")}})})}})}enterActive=!(e=0),n('input[name="s"]').on("input",function(){var i=this.value;s(function(){return i.length<=2?(n(dtGonza.area).hide(),void n(dtGonza.button).find("span").removeClass("fa-spinner-third").removeClass("fa-spin")):void(a||(a=!0,1==dtGonza.livesearchactive&&(n(dtGonza.button).find("span").addClass("fa-spinner-third").addClass("fa-spin"),n(dtGonza.area).find("ul").addClass("process").addClass("noselect"),n.ajax({type:"GET",url:dtGonza.api,data:"keyword="+i+"&nonce="+dtGonza.nonce,dataType:"json",success:function(s){var a,e,t;s.error?n(dtGonza.area).hide():(n(dtGonza.area).show(),e='<span class="icon-search-1">'+i+"</span>",e='<li class="ctsx"><a class="more live_search_click" data-search="searchform">'+(a=dtGonza.more.replace("%s",e))+"</a></li>",moreText2='<li class="ctsv"><a class="more live_search_click" data-search="form-search-resp">'+a+"</a></li>",t=[],n.each(s,function(s,a){name="",date="",imdb="",!1!==a.extra.date&&(date='<span class="release">('+a.extra.date+")</span>"),!1!==a.extra.imdb&&(imdb='<div class="imdb"><span class="fas fa-star"></span> '+a.extra.imdb+"</div>"),t.push('<li id="'+s+'"><a href="'+a.url+'" class="clearfix"><div class="poster"><img src="'+a.img+'" /></div><div class="title">'+a.title+date+"</div>"+imdb+"</a></li>")}),n(dtGonza.area).html("<ul>"+t.join("")+e+"</ul>"))},complete:function(){enterActive=a=!1,n(dtGonza.button).find("span").removeClass("fa-spinner-third").removeClass("fa-spin"),n(dtGonza.area).find("ul").removeClass("process").removeClass("noselect")}}))))},500)}),n(document).on("click",".live_search_click",function(){var s=n(this).data("search");0!=s&&n("#"+s).submit()}),n(document).on("keypress","#search-form",function(s){if(enterActive)return 13!=s.keyCode}),n(document).click(function(){n(event.target);0==n(event.target).closest('input[name="s"]').length?n(dtGonza.area).hide():n(dtGonza.area).show(),0==n(event.target).closest(".lglossary").length?(n(".items_glossary").hide(),n(".lglossary").removeClass("active")):n(".items_glossary").show()}),n(document).on("click",".post-request",function(){n(".post_request").show(),n("#post_request_archive").html('<div class="load_event">'+dtAjax.loading+"</div>");var s=n(this).data("id");n.ajax({url:dtAjax.url,type:"POST",data:{id:s,action:"dbmovies_post_archive"},error:function(s){console.log(s)},success:function(s){n("#post_request_archive").html(s),n(".backdrop").click(function(){n(".post_request").hide()})}})}),n("#discover_requests").keyup(function(){return s(function(){t()},500),!1}),n("#discover_requests").submit(function(){return t(),!1}),n(document).on("click",".lglossary",function(){var s=n(this).data("glossary"),a=n(this).data("type");n(".lglossary").removeClass("active"),n(this).addClass("active"),n(".items_glossary").show(),n(".items_glossary").html('<div class="onloader"></div>'),n.ajax({type:"GET",url:dtGonza.glossary,data:"term="+s+"&nonce="+dtGonza.nonce+"&type="+a,dataType:"json",success:function(s){if(s.error)return n(".items_glossary").hide(),void n(".lglossary").removeClass("active");n(".items_glossary").show();var e=[];n.each(s,function(s,a){imdb="",!1!==a.imdb&&(imdb='<div class="rating"><i class="fas fa-star"></i> '+a.imdb+"</div>"),e.push('<div id="'+s+'" class="item"><a href="'+a.url+'"><div class="poster"><img src="'+a.img+'"/>'+imdb+'</div></a><div class="data"><h3>'+a.title+"</h3><span>"+a.year+"</span></div></div>")}),n(".items_glossary").html('<div class="items animation-2">'+e.join("")+"</div>")}})}),n(document).keyup(function(s){27==s.keyCode&&(n(".post_request").hide(),n(".items_glossary").hide(),n(".items_glossary").html(" "),n(".lglossary").removeClass("active")),39==s.keyCode&&n("#nextpagination").trigger("click"),37==s.keyCode&&n("#prevpagination").trigger("click")})}(jQuery);
