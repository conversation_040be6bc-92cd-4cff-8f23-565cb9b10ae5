/**
 *
 * ---------------------------------------------------------
 * CODESTAR FRAMEWORK CSS MAP
 * ---------------------------------------------------------
 *
 * 01. Base
 *     01. 01. <PERSON>er
 *     01. 02. <PERSON><PERSON>
 *     01. 03. <PERSON><PERSON>
 *     01. 04. Navigation
 *     01. 05. Wrapper
 *     01. 06. Content
 *     01. 07. Section
 *     01. 08. Footer
 *     01. 09. Copyright
 *     01. 10. Show All Settings
 *     01. 11. Search Input
 *     01. 12. Metabox
 *     01. 13. Comment Metabox
 *     01. 14. Help Tooltip
 * 02. Themes
 *     02. 01. Theme Dark
 *     02. 02. Theme Light
 * 03. Fields
 *     03. 01. Field
 *     03. 02. Field: accordion
 *     03. 03. Field: background
 *     03. 04. Field: backup
 *     03. 05. Field: border, spacing, dimensions
 *     03. 06. Field: button_set
 *     03. 07. Field: checkbox, radio
 *     03. 08. Field: code_editor
 *     03. 09. Field: color
 *     03. 10. Field: color_group
 *     03. 11. Field: fieldset
 *     03. 12. Field: date
 *     03. 13. Field: gallery
 *     03. 14. Field: group
 *     03. 15. Field: icon
 *     03. 16. Field: image_select
 *     03. 17. Field: link_color
 *     03. 18. Field: map
 *     03. 19. Field: media
 *     03. 20. Field: palette
 *     03. 21. Field: repeater
 *     03. 22. Field: select
 *     03. 23. Field: slider
 *     03. 24. Field: sortable
 *     03. 25. Field: sorter
 *     03. 26. Field: spinner
 *     03. 27. Field: switcher
 *     03. 28. Field: tabbed
 *     03. 29. Field: text
 *     03. 30. Field: textarea
 *     03. 31. Field: typography
 *     03. 32. Field: upload
 *     03. 33. Field: wp_editor
 *     03. 34. Field: heading
 *     03. 35. Field: subheading
 *     03. 36. Field: submessage
 *     03. 37. Field: notice
 *     03. 38. Field: number
 *     03. 39. Field: link
 *     03. 40. Field: others
 * 04. Widget
 * 05. Customizer
 * 06. Taxonomy
 * 07. Profile
 * 08. Nav Menu
 * 09. Modal
 *     09. 01. Shortcode Modal
 *     09. 02. Gutenberg Modal
 *     09. 03. Icon Modal
 * 10. Helper
 * 11. Welcome Page
 * 12. Responsive
 * 13. Others
 *
 * ---------------------------------------------------------
 *
 */
/**
 * 01. Base
 */
.csf {
  position: relative;
}
.csf label {
  padding: 0;
  margin: 0;
  display: inline-block;
}

.csf-ab-icon {
  top: 2px;
}

#screen-meta-links + .csf-options {
  margin-top: 40px;
}

.csf-options {
  margin-top: 20px;
  margin-right: 20px;
}

/**
 * 01. 01. Header
 */
.csf-header {
  position: relative;
}

.csf-header-inner {
  padding: 25px;
  transition: box-shadow .3s ease;
}
.csf-header-inner h1 {
  float: left;
  font-size: 1.5em;
  line-height: 26px;
  font-weight: 400;
  margin: 0;
}
.csf-header-inner h1 small {
  font-size: 11px;
  font-weight: 500;
}

/**
 * 01. 02. Sticky
 */
.csf-sticky .csf-header-inner {
  position: fixed;
  z-index: 99;
  top: 32px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/**
 * 01. 03. Header Buttons
 */
.csf-buttons {
  float: right;
}
.csf-buttons .button {
  margin: 0 2px;
  line-height: 26px;
}
.csf-buttons .button:focus {
  outline: none !important;
  box-shadow: none !important;
}
.csf-buttons .csf-save {
  min-width: 72px;
}

.csf-header-left {
  float: left;
}

.csf-header-right {
  float: right;
}

/**
 * 01. 04. Navigation
 */
.csf-nav {
  display: block;
  position: relative;
  z-index: 10;
  float: left;
}
.csf-nav ul {
  clear: left;
  margin: 0;
  list-style-type: none;
}
.csf-nav ul li {
  margin-bottom: 0;
}
.csf-nav ul li a {
  font-size: 13px;
  position: relative;
  display: block;
  padding: 14px 12px;
  text-decoration: none;
  transition-property: color, background;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}
.csf-nav ul li a:focus {
  outline: none;
  box-shadow: none;
}
.csf-nav ul li .csf-arrow:after {
  content: "\f054";
  display: inline-block;
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 9px;
  line-height: 1;
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -4px;
}
.csf-nav ul li.csf-tab-expanded .csf-arrow:after {
  transform: rotate(90deg);
}
.csf-nav ul li.csf-tab-expanded ul {
  display: block;
}
.csf-nav ul ul {
  display: none;
  position: relative;
}
.csf-nav ul ul li a {
  font-size: 12px;
  padding: 12px 14px 12px 24px;
}
.csf-nav .csf-tab-icon {
  width: 20px;
  margin-right: 5px;
  font-size: 13px;
  text-align: center;
}
.csf-nav .csf-label-error {
  margin-left: 4px;
  vertical-align: top;
}

.csf-nav-normal {
  width: 225px;
}
.csf-nav-normal + .csf-content {
  margin-left: 225px;
}

.csf-nav-inline {
  width: 100%;
}
.csf-nav-inline ul li {
  display: inline-block;
  vertical-align: top;
}

.csf-nav-background {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 9;
  width: 225px;
}

/**
 * 01. 05. Wrapper
 */
.csf-wrapper {
  position: relative;
}

/**
 * 01. 06. Content
 */
.csf-content {
  position: relative;
  background-color: #fff;
}

/**
 * 01. 07. Section
 */
.csf-sections {
  float: left;
  width: 100%;
}

.csf-section-title {
  display: none;
  padding: 20px 30px;
  background-color: #f5f5f5;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.csf-section-title h3 {
  margin: 0;
  padding: 0;
  font-size: 13px;
  font-weight: bold;
  text-transform: uppercase;
}
.csf-section-title .csf-section-icon {
  margin-right: 5px;
}

/**
 * 01. 08. Footer
 */
.csf-footer {
  padding: 20px;
  font-size: 11px;
}

/**
 * 01. 09. Copyright
 */
.csf-copyright {
  float: left;
  margin-top: 5px;
}

/**
 * 01. 10. Show All Settings
 */
.csf-search-all .csf-nav-background,
.csf-search-all .csf-nav,
.csf-show-all .csf-nav-background,
.csf-show-all .csf-nav {
  display: none;
}
.csf-search-all .csf-content,
.csf-show-all .csf-content {
  margin-left: 0;
}
.csf-search-all .csf-section-title,
.csf-search-all .csf-section,
.csf-show-all .csf-section-title,
.csf-show-all .csf-section {
  display: block !important;
}

.csf-search-all .csf-section-title {
  display: none !important;
}

.csf-expand-all {
  float: left;
  padding: 0 8px;
  margin-right: 4px;
  z-index: 1;
  font-size: 13px;
  line-height: 30px;
  cursor: pointer;
  user-select: none;
  border-radius: 2px;
  transition: all .2s;
}
.csf-expand-all span {
  font-size: 11px;
  vertical-align: middle;
}

/**
 * 01. 11. Search Input
 */
.csf-search {
  float: left;
}
.csf-search input {
  margin: 0 2px 0 0;
  border: none;
  font-size: 12px;
  line-height: 30px;
  min-height: 30px;
  text-align: inherit;
  padding: 0 10px;
  border-radius: 2px;
  box-shadow: none;
}
.csf-search input:focus {
  box-shadow: none;
}

.csf-saving .csf-buttons,
.csf-saving .csf-content {
  cursor: default;
  pointer-events: none;
  opacity: 0.5;
}

/**
 * 01. 12. Metabox
 */
.csf-metabox {
  margin: -6px -12px -12px -12px;
}
.csf-metabox .csf-field {
  padding: 20px;
}
.csf-metabox .csf-section-title {
  padding: 20px;
}

.block-editor-page .csf-metabox {
  margin: -6px -14px -14px -14px;
}

.block-editor-editor-skeleton__content .csf-metabox {
  border-left: 1px solid #e2e4e7;
  border-right: 1px solid #e2e4e7;
}

.csf-sections-reset {
  float: left;
  width: 100%;
  text-align: right;
  border-top: 1px solid #eee;
}
.csf-sections-reset .csf-button-cancel,
.csf-sections-reset input {
  display: none;
}
.csf-sections-reset label {
  padding: 10px;
}
.csf-sections-reset span {
  -webkit-user-select: none;
  user-select: none;
}
.csf-sections-reset input:checked ~ .csf-button-reset {
  display: none;
}
.csf-sections-reset input:checked ~ .csf-button-cancel {
  display: inline-block;
}

#side-sortables .csf-section-title {
  padding: 12px;
}
#side-sortables .csf-field {
  padding: 10px 15px;
}
#side-sortables .csf-field .csf-title {
  float: none;
  width: 100%;
  margin-bottom: 6px;
}
#side-sortables .csf-field .csf-fieldset {
  float: none;
  width: 100%;
}
#side-sortables .csf-field-text input {
  width: 100%;
}
#side-sortables .csf-notice {
  padding: 10px 15px;
}

/**
 * 01. 13. Comment Metabox
 */
.csf-comment-metabox {
  margin: -6px -12px -12px -12px;
}
.csf-comment-metabox .csf-field {
  padding: 20px;
}
.csf-comment-metabox .csf-section-title {
  padding: 20px;
}

/**
 * 01. 14. Help Tooltip
 */
.csf-tooltip {
  position: absolute;
  z-index: 5000001;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  text-decoration: none;
  padding: 6px 12px;
  max-width: 200px;
  color: #fff;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
}

/**
 * 02. Themes
 */
/**
 * 02. 01. Theme Dark
 */
.csf-theme-dark .csf-header-inner {
  background-color: #050505;
}
.csf-theme-dark .csf-header-inner h1 {
  color: #fff;
}
.csf-theme-dark .csf-header-inner h1 small {
  color: #555;
}
.csf-theme-dark .csf-expand-all {
  color: #999;
  background-color: #222;
}
.csf-theme-dark .csf-expand-all:hover {
  color: #fff;
  background-color: #333;
}
.csf-theme-dark .csf-search input {
  color: #fff;
  background-color: #222;
}
.csf-theme-dark .csf-search:focus {
  background-color: #444;
}
.csf-theme-dark .csf-search::-webkit-input-placeholder {
  color: #666;
}
.csf-theme-dark .csf-nav ul li a {
  color: #999;
  border-color: #2f2f2f;
  background-color: #222;
}
.csf-theme-dark .csf-nav ul li a:hover {
  color: #fff;
}
.csf-theme-dark .csf-nav ul li .csf-active {
  color: #fff;
  background-color: #111;
}
.csf-theme-dark .csf-nav ul ul li a {
  border-color: #2f2f2f;
  background-color: #191919;
}
.csf-theme-dark .csf-nav ul ul li .csf-active {
  background-color: #101010;
}
.csf-theme-dark .csf-nav ul ul:before {
  background-color: rgba(34, 34, 34, 0.75);
}
.csf-theme-dark .csf-nav > ul > li:last-child > a {
  border: none;
}
.csf-theme-dark .csf-nav-normal ul li a {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.csf-theme-dark .csf-nav-normal ul li .csf-active:after {
  content: " ";
  position: absolute;
  right: 0;
  top: 50%;
  height: 0;
  width: 0;
  pointer-events: none;
  border: solid transparent;
  border-right-color: #fff;
  border-width: 4px;
  margin-top: -4px;
}
.csf-theme-dark .csf-nav-inline {
  background-color: #222;
}
.csf-theme-dark .csf-nav-inline ul li a {
  text-align: center;
  border-right-width: 1px;
  border-right-style: solid;
}
.csf-theme-dark .csf-nav-inline ul li .csf-active:after {
  content: " ";
  position: absolute;
  left: 50%;
  bottom: 0;
  height: 0;
  width: 0;
  pointer-events: none;
  border: solid transparent;
  border-bottom-color: #fff;
  border-width: 4px;
  margin-left: -4px;
}
.csf-theme-dark .csf-nav-background {
  background-color: #222;
}
.csf-theme-dark .csf-footer {
  color: #555;
  background-color: #050505;
}

/**
 * 02. 02. Theme Light
 */
.csf-theme-light .csf-container {
  border: 1px solid #ccd0d4;
  box-shadow: 0 0 15 rgba(0, 0, 0, 0.04);
}
.csf-theme-light .csf-header-inner {
  border-bottom: 1px solid #ccd0d4;
  background-color: #f5f5f5;
  background: linear-gradient(#fefefe, #f5f5f5);
}
.csf-theme-light .csf-header-inner h1 small {
  color: #999;
}
.csf-theme-light .csf-expand-all {
  color: #999;
  background-color: #eee;
}
.csf-theme-light .csf-expand-all:hover {
  color: #555;
}
.csf-theme-light .csf-search input {
  color: #555;
  background-color: #eee;
}
.csf-theme-light .csf-search input::-webkit-input-placeholder {
  color: #999;
}
.csf-theme-light .csf-nav ul li a {
  font-weight: 500;
  color: #444;
  background-color: #f5f5f5;
}
.csf-theme-light .csf-nav ul li a:hover {
  color: #111;
  background-color: #fff;
}
.csf-theme-light .csf-nav ul li .csf-active {
  color: #111;
  background-color: #fff;
}
.csf-theme-light .csf-nav ul ul li a {
  background-color: #eee;
}
.csf-theme-light .csf-nav-normal > ul {
  margin-right: -1px;
  margin-bottom: -1px;
}
.csf-theme-light .csf-nav-normal > ul li a {
  border-bottom: 1px solid #ccd0d4;
  border-right: 1px solid #ccd0d4;
}
.csf-theme-light .csf-nav-normal > ul li .csf-active {
  border-right-color: #fff;
}
.csf-theme-light .csf-nav-inline {
  background-color: #f5f5f5;
  border-bottom: 1px solid #ccd0d4;
}
.csf-theme-light .csf-nav-inline > ul {
  margin-bottom: -1px;
}
.csf-theme-light .csf-nav-inline > ul li a {
  text-align: center;
  border-right: 1px solid #ccd0d4;
  border-bottom: 1px solid #ccd0d4;
}
.csf-theme-light .csf-nav-inline > ul li .csf-active {
  border-bottom-color: #fff;
}
.csf-theme-light .csf-nav-inline > ul ul {
  display: none !important;
}
.csf-theme-light .csf-nav-inline .csf-arrow:after {
  display: none;
}
.csf-theme-light .csf-nav-background {
  background-color: #f5f5f5;
  border-right: 1px solid #ccd0d4;
}
.csf-theme-light .csf-footer {
  color: #555;
  border-top: 1px solid #ccd0d4;
  background-color: #f5f5f5;
  background: linear-gradient(#fafafa, #f5f5f5);
}

/**
 * 03. Fields
 */
.csf-field {
  position: relative;
  padding: 30px;
}
.csf-field + .csf-field {
  border-top: 1px solid #eee;
}
.csf-field p:first-child {
  margin-top: 0;
}
.csf-field p:last-child {
  margin-bottom: 0;
}
.csf-field:after, .csf-field:before {
  content: " ";
  display: table;
}
.csf-field:after {
  clear: both;
}
.csf-field h4 {
  margin-top: 0;
}
.csf-field .csf-title {
  position: relative;
  width: 20%;
  float: left;
}
.csf-field .csf-title h4 {
  margin: 0;
  color: #23282d;
}
.csf-field .csf-fieldset {
  float: right;
  width: calc(80% - 20px);
}

.csf-pseudo-field {
  padding: 0 5px 0 0 !important;
  display: inline-block;
}
.csf-pseudo-field + .csf-pseudo-field {
  border: 0;
}
.csf-pseudo-field pre {
  display: none;
}

/**
 * 03. 02. Field: accordion
 */
.csf-field-accordion .csf-accordion-item {
  position: relative;
  margin-bottom: 5px;
}
.csf-field-accordion .csf-accordion-item:last-child {
  margin-bottom: 0;
}
.csf-field-accordion .csf-accordion-item h4 {
  font-size: 1em;
}
.csf-field-accordion .csf-accordion-title {
  display: block;
  cursor: pointer;
  position: relative;
  margin: 0;
  padding: 15px;
  min-height: 0;
  font-size: 100%;
  user-select: none;
  border: 1px solid #ccd0d4;
  background-color: #fafafa;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  transition: border-color .15s;
}
.csf-field-accordion .csf-accordion-title:active, .csf-field-accordion .csf-accordion-title:hover, .csf-field-accordion .csf-accordion-title:focus {
  outline: none;
  border-color: #999;
}
.csf-field-accordion .csf-accordion-title .csf--icon {
  width: 20px;
  text-align: center;
  margin-right: 2px;
}
.csf-field-accordion .csf-accordion-icon {
  width: 16px;
  text-align: center;
}
.csf-field-accordion .csf-accordion-content {
  display: none;
  padding: 0;
  border: 1px solid #ccd0d4;
  border-top: none;
  background-color: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}
.csf-field-accordion .csf-accordion-content > .csf-field {
  padding: 15px;
}
.csf-field-accordion .csf-accordion-open {
  display: block;
}

/**
 * 03. 03. Field: background
 */
.csf-field-background .csf-field {
  border: 0 !important;
  padding: 0;
  margin-bottom: 6px;
  margin-right: 6px;
}
.csf-field-background .csf--title {
  color: #777;
  font-size: 12px;
}
.csf-field-background .csf--background-colors {
  display: flex;
  flex-wrap: wrap;
}
.csf-field-background .csf--background-attributes {
  display: flex;
  flex-wrap: wrap;
}
.csf-field-background .csf--background-attributes select {
  min-width: 100%;
  margin: 0;
}
.csf-field-background .csf--background-attributes .csf-field {
  flex: 1;
}
.csf-field-background .csf--attributes-hidden {
  display: none;
}

/**
 * 03. 04. Field: backup
 */
.csf-field-backup textarea {
  width: 100%;
  min-height: 200px;
  margin-bottom: 5px;
}
.csf-field-backup small {
  display: inline-block;
  margin: 5px;
}
.csf-field-backup hr {
  margin: 20px 0;
  border: none;
  border-bottom: 1px solid #e5e5e5;
}

/**
 * 03. 05. Field: border, spacing, dimensions
 */
.csf-field-border .csf--inputs,
.csf-field-spacing .csf--inputs,
.csf-field-dimensions .csf--inputs {
  float: left;
  display: flex;
  flex-wrap: wrap;
}
.csf-field-border .csf--input,
.csf-field-spacing .csf--input,
.csf-field-dimensions .csf--input {
  display: flex;
  padding-right: 6px;
  padding-bottom: 4px;
  box-sizing: border-box;
}
.csf-field-border .csf--input select,
.csf-field-spacing .csf--input select,
.csf-field-dimensions .csf--input select {
  margin: 0;
}
.csf-field-border .csf--input input,
.csf-field-spacing .csf--input input,
.csf-field-dimensions .csf--input input {
  position: relative;
  z-index: 1;
  margin: 0;
  width: 65px;
  max-width: 100%;
  text-align: center;
}
.csf-field-border .csf--color,
.csf-field-spacing .csf--color,
.csf-field-dimensions .csf--color {
  float: left;
}
.csf-field-border .csf--label,
.csf-field-spacing .csf--label,
.csf-field-dimensions .csf--label {
  display: flex;
  flex-direction: column;
  justify-content: center;
  user-select: none;
  min-width: 20px;
  max-width: 100%;
  padding: 0 4px;
  font-size: 12px;
  text-align: center;
  color: #555;
  border: 1px solid #7B776C;
  background-color: #f5f5f5;
}
.csf-field-border .csf--icon,
.csf-field-spacing .csf--icon,
.csf-field-dimensions .csf--icon {
  border-right: 0;
  border-radius: 4px 0 0 4px;
}
.csf-field-border .csf--icon + input,
.csf-field-spacing .csf--icon + input,
.csf-field-dimensions .csf--icon + input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.csf-field-border .csf--unit,
.csf-field-spacing .csf--unit,
.csf-field-dimensions .csf--unit {
  border-left: 0;
  border-radius: 0 4px 4px 0;
}
.csf-field-border .csf--is-unit,
.csf-field-spacing .csf--is-unit,
.csf-field-dimensions .csf--is-unit {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/**
 * 03. 06. Field: button_set
 */
.csf-field-button_set .csf--buttons {
  display: inline-block;
}
.csf-field-button_set .csf--button {
  position: relative;
  z-index: 1;
  float: left;
  cursor: pointer;
  padding: 7px 14px;
  min-width: 16px;
  text-align: center;
  color: #555;
  border: 1px solid #cccccc;
  background-color: #f7f7f7;
  user-select: none;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}
.csf-field-button_set .csf--button:first-child {
  border-radius: 4px 0 0 4px;
}
.csf-field-button_set .csf--button:last-child {
  border-radius: 0 4px 4px 0;
}
.csf-field-button_set .csf--button:not(:first-child) {
  margin-left: -1px;
}
.csf-field-button_set .csf--button:hover {
  background-color: #eee;
}
.csf-field-button_set .csf--active:hover,
.csf-field-button_set .csf--active {
  z-index: 2;
  color: #fff;
  border-color: #006799;
  background-color: #0085ba;
}
.csf-field-button_set input {
  display: none;
}

/**
 * 03. 07. Field: checkbox, radio
 */
.csf-field-checkbox ul,
.csf-field-radio ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  overflow-y: auto;
  max-height: 305px;
}
.csf-field-checkbox ul li,
.csf-field-radio ul li {
  margin-bottom: 6px;
}
.csf-field-checkbox ul ul,
.csf-field-radio ul ul {
  max-height: none;
}
.csf-field-checkbox ul ul li,
.csf-field-radio ul ul li {
  margin-left: 8px;
}
.csf-field-checkbox ul ul li:first-child,
.csf-field-radio ul ul li:first-child {
  margin-left: 0;
}
.csf-field-checkbox input,
.csf-field-radio input {
  margin: 0 1px;
}
.csf-field-checkbox .csf--inline-list li,
.csf-field-radio .csf--inline-list li {
  display: inline-block;
  margin-right: 15px;
}
.csf-field-checkbox .csf--text,
.csf-field-radio .csf--text {
  margin-left: 5px;
  vertical-align: middle;
}
.csf-field-checkbox .csf-checker,
.csf-field-radio .csf-checker {
  cursor: pointer;
}

/**
 * 03. 08. Field: code_editor
 */
.csf-field-code_editor .CodeMirror {
  width: 100%;
  height: 400px;
}
.csf-field-code_editor .cm-s-default {
  border: 1px solid #ccd0d4;
}
.csf-field-code_editor textarea {
  width: 100%;
  height: 400px;
}

/**
 * 03. 09. Field: color
 */
.csf-field-color > input {
  opacity: 0.75;
  width: 115px;
  max-width: 100%;
}
.csf-field-color .button.wp-picker-clear {
  padding: 0 8px;
  margin-left: 6px;
  line-height: 2.54545455;
  min-height: 30px;
}

/**
 * 03. 10. Field: color_group
 */
.csf-field-color_group .csf--left {
  float: left;
  margin-right: 10px;
  margin-bottom: 5px;
}
.csf-field-color_group .csf--title {
  color: #999;
  margin-bottom: 5px;
}

/**
 * 03. 11. Field: fieldset
 */
.csf-field-fieldset .csf-fieldset-content {
  border: 1px solid #ccd0d4;
  background-color: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}
.csf-field-fieldset .csf-fieldset-content > .csf-field {
  padding: 15px;
}
.csf-field-fieldset .csf-field-subheading {
  font-size: 13px;
}

/**
 * 03. 12. Field: date
 */
.csf-field-date input {
  margin: 0;
}
.csf-field-date .csf--to {
  margin-left: 7px;
}

.csf-datepicker-wrapper {
  margin-top: 5px;
  width: auto;
  background-color: #fff;
  z-index: 9999999 !important;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
}
.csf-datepicker-wrapper * {
  float: none;
  margin: 0;
  padding: 0;
  font-family: inherit;
  font-weight: normal;
  font-style: normal;
  text-decoration: none;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}
.csf-datepicker-wrapper .ui-widget-header,
.csf-datepicker-wrapper .ui-datepicker-header {
  color: #fff;
  background: #00a0d2;
}
.csf-datepicker-wrapper .ui-datepicker-header .ui-state-hover {
  cursor: pointer;
}
.csf-datepicker-wrapper .ui-datepicker-title {
  font-size: 14px;
  line-height: 40px;
  text-align: center;
}
.csf-datepicker-wrapper .ui-datepicker-prev,
.csf-datepicker-wrapper .ui-datepicker-next {
  position: static;
  top: auto;
  left: auto;
  right: auto;
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 12px;
  text-align: center;
  width: 41px;
  height: 40px;
  line-height: 40px;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.csf-datepicker-wrapper .ui-datepicker-next span,
.csf-datepicker-wrapper .ui-datepicker-prev span {
  display: none;
}
.csf-datepicker-wrapper .ui-datepicker-prev {
  float: left;
}
.csf-datepicker-wrapper .ui-datepicker-next {
  float: right;
}
.csf-datepicker-wrapper .ui-datepicker-prev:before {
  content: '\f053';
}
.csf-datepicker-wrapper .ui-datepicker-next:before {
  content: '\f054';
}
.csf-datepicker-wrapper .ui-datepicker-prev-hover,
.csf-datepicker-wrapper .ui-datepicker-next-hover {
  opacity: 0.75;
}
.csf-datepicker-wrapper tbody .ui-datepicker-week-col {
  background-color: #f7f7f7;
}
.csf-datepicker-wrapper .ui-datepicker-buttonpane {
  padding: 10px;
  text-align: center;
  background-color: #f7f7f7;
}
.csf-datepicker-wrapper .ui-datepicker-buttonpane button {
  cursor: pointer;
  margin: 0 5px;
  padding: 7px 14px;
  border: 1px solid #eee;
  background-color: #fff;
}
.csf-datepicker-wrapper select {
  margin: 0 4px;
}
.csf-datepicker-wrapper select option {
  color: #555;
}
.csf-datepicker-wrapper table {
  font-size: 13px;
  border-collapse: collapse;
  width: 100%;
}
.csf-datepicker-wrapper thead {
  color: #fff;
  background: #32373c;
}
.csf-datepicker-wrapper th {
  text-align: center;
  padding: 7px;
  border: 1px solid #444;
}
.csf-datepicker-wrapper td {
  text-align: center;
  border: 1px solid #f4f4f4;
}
.csf-datepicker-wrapper td.ui-datepicker-other-month {
  border: transparent;
}
.csf-datepicker-wrapper td .ui-state-default {
  color: #555;
  width: auto;
  display: block;
  padding: 6px 12px;
}
.csf-datepicker-wrapper td .ui-state-active,
.csf-datepicker-wrapper td .ui-state-hover {
  color: #fff;
  background-color: #0073aa;
}
.csf-datepicker-wrapper td.ui-state-disabled .ui-state-default {
  opacity: 0.5;
}

/**
 * 03. 13. Field: gallery
 */
.csf-field-gallery input {
  display: none;
}
.csf-field-gallery ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.csf-field-gallery ul li {
  display: inline-block;
  position: relative;
  padding: 4px;
  margin: 0 5px 10px 0;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  border-radius: 2px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08);
}
.csf-field-gallery ul li img {
  max-height: 60px;
  display: inline-block;
  vertical-align: middle;
}
.csf-field-gallery .button {
  margin-right: 5px;
  margin-bottom: 5px;
}

/**
 * 03. 14. Field: group
 */
.csf-field-group .csf-cloneable-hidden {
  display: none !important;
}
.csf-field-group .csf-cloneable-wrapper {
  position: relative;
}
.csf-field-group .csf-cloneable-item {
  display: none;
  position: relative;
  margin-bottom: 5px;
}
.csf-field-group .csf-cloneable-item h4 {
  font-size: 1em;
}
.csf-field-group .ui-accordion .csf-cloneable-item {
  display: block;
}
.csf-field-group .csf-cloneable-content {
  border: 1px solid #ccd0d4;
  background-color: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}
.csf-field-group .csf-cloneable-content > .csf-field {
  padding: 15px;
}
.csf-field-group .csf-cloneable-title {
  display: block;
  cursor: pointer;
  position: relative;
  user-select: none;
  margin: 0;
  padding: 15px 65px 15px 10px;
  min-height: 0;
  font-size: 100%;
  border: 1px solid #ccd0d4;
  background-color: #fafafa;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  transition: border-color .15s;
}
.csf-field-group .csf-cloneable-title:active, .csf-field-group .csf-cloneable-title:hover, .csf-field-group .csf-cloneable-title:focus {
  border-color: #999;
  outline: none;
}
.csf-field-group .csf-cloneable-helper {
  position: absolute;
  top: 12px;
  right: 10px;
  z-index: 1;
  font-size: 14px;
  line-height: 1em;
}
.csf-field-group .csf-cloneable-helper i {
  display: inline-block;
  cursor: pointer;
  padding: 5px;
  color: #999;
}
.csf-field-group .csf-cloneable-helper i:hover {
  color: #555;
}
.csf-field-group .csf-cloneable-content {
  padding: 0;
  border-top: 0;
}
.csf-field-group .csf-cloneable-title-prefix,
.csf-field-group .csf-cloneable-title-number {
  margin-right: 5px;
}
.csf-field-group .csf-cloneable-alert {
  display: none;
  margin-bottom: 5px;
  padding: 10px 20px;
  color: #a94442;
  border: 1px solid #ebccd1;
  background-color: #f2dede;
}
.csf-field-group .widget-placeholder {
  margin-bottom: 10px;
  border: 1px dashed #f1c40f;
  background-color: #fffae4;
}
.csf-field-group .csf-cloneable-header-icon {
  display: inline-block;
  text-align: center;
  font-size: 14px;
  width: 17px;
  text-indent: 0;
  vertical-align: text-top;
}
.csf-field-group .csf-cloneable-placeholder {
  background-color: #ddd;
  margin-top: 4px;
  width: 100px;
  height: 10px;
  font-size: 10px;
  line-height: 10px;
  display: inline-block;
  vertical-align: top;
  border-radius: 2px;
}

/**
 * 03. 15. Field: icon
 */
.csf-field-icon input {
  display: none;
}
.csf-field-icon .button {
  margin-right: 5px;
}
.csf-field-icon .csf-icon-preview i {
  display: inline-block;
  font-size: 16px;
  width: 30px;
  height: 28px;
  line-height: 28px;
  margin-right: 5px;
  text-align: center;
  vertical-align: top;
  color: #555;
  border: 1px solid #ccc;
  background-color: #f7f7f7;
  border-radius: 3px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08);
}

/**
 * 03. 16. Field: image_select
 */
.csf-field-image_select .csf--image {
  display: inline-block;
  margin: 0 5px 5px 0;
}
.csf-field-image_select .csf--inline-list .csf--image {
  display: block;
}
.csf-field-image_select figure {
  cursor: pointer;
  position: relative;
  display: inline-block;
  max-width: 100%;
  margin: 0;
  vertical-align: bottom;
  border: 2px solid transparent;
  background-color: #fff;
  user-select: none;
  transition: all .2s;
}
.csf-field-image_select figure:before {
  position: absolute;
  top: 0;
  left: 0;
  text-align: center;
  font-size: 10px;
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  content: "\f00c";
  width: 16px;
  height: 16px;
  line-height: 14px;
  opacity: 0;
  color: #fff;
  background-color: #222;
  transition: opacity .2s;
}
.csf-field-image_select .csf--active figure {
  border-color: #222;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}
.csf-field-image_select .csf--active figure:before {
  opacity: 1;
}
.csf-field-image_select img {
  max-width: 100%;
  height: auto;
  vertical-align: top;
}
.csf-field-image_select input {
  display: none;
}

/**
 * 03. 17. Field: link_color
 */
.csf-field-link_color .csf--left {
  float: left;
  margin-right: 10px;
  margin-bottom: 5px;
}
.csf-field-link_color .csf--title {
  color: #777;
  margin-bottom: 5px;
}

/**
 * 03. 18. Field: map
 */
.csf-field-map input {
  width: 100%;
}
.csf-field-map input[type="text"].ui-autocomplete-loading {
  background-position-x: calc(100% - 5px);
}
.csf-field-map .csf--map-search + .csf--map-osm-wrap {
  margin-top: 10px;
}
.csf-field-map .csf--map-osm-wrap {
  position: relative;
  padding: 5px;
  border: 1px solid #eee;
  background-color: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}
.csf-field-map .csf--map-osm {
  position: relative;
  z-index: 1;
  min-height: 250px;
}
.csf-field-map .csf--map-inputs {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.csf-field-map .csf--map-input {
  flex: 1;
}
.csf-field-map .csf--map-input:last-child {
  padding-left: 10px;
}
.csf-field-map label {
  display: block;
  color: #777;
  font-size: 12px;
  margin: 0 0 2px 0;
}

.csf-map-ui-autocomplate {
  z-index: 999999;
  border-radius: 4px;
  overflow: hidden;
}

/**
 * 03. 19. Field: media
 */
.csf-field-media .csf--placeholder {
  display: flex;
  align-items: flex-start;
}
.csf-field-media .csf--placeholder input {
  width: 100%;
  margin: 0;
}
.csf-field-media .button {
  margin-left: 5px;
}
.csf-field-media .hidden + .button {
  margin-left: 0;
}
.csf-field-media .csf--preview {
  position: relative;
}

/**
 * 03. 20. Field: palette
 */
.csf-field-palette .csf--palette {
  position: relative;
  display: inline-block;
  cursor: pointer;
  border: 2px solid #ddd;
  margin-right: 10px;
  margin-bottom: 10px;
  user-select: none;
  -webkit-user-select: none;
  transition: all .2s;
}
.csf-field-palette .csf--palette span {
  vertical-align: middle;
  display: inline-block;
  width: 22px;
  height: 60px;
  line-height: 60px;
  overflow: hidden;
  text-indent: -999px;
}
.csf-field-palette .csf--palette:before {
  position: absolute;
  top: 0;
  left: 0;
  text-align: center;
  font-size: 10px;
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  content: "\f00c";
  width: 16px;
  height: 16px;
  line-height: 14px;
  opacity: 0;
  color: #fff;
  background-color: #222;
  transition: opacity .2s;
}
.csf-field-palette .csf--active {
  border-color: #222;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}
.csf-field-palette .csf--active:before {
  opacity: 1;
}
.csf-field-palette input {
  display: none;
}

/**
 * 03. 21. Field: repeater
 */
.csf-field-repeater .csf-field-text input {
  width: 100%;
}
.csf-field-repeater .csf-repeater-hidden {
  display: none !important;
}
.csf-field-repeater .csf-repeater-wrapper .csf-repeater-item {
  display: table;
  width: 100%;
  margin-bottom: 5px;
  border: 1px solid #eee;
}
.csf-field-repeater .csf-repeater-wrapper .csf-repeater-item h4 {
  font-size: 1em;
}
.csf-field-repeater .csf-repeater-content {
  width: 100%;
  display: table-cell;
  vertical-align: middle;
  background-color: #fff;
}
.csf-field-repeater .csf-repeater-content > .csf-field {
  padding: 15px;
}
.csf-field-repeater .csf-repeater-helper {
  width: 100%;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  font-size: 14px;
  line-height: 1em;
  border-left: 1px solid #eee;
  background-color: #f7f7f7;
}
.csf-field-repeater .csf-repeater-helper i {
  display: inline-block;
  cursor: pointer;
  color: #999;
  padding: 5px;
}
.csf-field-repeater .csf-repeater-helper i:hover {
  color: #555;
}
.csf-field-repeater .csf-repeater-helper-inner {
  width: 75px;
}
.csf-field-repeater .csf-repeater-alert {
  display: none;
  margin-bottom: 5px;
  padding: 10px 20px;
  color: #a94442;
  border: 1px solid #ebccd1;
  background-color: #f2dede;
}
.csf-field-repeater .widget-placeholder {
  height: 50px;
  margin-bottom: 3px;
  border: 1px dashed #f1c40f;
  background-color: #fffae4;
}
.csf-field-repeater .ui-sortable-helper {
  height: 50px !important;
  overflow: hidden !important;
  border-color: #ccc !important;
  background-color: #eee !important;
  opacity: 0.5;
}
.csf-field-repeater .ui-sortable-helper .csf-repeater-helper,
.csf-field-repeater .ui-sortable-helper .csf-repeater-content {
  display: none;
}

/**
 * 03. 22. Field: select
 */
.csf-field-select .csf-fieldset {
  min-height: 30px;
}
.csf-field-select .csf-chosen {
  display: none;
}
.csf-field-select select {
  max-width: 100%;
  margin: 0;
}

/**
 * 03. 23. Field: slider
 */
.csf-field-slider .csf--wrap {
  display: flex;
  align-items: center;
}
.csf-field-slider .csf--input {
  display: flex;
}
.csf-field-slider .csf--unit {
  display: flex;
  justify-content: center;
  flex-direction: column;
  user-select: none;
  padding: 0 6px;
  font-size: 11px;
  line-height: 1;
  border-radius: 0 4px 4px 0;
  color: #555;
  border: 1px solid #7e8993;
  border-left: 0;
  background-color: #f5f5f5;
}
.csf-field-slider .csf-slider-ui {
  margin-right: 15px;
}
.csf-field-slider input[type=number] {
  position: relative;
  z-index: 1;
  margin: 0;
  width: 50px;
  text-align: center;
}
.csf-field-slider .csf--is-unit {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.csf-field-slider .ui-slider {
  position: relative;
  width: 100%;
  height: 3px;
  border: none;
  background: #ddd;
  border-radius: 2px;
}
.csf-field-slider .ui-slider-range {
  height: 3px;
  border: none;
  background: #333;
  border-radius: 2px;
}
.csf-field-slider .ui-slider-handle {
  position: absolute;
  width: 16px;
  height: 16px;
  top: -7px;
  margin-left: -8px;
  border: none;
  background: #333;
  border-radius: 2px;
}
.csf-field-slider .ui-state-active,
.csf-field-slider .ui-slider-handle:hover {
  cursor: pointer;
  background: #111;
}

/**
 * 03. 24. Field: sortable
 */
.csf-field-sortable .csf-field-text input {
  width: 100%;
  max-width: 100%;
}
.csf-field-sortable .csf-sortable .csf-sortable-item {
  display: table;
  width: 100%;
  margin-bottom: 5px;
  border: 1px solid #eee;
}
.csf-field-sortable .csf-sortable .csf-sortable-item h4 {
  font-size: 1em;
}
.csf-field-sortable .csf-sortable-content {
  width: 100%;
  display: table-cell;
  vertical-align: middle;
  background-color: #fff;
}
.csf-field-sortable .csf-sortable-content > .csf-field {
  padding: 15px;
}
.csf-field-sortable .csf-sortable-helper {
  width: 100%;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  font-size: 14px;
  line-height: 1em;
  border-left: 1px solid #eee;
  background-color: #f7f7f7;
}
.csf-field-sortable .csf-sortable-helper i {
  display: inline-block;
  cursor: pointer;
  width: 50px;
  color: #555;
}
.csf-field-sortable .csf-sortable-helper i:hover {
  opacity: 0.5;
}
.csf-field-sortable .widget-placeholder {
  height: 50px;
  margin-bottom: 3px;
  border: 1px dashed #f1c40f;
  background-color: #fffae4;
}
.csf-field-sortable .ui-sortable-helper {
  height: 50px !important;
  overflow: hidden !important;
  border-color: #ccc !important;
  background-color: #eee !important;
  opacity: 0.5;
}
.csf-field-sortable .ui-sortable-helper .csf-sortable-helper,
.csf-field-sortable .ui-sortable-helper .csf-sortable-content {
  display: none;
}

/**
 * 03. 25. Field: sorter
 */
.csf-field-sorter .ui-sortable-placeholder {
  height: 20px;
  border: 1px dashed #f1c40f;
  background-color: #fffae4;
}
.csf-field-sorter .csf-modules {
  float: left;
  width: 50%;
  box-sizing: border-box;
}
.csf-field-sorter .csf-modules:first-child {
  padding-right: 15px;
}
.csf-field-sorter .csf-modules:last-child {
  padding-left: 15px;
}
.csf-field-sorter .csf-disabled,
.csf-field-sorter .csf-enabled {
  padding: 5px 15px;
  border: 1px dashed #ddd;
  background-color: #fff;
}
.csf-field-sorter .csf-disabled li {
  opacity: 0.5;
  transition: opacity .15s;
}
.csf-field-sorter .csf-disabled .ui-sortable-helper {
  opacity: 1;
}
.csf-field-sorter .csf-sorter-title {
  font-size: 13px;
  font-weight: 600;
  padding: 10px;
  text-align: center;
  border: 1px dashed #ddd;
  border-bottom: none;
  background-color: #f8f8f8;
  text-transform: uppercase;
}
.csf-field-sorter ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  min-height: 62px;
}
.csf-field-sorter ul li {
  margin: 10px 0;
  padding: 10px 15px;
  cursor: move;
  font-weight: bold;
  text-align: center;
  border: 1px solid #e5e5e5;
  background-color: #fafafa;
  transition: border-color .15s;
}
.csf-field-sorter ul li:hover {
  border-color: #bbb;
}

/**
 * 03. 26. Field: spinner
 */
.csf-field-spinner .csf--spin {
  display: flex;
}
.csf-field-spinner .ui-spinner {
  display: flex;
}
.csf-field-spinner .ui-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  min-width: 20px;
  padding: 0 4px;
  color: #555;
  border: 1px solid #7e8993;
  background-color: #f5f5f5;
}
.csf-field-spinner .ui-spinner-button {
  cursor: pointer;
}
.csf-field-spinner .ui-spinner-button:hover {
  background-color: #e7e7e7;
}
.csf-field-spinner .ui-spinner-button:active {
  background-color: #ddd;
}
.csf-field-spinner .ui-spinner-button:before {
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 14px;
  line-height: 14px;
}
.csf-field-spinner .ui-spinner-down {
  order: 1;
  border-right: 0;
  border-radius: 4px 0 0 4px;
}
.csf-field-spinner .ui-spinner-down:before {
  content: "\f0d9";
}
.csf-field-spinner .ui-spinner-input {
  order: 2;
}
.csf-field-spinner .csf--unit {
  order: 3;
  border-left: 0;
  user-select: none;
}
.csf-field-spinner .ui-spinner-up {
  order: 4;
  border-left: 0;
  border-radius: 0 4px 4px 0;
}
.csf-field-spinner .ui-spinner-up:before {
  content: "\f0da";
}
.csf-field-spinner input {
  position: relative;
  z-index: 1;
  width: 50px;
  text-align: center;
  margin: 0;
  padding: 0 8px;
  border-radius: 0;
}
.csf-field-spinner .ui-button-text,
.csf-field-spinner .ui-button-icon,
.csf-field-spinner .ui-button-icon-space {
  display: none;
}

/**
 * 03. 27. Field: switcher
 */
.csf-field-switcher .csf--switcher {
  float: left;
  cursor: pointer;
  position: relative;
  width: 60px;
  height: 26px;
  padding: 0;
  margin: 0;
  overflow: hidden;
  border-radius: 4px;
  background-color: #ed6f6f;
  user-select: none;
  -webkit-user-select: none;
}
.csf-field-switcher .csf--ball {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 24px;
  height: 18px;
  background-color: #fff;
  border-radius: 4px;
  transition: all .1s;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.15);
}
.csf-field-switcher .csf--on,
.csf-field-switcher .csf--off {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  font-size: 11px;
  line-height: 26px;
  font-weight: 500;
  font-style: normal;
  text-align: center;
  text-transform: uppercase;
  color: #fff;
  padding-right: 28px;
  opacity: 0;
  transition: all .1s;
}
.csf-field-switcher .csf--off {
  padding-right: 0;
  padding-left: 28px;
  opacity: 1;
}
.csf-field-switcher .csf--active {
  background: #4fb845;
}
.csf-field-switcher .csf--active .csf--on {
  opacity: 1;
}
.csf-field-switcher .csf--active .csf--off {
  opacity: 0;
}
.csf-field-switcher .csf--active .csf--ball {
  left: 100%;
  margin-left: -28px;
}
.csf-field-switcher .csf--label {
  float: left;
  margin-top: 4px;
  margin-left: 8px;
  font-weight: 400;
  color: #999;
}
.csf-field-switcher input {
  display: none;
}

/**
 * 03. 28. Field: tabbed
 */
.csf-field-tabbed .csf-tabbed-content {
  border: 1px solid #ccd0d4;
  background-color: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}
.csf-field-tabbed .csf-tabbed-content > .csf-field {
  padding: 15px;
}
.csf-field-tabbed .csf-tabbed-nav .csf--icon {
  padding-right: 5px;
}
.csf-field-tabbed .csf-tabbed-nav a {
  display: inline-block;
  padding: 12px 15px;
  margin-top: 1px;
  margin-right: 5px;
  margin-bottom: -1px;
  position: relative;
  text-decoration: none;
  color: #444;
  font-weight: 600;
  border: 1px solid #ccd0d4;
  background-color: #f3f3f3;
  transition: all .2s;
}
.csf-field-tabbed .csf-tabbed-nav a:hover {
  background-color: #f9f9f9;
}
.csf-field-tabbed .csf-tabbed-nav a.csf-tabbed-active {
  background-color: #fff;
  border-bottom-color: #fff;
}
.csf-field-tabbed .csf-tabbed-nav a:focus {
  outline: none;
  box-shadow: none;
}

/**
 * 03. 29. Field: text
 */
.csf-field-text input {
  width: 50%;
  max-width: 100%;
  margin: 0;
}

/**
 * 03. 30. Field: textarea
 */
.csf-field-textarea textarea {
  width: 100%;
  max-width: 100%;
  min-height: 125px;
}
.csf-field-textarea .csf-shortcode-button {
  margin-bottom: 10px;
  margin-right: 5px;
}

/**
 * 03. 31. Field: typography
 */
.csf-field-typography textarea,
.csf-field-typography select {
  margin: 0;
  min-width: 100%;
  max-width: 100%;
}
.csf-field-typography .csf--title {
  color: #777;
  margin: 0 0 2px 0;
  font-size: 12px;
}
.csf-field-typography .csf--title small {
  vertical-align: top;
}
.csf-field-typography .csf--blocks {
  display: flex;
  flex-wrap: wrap;
}
.csf-field-typography .csf--block {
  flex: 1;
  max-width: 100%;
  padding-right: 6px;
  padding-bottom: 6px;
}
.csf-field-typography .csf--input {
  margin: 0;
  min-width: 100%;
}
.csf-field-typography .csf--input-wrap {
  position: relative;
}
.csf-field-typography .csf--unit {
  position: absolute;
  z-index: 1;
  right: 4px;
  top: 4px;
  bottom: 4px;
  padding: 2px 6px;
  color: #666;
  font-size: 11px;
  line-height: 1;
  border-radius: 2px;
  background: #eee;
  user-select: none;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.csf-field-typography .csf--preview {
  font-size: 16px;
  line-height: 20px;
  padding: 20px;
  color: #222;
  border: 1px solid #eee;
  background-color: #fff;
  border-radius: 2.5px;
  user-select: none;
  -webkit-user-select: none;
  transition: background-color .2s, border-color .2s;
}
.csf-field-typography .csf--block-preview {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  max-width: 100%;
}
.csf-field-typography .csf--black-background {
  border-color: #000;
  background-color: #000;
}
.csf-field-typography .csf--toggle {
  position: absolute;
  top: 5px;
  right: 10px;
  color: #999;
}
.csf-field-typography .csf--block-extra-styles {
  margin-top: 5px;
}

/**
 * 03. 32. Field: upload
 */
.csf-field-upload input {
  width: 100%;
  margin: 0;
}
.csf-field-upload .csf--wrap {
  display: flex;
  align-items: flex-start;
}
.csf-field-upload .button {
  margin-left: 5px;
}
.csf-field-upload .csf--preview {
  position: relative;
}

/**
 * 03. 33. Field: wp_editor
 */
.csf-field-wp_editor .csf-wp-editor {
  float: left;
  width: 100%;
}
.csf-field-wp_editor .mce-toolbar-grp {
  border: none;
}
.csf-field-wp_editor .mce-btn.mce-active button,
.csf-field-wp_editor .mce-btn.mce-active:hover button,
.csf-field-wp_editor .mce-btn.mce-active i,
.csf-field-wp_editor .mce-btn.mce-active:hover i {
  color: #23282d;
}
.csf-field-wp_editor .wp-media-buttons {
  position: relative;
  z-index: 2;
}
.csf-field-wp_editor .wp-editor-tabs {
  position: relative;
  z-index: 1;
}
.csf-field-wp_editor .csf-no-tinymce {
  border: 1px solid #e5e5e5;
}
.csf-field-wp_editor .csf-no-quicktags .wp-media-buttons {
  float: none;
  display: block;
}
.csf-field-wp_editor .csf-no-quicktags .mce-tinymce {
  box-shadow: none;
  border: 1px solid #e5e5e5;
}
.csf-field-wp_editor textarea {
  width: 100%;
  max-width: 100%;
  margin: 0;
  box-shadow: none;
}

/**
 * 03. 34. Field: heading
 */
.csf-field-heading {
  font-size: 1.5em;
  font-weight: bold;
  color: #23282d;
  background-color: #f5f5f5;
}

/**
 * 03. 35. Field: subheading
 */
.csf-field-subheading {
  font-size: 14px;
  font-weight: bold;
  padding-top: 17px;
  padding-bottom: 17px;
  color: #23282d;
  background-color: #f7f7f7;
}

/**
 * 03. 36. Field: submessage
 */
.csf-field-submessage {
  padding: 0 !important;
  border: 0 !important;
}
.csf-field-submessage + .csf-field {
  border-top: 0 !important;
}

.csf-submessage {
  font-size: 12px;
  padding: 17px 30px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
}

.csf-submessage-success {
  color: #3c763d;
  border-color: #d6e9c6;
  background-color: #dff0d8;
}

.csf-submessage-info {
  color: #31708f;
  border-color: #bce8f1;
  background-color: #d9edf7;
}

.csf-submessage-warning {
  color: #8a6d3b;
  border-color: #faebcc;
  background-color: #fcf8e3;
}

.csf-submessage-danger {
  color: #a94442;
  border-color: #ebccd1;
  background-color: #f2dede;
}

.csf-submessage-normal {
  color: #23282d;
  border-color: #eee;
  background-color: #f7f7f7;
}

/**
 * 03. 37. Field: notice
 */
.csf-field-notice {
  background-color: #f7f7f7;
}

.csf-notice {
  padding: 12px;
  background-color: #fff;
  border-left-style: solid;
  border-left-width: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.csf-notice-success {
  border-color: #46b450;
}

.csf-notice-info {
  border-color: #339fd4;
}

.csf-notice-warning {
  border-color: #ffbc00;
}

.csf-notice-danger {
  border-color: #dc3232;
}

.csf-notice-normal {
  border-color: #222;
}

/**
 * 03. 38. Field: number
 */
.csf-field-number input {
  width: 100%;
  margin: 0;
}
.csf-field-number .csf--wrap {
  position: relative;
  float: left;
  width: 100px;
}
.csf-field-number .csf--unit {
  position: absolute;
  z-index: 1;
  right: 4px;
  top: 4px;
  bottom: 4px;
  padding: 2px 6px;
  color: #666;
  font-size: 11px;
  line-height: 1;
  border-radius: 2px;
  background: #eee;
  user-select: none;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

/**
 * 03. 39. Field: link
 */
.csf-field-link input {
  display: none;
}
.csf-field-link .csf--result {
  display: inline-block;
  font-size: 12px;
  line-height: 16px;
  padding: 7px 10px;
  margin-bottom: 7px;
  color: #777;
  border: 1px solid #e5e5e5;
  background-color: #f5f5f5;
  border-radius: 2px;
  world-break: break-word;
}
.csf-field-link .csf--wrap {
  position: relative;
  float: left;
  width: 100px;
}
.csf-field-link .csf--unit {
  position: absolute;
  z-index: 1;
  right: 4px;
  top: 4px;
  bottom: 4px;
  padding: 2px 6px;
  color: #666;
  font-size: 11px;
  line-height: 1;
  border-radius: 2px;
  background: #eee;
  user-select: none;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

/**
 * 03. 40. others
 */
.csf-help {
  cursor: help;
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  font-size: 13px;
  color: #aaa;
}
.csf-help .csf-help-text {
  display: none;
}

.csf-image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding: 6px;
  width: 120px;
  height: 90px;
  max-width: 100%;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 2px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08);
}
.csf-image-preview img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.csf-image-preview a {
  position: absolute;
  z-index: 1;
  right: 4px;
  top: 4px;
  font-size: 14px;
  width: 22px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  text-decoration: none;
  color: #fff;
  background-color: #dd3333;
  opacity: 0.75;
  border-radius: 2px;
  transition: all .2s;
}
.csf-image-preview a:hover {
  opacity: 1;
}
.csf-image-preview a:focus {
  box-shadow: none;
}

.csf-field-custom .csf-field {
  padding: 0;
}

.csf-field .chosen-container-single .chosen-single {
  height: 28px;
  line-height: 26px;
}
.csf-field .chosen-container-single .chosen-single abbr {
  top: 0;
  right: 20px;
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 12px;
  height: 100%;
  width: 18px;
  color: #aaa;
  text-align: center;
  background: none;
}
.csf-field .chosen-container-single .chosen-single abbr:before {
  content: "\f00d";
}
.csf-field .chosen-container-single .chosen-single abbr:hover {
  color: #555;
}
.csf-field .chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 12px;
  height: 100%;
  width: 18px;
  color: #aaa;
  text-align: center;
  background: none;
}
.csf-field .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:before {
  content: "\f00d";
  display: inline-block;
  padding-top: 3px;
}
.csf-field .chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
  color: #555;
}
.csf-field .chosen-container-single .chosen-single div b {
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 14px;
  color: #aaa;
  background: none;
}
.csf-field .chosen-container-single .chosen-single div b:before {
  content: "\f107";
}
.csf-field .chosen-container-single .chosen-single div b:hover {
  color: #555;
}
.csf-field .chosen-container-multi .chosen-choices li.search-choice-placeholder {
  border: 1px dashed #aaa;
  margin: 3px 5px 3px 0;
}
.csf-field .chosen-container-multi .ui-sortable li.search-choice span {
  cursor: move;
}
.csf-field .chosen-container-active.chosen-with-drop .chosen-single div b:before {
  content: "\f106";
}
.csf-field .chosen-container-single .chosen-single-with-deselect span {
  margin-right: 40px;
}
.csf-field .chosen-container-single .chosen-search input[type="text"] {
  background: none;
}
.csf-field .chosen-container-single .chosen-search:before {
  font-family: "Font Awesome 5 Pro", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  font-size: 11px;
  content: "\f002";
  position: absolute;
  right: 12px;
  top: 10px;
  color: #aaa;
}
.csf-field .wp-picker-container {
  display: inline-block;
}
.csf-field .wp-picker-container .wp-color-result.button {
  margin-bottom: 0;
}
.csf-field .csf--transparent-wrap {
  display: none;
  position: relative;
  top: -1px;
  width: 235px;
  padding: 9px 10px;
  border: 1px solid #dfdfdf;
  border-top: none;
  background-color: #fff;
}
.csf-field .wp-picker-active .csf--transparent-wrap {
  display: block;
}
.csf-field .csf--transparent-slider {
  position: absolute;
  width: 190px;
  margin-left: 2px;
  height: 18px;
}
.csf-field .csf--transparent-slider .ui-slider-handle {
  position: absolute;
  top: -3px;
  bottom: -3px;
  z-index: 5;
  border-color: #aaa;
  border-style: solid;
  border-width: 4px 3px;
  width: 10px;
  height: 16px;
  margin: 0 -5px;
  background: none;
  cursor: ew-resize;
  opacity: 0.9;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.csf-field .csf--transparent-slider .ui-slider-handle:before {
  content: " ";
  position: absolute;
  left: -2px;
  right: -2px;
  top: -3px;
  bottom: -3px;
  border: 2px solid #fff;
  border-radius: 3px;
}
.csf-field .csf--transparent-offset {
  height: 18px;
  width: 200px;
  background: url(../images/checkerboard.png) repeat-y center left scroll #fff;
  border-radius: 2px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.4);
}
.csf-field .csf--transparent-text {
  position: absolute;
  top: 12px;
  right: 10px;
  width: 30px;
  font-size: 12px;
  line-height: 12px;
  text-align: center;
  color: #999;
}
.csf-field .csf--transparent-button {
  cursor: pointer;
  user-select: none;
  margin-top: 10px;
  font-size: 11px;
  text-align: center;
  border-radius: 2px;
  padding: 3px 7px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  letter-spacing: 0.2px;
  color: #777;
  border: 1px solid #ccc;
  background-color: #f7f7f7;
  transition: background-color .2s, border-color .2s, color .2s;
}
.csf-field .csf--transparent-active .wp-color-result {
  background-image: url(../images/checkerboard.png);
  background-size: 135px;
  background-position: center left;
  background-color: transparent !important;
}
.csf-field .csf--transparent-active .csf--transparent-button {
  color: #fff;
  border-color: #3ea032;
  background-color: #4fb845;
}
.csf-field .csf--transparent-active .fa:before {
  content: "\f205";
}

/**
 * 04. Widget
 */
.csf-widgets > .csf-field {
  position: relative;
  top: -1px;
  margin-right: -15px;
  margin-left: -15px;
  padding: 12px 15px;
}
.csf-widgets > .csf-field .csf-field {
  margin-left: 0;
  margin-right: 0;
}
.csf-widgets > .csf-field .csf-title {
  float: none;
  width: 100%;
  margin-bottom: 5px;
}
.csf-widgets > .csf-field .csf-fieldset {
  float: none;
  width: 100%;
}
.csf-widgets .csf-field-text input {
  width: 100%;
}
.csf-widgets .csf-field-notice .csf-notice {
  padding: 15px;
}

.control-section .csf-widgets > .csf-field {
  margin-right: -10px;
  margin-left: -10px;
  padding: 10px 12px;
}

/**
 * 05. Widget
 */
.control-section .csf-field {
  padding: 0;
}
.control-section .csf-field .csf-title {
  float: none;
  width: 100%;
  margin-bottom: 6px;
}
.control-section .csf-field .csf-title h4 {
  display: block;
  font-size: 13px;
  line-height: 1;
  font-weight: 600;
  color: inherit;
}
.control-section .csf-field .csf-fieldset {
  float: none;
  width: 100%;
}
.control-section .csf-help {
  top: -5px;
  right: -5px;
}
.control-section .csf-field-select select {
  width: 100%;
}
.control-section .csf-field-heading {
  color: inherit;
  font-size: 14px;
  line-height: 1em;
  margin-right: -15px;
  margin-left: -15px;
  padding: 15px;
}
.control-section .csf-field-subheading {
  color: inherit;
  font-size: 11px;
  margin-right: -15px;
  margin-left: -15px;
  padding: 10px 15px;
}
.control-section .csf-subtitle-text {
  margin-top: 4px;
  font-size: 12px;
}
.control-section .csf-field-submessage .csf-submessage {
  margin-right: -15px;
  margin-left: -15px;
  padding: 15px;
}
.control-section .csf-fieldset .csf-field-submessage .csf-submessage,
.control-section .csf-fieldset .csf-field-heading,
.control-section .csf-fieldset .csf-field-subheading {
  margin-left: 0;
  margin-right: 0;
}
.control-section .csf-field-date .csf--to {
  margin-left: 0;
}
.control-section .csf-field-sorter ul li {
  padding: 5px;
}
.control-section .csf-field-sorter .csf-modules {
  float: none;
  width: 100%;
}
.control-section .csf-field-sorter .csf-modules:first-child {
  padding-right: 0;
  padding-bottom: 15px;
}
.control-section .csf-field-background .csf--background-attributes {
  flex-direction: column;
}
.control-section .csf-field-spacing input {
  width: 90px;
}
.control-section .csf-field-border .csf--input {
  flex: 1 50%;
}
.control-section .csf-field-border input,
.control-section .csf-field-border select {
  width: 100%;
}
.control-section .csf-field-spinner input {
  width: 50px;
}
.control-section .csf-field-number .csf--wrap {
  width: 100%;
}
.control-section .csf-field-backup .csf-export-data {
  display: none;
}
.control-section .csf-field-fieldset .csf-fieldset-content {
  border-color: #e5e5e5;
}
.control-section .csf-tabbed-content > .csf-field,
.control-section .csf-sortable-content > .csf-field,
.control-section .csf-repeater-content > .csf-field,
.control-section .csf-fieldset-content > .csf-field,
.control-section .csf-cloneable-content > .csf-field,
.control-section .csf-accordion-content > .csf-field {
  padding: 10px;
}
.control-section .csf-tabbed-content > .csf-field .csf-title,
.control-section .csf-sortable-content > .csf-field .csf-title,
.control-section .csf-repeater-content > .csf-field .csf-title,
.control-section .csf-fieldset-content > .csf-field .csf-title,
.control-section .csf-cloneable-content > .csf-field .csf-title,
.control-section .csf-accordion-content > .csf-field .csf-title {
  margin-bottom: 5px;
}
.control-section .csf-tabbed-content > .csf-field h4,
.control-section .csf-sortable-content > .csf-field h4,
.control-section .csf-repeater-content > .csf-field h4,
.control-section .csf-fieldset-content > .csf-field h4,
.control-section .csf-cloneable-content > .csf-field h4,
.control-section .csf-accordion-content > .csf-field h4 {
  font-size: 12px;
}
.control-section .csf-depend-hidden.csf-depend-on {
  display: none !important;
}
.control-section .csf-depend-visible.csf-depend-on {
  border-top: 0 !important;
}

/**
 * 06. Taxonomy
 */
.csf-taxonomy {
  max-width: 95%;
}
.csf-taxonomy > .csf-field {
  border-top: none !important;
}
.csf-taxonomy > .csf-field-heading {
  font-size: 1.1em;
  padding: 20px !important;
  border: 1px solid #ddd;
}
.csf-taxonomy > .csf-field-subheading {
  font-size: 12px;
  padding: 15px !important;
  border: 1px solid #ddd;
}
.csf-taxonomy > .csf-field-submessage .csf-submessage {
  padding: 15px;
  border-left-width: 1px;
  border-left-style: solid;
  border-right-width: 1px;
  border-right-style: solid;
}
.csf-taxonomy > .csf-field-notice {
  background-color: transparent;
}
.csf-taxonomy .csf-section-title {
  display: block;
  padding: 15px;
  background-color: #f9f9f9;
  border: 1px solid #e5e5e5;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.csf-taxonomy-add-fields > .csf-field {
  padding: 8px 0;
}
.csf-taxonomy-add-fields > .csf-field > .csf-title {
  float: none;
  width: 100%;
  padding: 2px 2px 4px 0;
}
.csf-taxonomy-add-fields > .csf-field > .csf-title h4 {
  font-size: 12px;
  font-weight: normal;
}
.csf-taxonomy-add-fields > .csf-field > .csf-fieldset {
  float: none;
  width: 100%;
}
.csf-taxonomy-add-fields > .csf-field > .csf-fieldset > .csf-help {
  right: -5px;
}
.csf-taxonomy-add-fields + p.submit {
  margin-top: 0;
}

.csf-taxonomy-edit-fields > .csf-field {
  padding: 20px 0;
}
.csf-taxonomy-edit-fields > .csf-field > .csf-title {
  width: 200px;
}
.csf-taxonomy-edit-fields > .csf-field > .csf-title h4 {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  display: inline-block;
  vertical-align: middle;
}
.csf-taxonomy-edit-fields > .csf-field > .csf-fieldset {
  width: calc(100% - 220px);
}
.csf-taxonomy-edit-fields > .csf-field > .csf-fieldset > .csf-help {
  top: -5px;
  right: -5px;
}
.csf-taxonomy-edit-fields > .csf-field-submessage {
  margin: 20px 0;
}
.csf-taxonomy-edit-fields > .csf-field-subheading,
.csf-taxonomy-edit-fields > .csf-field-heading {
  margin: 20px 0;
  border: 1px solid #ddd;
}

/**
 * 08. Nav Menu
 */
.csf-nav-menu-options {
  clear: both;
  float: left;
  width: 100%;
}
.csf-nav-menu-options > .csf-fields {
  margin-left: -10px;
  margin-top: 10px;
  margin-bottom: 10px;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.csf-nav-menu-options > .csf-fields > .csf-field {
  padding: 12px 14px 12px 12px;
}
.csf-nav-menu-options > .csf-fields > .csf-field .csf-title {
  float: none;
  width: 100%;
  margin-bottom: 5px;
}
.csf-nav-menu-options > .csf-fields > .csf-field .csf-fieldset {
  float: none;
  width: 100%;
}
.csf-nav-menu-options .csf-field-text input {
  width: 100%;
}
.csf-nav-menu-options .csf-field-notice .csf-notice {
  padding: 15px;
}

.csf-nav-menu-title {
  padding: 12px 14px 12px 12px;
  background-color: #f5f5f5;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.csf-nav-menu-title:first-child {
  border-top: 0;
}
.csf-nav-menu-title h4 {
  margin: 0;
  padding: 0;
  color: #23282d;
}

.csf-nav-menu-icon {
  margin-right: 5px;
}

/**
 * 06. Profile
 */
.csf-profile-options > h2 > .fa {
  padding-right: 7px;
}
.csf-profile-options > .csf-field {
  max-width: 750px;
  padding: 15px 0;
  border-top: none !important;
}
.csf-profile-options > .csf-field > .csf-title {
  width: 200px;
}
.csf-profile-options > .csf-field > .csf-title h4 {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  display: inline-block;
  vertical-align: middle;
}
.csf-profile-options > .csf-field > .csf-fieldset {
  width: calc(100% - 220px);
}
.csf-profile-options > .csf-field > .csf-fieldset > .csf-help {
  top: -15px;
  right: -5px;
}
.csf-profile-options > .csf-field-heading {
  font-size: 1.1em;
}
.csf-profile-options > .csf-field-subheading {
  font-size: 12px;
}
.csf-profile-options > .csf-field-subheading,
.csf-profile-options > .csf-field-heading {
  margin: 10px 0;
  padding: 15px !important;
  border: 1px solid #ddd;
}
.csf-profile-options > .csf-field-submessage {
  margin: 20px 0;
}
.csf-profile-options > .csf-field-submessage .csf-submessage {
  padding: 10px;
  border-left-width: 1px;
  border-left-style: solid;
  border-right-width: 1px;
  border-right-style: solid;
}
.csf-profile-options > .csf-field-notice {
  background-color: transparent;
}

/**
 * 09. Modal
 */
.csf-modal {
  position: fixed;
  z-index: 100101;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.csf-modal.hidden {
  display: none;
}

.csf-modal-icon {
  z-index: 100102;
}

.csf-modal-table {
  display: table;
  width: 100%;
  height: 100%;
}

.csf-modal-table-cell {
  display: table-cell;
  vertical-align: middle;
  margin: 100px 0;
}

.csf-modal-inner {
  position: relative;
  z-index: 10;
  width: 760px;
  height: 750px;
  margin: 0 auto;
  background-color: #fff;
}

.csf-modal-content {
  position: relative;
  overflow: hidden;
  overflow-y: auto;
  height: 595px;
}
.csf-modal-content .csf-shortcode-button {
  display: none;
}
.csf-modal-content .csf-field {
  padding: 15px 30px 15px 15px;
}
.csf-modal-content a:active, .csf-modal-content a:focus {
  outline: none;
  box-shadow: none;
}
.csf-modal-content h4 {
  font-size: 13px;
}
.csf-modal-content h4 small {
  font-style: italic;
  font-weight: 400;
  color: #aaa;
}

.csf-modal-title {
  position: relative;
  background-color: #fcfcfc;
  border-bottom: 1px solid #ddd;
  height: 36px;
  font-size: 16px;
  font-weight: 600;
  line-height: 36px;
  margin: 0;
  padding: 0 36px 0 16px;
}

.csf-modal-header {
  width: 100%;
  padding: 14px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}
.csf-modal-header select {
  display: block;
  width: 250px;
  margin: 0 auto;
  font-size: 13px;
  line-height: 1;
  height: 30px;
  min-height: 30px;
  background-color: #fff;
}

.csf-modal-close {
  color: #666;
  padding: 0;
  position: absolute;
  top: 0;
  right: 0;
  width: 36px;
  height: 36px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
}
.csf-modal-close:before {
  font: normal 20px/36px dashicons;
  content: "\f158";
  vertical-align: top;
  width: 36px;
  height: 36px;
}
.csf-modal-close:hover {
  opacity: 0.5;
}

.csf-modal-insert-wrapper {
  text-align: center;
  width: 100%;
  padding: 15px 0;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
}

.csf-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.5;
}

/**
 * 09. 01. Shortcode Modal
 */
.csf--repeatable {
  padding: 15px 15px 0 15px;
}

.csf--repeat-button-block {
  text-align: center;
  padding-bottom: 15px;
}

.csf--repeat-shortcode {
  position: relative;
  margin-bottom: 15px;
  border: 1px dashed #ddd;
}
.csf--repeat-shortcode:first-child .csf-repeat-remove {
  display: none;
}
.csf--repeat-shortcode .csf-repeat-remove {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10;
  cursor: pointer;
  display: inline-block;
  font-size: 11px;
  width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 2px;
  color: #fff;
  background-color: #e14d43;
  opacity: 0.5;
}
.csf--repeat-shortcode .csf-repeat-remove:hover {
  opacity: 0.5;
}

.csf-shortcode-single .csf-modal-inner {
  height: 750px;
}
.csf-shortcode-single .csf-modal-content {
  height: 652px;
}

.elementor-editor-active .csf-shortcode-button {
  margin-left: 5px;
}
.elementor-editor-active .csf-modal .hidden {
  display: none !important;
}

/**
 * 09. 02. Gutenberg Modal
 */
.csf-shortcode-block {
  text-align: center;
  padding: 14px;
  font-size: 13px;
  background-color: #f5f5f5;
  font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
}

.csf-shortcode-block .components-button {
  margin-bottom: 10px;
}

/**
 * 09. 03. Icon Modal
 */
.csf-modal-icon .csf-icon-title {
  padding: 15px 0;
  margin: 4px;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  border: 1px solid #eee;
  background-color: #f7f7f7;
}
.csf-modal-icon .csf-modal-header {
  text-align: center;
}
.csf-modal-icon .csf-icon-search {
  width: 50%;
  height: 40px;
  line-height: 40px;
}
.csf-modal-icon i {
  cursor: pointer;
  display: inline-block;
  margin: 4px;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 16px;
  color: #555;
  text-align: center;
  border: 1px solid #ccc;
  background-color: #f7f7f7;
  border-radius: 2px;
  box-shadow: 1px 1px 0 rgba(0, 0, 0, 0.05);
}
.csf-modal-icon i:hover {
  color: #fff;
  border-color: #222;
  background-color: #222;
}
.csf-modal-icon .csf-modal-content {
  padding: 10px;
  height: 618px;
}
.csf-modal-icon .csf-error-text {
  padding: 10px;
}

.csf-modal-loading {
  display: none;
  position: absolute;
  left: 15px;
  top: 15px;
}

.csf-loading {
  position: relative;
  width: 20px;
  height: 20px;
  background: #ccc;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.07);
}
.csf-loading:after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  content: "";
  margin-top: -2px;
  margin-left: -2px;
  background-color: white;
  animation-duration: 0.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  animation-name: csfLoader;
  border-radius: 4px;
}

@keyframes csfLoader {
  0% {
    transform: rotate(0deg) translateX(-6px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(-6px) rotate(-360deg);
  }
}
/**
 * 10. Helper
 */
.csf-subtitle-text {
  margin-top: 6px;
  font-weight: 400;
  color: #999;
}

.csf-desc-text {
  clear: both;
  float: left;
  width: 100%;
  margin-top: 6px;
  font-weight: 400;
  color: #999;
}

.csf-error-text {
  margin-top: 6px;
  color: #d02c21;
}

.csf-before-text {
  margin-bottom: 6px;
}

.csf-after-text {
  margin-top: 6px;
}

.csf-metabox-hide {
  display: none !important;
}

.csf-metabox-show {
  display: block !important;
}

.csf-depend-hidden.csf-depend-on {
  display: none;
}

.csf-depend-visible.csf-depend-on {
  display: block;
  opacity: 0.75;
  filter: grayscale(1);
  user-select: none;
  border-top: 1px solid #eee;
}
.csf-depend-visible.csf-depend-on .clear:before {
  content: "";
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  background-color: #eee;
  opacity: 0.25;
  z-index: 10;
}

.csf-warning-primary {
  color: #fff !important;
  border-color: #dc3545 !important;
  background: #dc3545 !important;
}
.csf-warning-primary:hover, .csf-warning-primary:focus {
  border-color: #bd2130 !important;
  background: #bd2130 !important;
}
.csf-warning-primary:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #bd2130 !important;
}
.csf-warning-primary:active {
  border-color: #bd2130 !important;
  background: #bd2130 !important;
}

.csf-form-result {
  display: none;
  float: left;
  padding: 0 8px;
  margin-right: 4px;
  font-size: 11px;
  line-height: 30px;
  user-select: none;
  border-radius: 2px;
}

.csf-form-show {
  display: block;
}

.csf-form-success {
  color: #fff;
  background-color: #46b450;
}

.csf-form-warning {
  color: #8a6d3b;
  background-color: #faebcc;
}

.csf-label-error {
  position: relative;
  top: -2px;
  display: inline-block;
  font-size: 10px;
  line-height: 10px;
  height: 10px;
  width: 10px;
  padding: 1px;
  font-style: normal;
  text-align: center;
  color: #fff;
  vertical-align: middle;
  background-color: #e10000;
  border-radius: 2px;
}

.csf-no-option {
  padding: 30px;
}

.csf-input-number {
  -moz-appearance: textfield;
}

.csf-input-number::-webkit-inner-spin-button,
.csf-input-number::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.csf-fa5-shims .csf .fas,
.csf-fa5-shims .csf .far,
.csf-fa5-shims .csf .fab {
  font-family: "FontAwesome";
  font-style: normal;
}

/**
 * 11. Welcome Page
 */
.csf-welcome-wrap {
  position: relative;
  margin: 25px 40px 0 20px;
  font-size: 15px;
  max-width: 1200px;
}
.csf-welcome-wrap p {
  font-size: 14px;
  line-height: 1.5;
}
.csf-welcome-wrap h1 {
  margin: 0.2em 200px 0 0;
  padding: 0;
  color: #32373c;
  line-height: 1.2em;
  font-size: 2.8em;
  font-weight: 400;
}
.csf-welcome-wrap .csf-logo {
  position: absolute;
  overflow: hidden;
  top: 0;
  right: 0;
  height: 160px;
  width: 140px;
  background-image: linear-gradient(45deg, #2d67cb, #ad19f3);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25), inset 0 0 0 4px rgba(0, 0, 0, 0.25);
}
.csf-welcome-wrap .csf-logo .csf--effects i {
  position: absolute;
  width: 200px;
  height: 100px;
  background-color: rgba(0, 0, 0, 0.15);
  transform: rotate(-45deg);
}
.csf-welcome-wrap .csf-logo .csf--effects i:nth-child(1) {
  bottom: -20px;
  right: -70px;
}
.csf-welcome-wrap .csf-logo .csf--effects i:nth-child(2) {
  bottom: -35px;
  right: -80px;
}
.csf-welcome-wrap .csf-logo .csf--effects i:nth-child(3) {
  bottom: -50px;
  right: -90px;
}
.csf-welcome-wrap .csf-logo .csf--effects i:nth-child(4) {
  bottom: -65px;
  right: -100px;
}
.csf-welcome-wrap .csf-logo .csf--wp-logos {
  position: relative;
  padding-top: 25px;
  text-align: center;
}
.csf-welcome-wrap .csf-logo .csf--wp-logo {
  position: absolute;
  left: 20px;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url(../images/wp-logo.svg);
}
.csf-welcome-wrap .csf-logo .csf--wp-plugin-logo {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 3px solid #fff;
  background-size: 40px;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url(../images/wp-plugin-logo.svg);
  border-radius: 100%;
  vertical-align: middle;
}
.csf-welcome-wrap .csf-logo .csf--text {
  position: absolute;
  left: 0;
  right: 0;
  top: 90px;
  color: #fff;
  font-size: 13px;
  line-height: 1.2em;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.25);
}
.csf-welcome-wrap .csf-logo .csf--version {
  top: auto;
  left: auto;
  right: 8px;
  bottom: 4px;
  font-size: 11px;
  text-transform: lowercase;
}
.csf-welcome-wrap .csf-about-text {
  font-weight: 400;
  line-height: 1.6em;
  font-size: 19px;
  margin: 1em 200px 1em 0;
  color: #555d66;
}
.csf-welcome-wrap .csf-demo-button {
  margin: 1em 200px 2em 0;
}
.csf-welcome-wrap .nav-tab-wrapper {
  margin-bottom: 20px;
}
.csf-welcome-wrap ul {
  list-style-type: disc;
  padding-left: 15px;
}
.csf-welcome-wrap .csf--col {
  float: left;
  padding-right: 20px;
  box-sizing: border-box;
}
.csf-welcome-wrap .csf--col-2 {
  width: 50%;
}
.csf-welcome-wrap .csf--col-3 {
  width: 33.333%;
}
.csf-welcome-wrap .csf--col-4 {
  width: 25%;
}
.csf-welcome-wrap .csf--col-5 {
  width: 20%;
}
.csf-welcome-wrap .csf--col-last {
  padding-right: 0;
}
.csf-welcome-wrap .csf--col-upgrade {
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #e5e5e5;
}

.csf--table-compare thead td,
.csf--table-compare tfoot td {
  text-align: center;
}
.csf--table-compare td {
  font-size: 14px;
  text-align: center;
  vertical-align: middle;
  padding: 10px;
}
.csf--table-compare td:first-child {
  text-align: left;
}
.csf--table-compare tfoot td {
  padding: 15px 0;
}
.csf--table-compare .fa {
  font-size: 18px;
}
.csf--table-compare .fa-check-circle {
  color: #46b450;
}
.csf--table-compare .fa-times-circle {
  color: #dc3232;
}

.csf-welcome-cols {
  clear: both;
  margin: 20px 0;
  background-color: #fff;
  padding: 0 0;
  border-radius: 2px;
  border: 1px solid #e5e5e5;
}
.csf-welcome-cols .csf--col {
  width: 33.333%;
  float: left;
  padding: 20px;
  text-align: center;
  box-sizing: border-box;
  min-height: 200px;
  border-right: 1px solid #e5e5e5;
}
.csf-welcome-cols .csf--left,
.csf-welcome-cols .csf--block {
  float: left;
  width: 20%;
  padding: 0 30px;
  text-align: center;
  box-sizing: border-box;
}
.csf-welcome-cols .csf--block {
  width: 80%;
}
.csf-welcome-cols .csf--col-first {
  border-bottom: 1px solid #e5e5e5;
}
.csf-welcome-cols .csf--last {
  border-right: none;
}
.csf-welcome-cols .csf--space {
  height: 20px;
}
.csf-welcome-cols .csf--icon {
  display: inline-block;
  font-size: 20px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  margin-bottom: 10px;
  color: #fff;
  background-color: #555;
  border-radius: 30px;
}
.csf-welcome-cols .csf--active {
  background-color: #5cb85c;
}
.csf-welcome-cols .csf--deactive {
  background-color: #e14d43;
}
.csf-welcome-cols .csf--title {
  font-weight: bold;
  display: block;
}
.csf-welcome-cols p:last-child {
  margin-bottom: 0;
}

.csf-features-cols .csf--key-features {
  width: 30%;
}
.csf-features-cols .csf--available-fields {
  width: 70%;
}

.csf-code-block {
  margin: 20px 0;
  padding: 5px 20px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
}
.csf-code-block pre {
  font-size: 13px;
  color: #0073aa;
}
.csf-code-block pre span {
  color: #999;
}

.csf--table-fields td {
  font-size: 14px;
}

.csf--upgrade a {
  color: #5cb85c;
  font-weight: bold;
}
.csf--upgrade a:focus, .csf--upgrade a:hover {
  color: #4aa14a;
  outline: none;
  box-shadow: none;
}

@media only screen and (max-width: 782px) {
  .csf-welcome-cols .csf--col {
    width: 100%;
    min-height: auto;
    border-right: none;
    border-bottom: 1px solid #e5e5e5;
  }

  .csf-features-cols .csf--key-features {
    width: 100%;
  }
  .csf-features-cols .csf--available-fields {
    width: 100%;
  }
}
/**
 * 12. Responsive
 */
@media only screen and (max-width: 1200px) {
  .csf-metabox .csf-field .csf-title {
    float: none;
    width: 100%;
    margin-bottom: 10px;
  }
  .csf-metabox .csf-field .csf-fieldset {
    float: none;
    width: 100%;
  }
}
@media only screen and (max-width: 782px) {
  .csf-header-inner {
    text-align: center;
  }
  .csf-header-inner h1 {
    width: 100%;
    margin-bottom: 10px;
  }

  .csf-form-result {
    float: none;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .csf-search,
  .csf-header-right,
  .csf-header-left {
    width: 100%;
  }

  .csf-search {
    text-align: center;
    margin-bottom: 15px;
  }

  .csf-footer {
    text-align: center;
  }

  .csf-buttons {
    float: none;
  }

  .csf-copyright {
    float: none;
    margin-top: 10px;
  }

  .csf-nav,
  .csf-expand-all,
  .csf-reset-section,
  .csf-nav-background {
    display: none !important;
  }

  .csf-nav-normal + .csf-content {
    margin-left: 0;
  }

  .csf-section-title,
  .csf-section {
    display: block !important;
  }

  .csf-field .csf-title {
    float: none;
    width: 100%;
    margin-bottom: 10px;
  }
  .csf-field .csf-fieldset {
    float: none;
    width: 100%;
  }

  .csf-field-color .button.wp-picker-clear {
    padding: 0 8px;
    line-height: 2.14285714;
    min-height: 32px;
  }

  .csf-modal-inner {
    width: 350px;
    height: 400px;
  }

  .csf-modal-content {
    height: 237px;
  }

  .csf-icon-dialog .csf-modal-inner {
    width: 305px;
    height: 380px;
  }
  .csf-icon-dialog .csf-modal-content {
    height: 267px;
  }

  .csf-modal-icon .csf-modal-inner {
    width: 330px;
    height: 385px;
  }
  .csf-modal-icon .csf-modal-content {
    height: 252px;
  }

  .csf-profile-options > .csf-field > .csf-title,
  .csf-taxonomy-edit-fields > .csf-field > .csf-title {
    float: none;
    width: 100%;
    margin-bottom: 10px;
  }
  .csf-profile-options > .csf-field > .csf-fieldset,
  .csf-taxonomy-edit-fields > .csf-field > .csf-fieldset {
    float: none;
    width: 100%;
  }

  .csf-nav-menu-options > .csf-fields {
    margin-left: -10px;
    margin-right: -10px;
  }
  .csf-nav-menu-options > .csf-fields > .csf-field {
    padding: 10px;
  }
}
/**
 * Chosen JS Styles
 */
.chosen-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 13px;
  user-select: none;
}

.chosen-container * {
  box-sizing: border-box;
}

.chosen-container .chosen-drop {
  position: absolute;
  top: 100%;
  z-index: 1010;
  width: 100%;
  border: 1px solid #aaa;
  border-top: 0;
  background: #fff;
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  clip: rect(0, 0, 0, 0);
  clip-path: inset(100% 100%);
}

.chosen-container.chosen-with-drop .chosen-drop {
  clip: auto;
  clip-path: none;
}

.chosen-container a {
  cursor: pointer;
}

.chosen-container .search-choice .group-name, .chosen-container .chosen-single .group-name {
  margin-right: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: normal;
  color: #999999;
}

.chosen-container .search-choice .group-name:after, .chosen-container .chosen-single .group-name:after {
  content: ":";
  padding-left: 2px;
  vertical-align: top;
}

.chosen-container-single .chosen-single {
  position: relative;
  display: block;
  overflow: hidden;
  padding: 0 0 0 8px;
  height: 25px;
  border: 1px solid #aaa;
  border-radius: 5px;
  background-color: #fff;
  background: linear-gradient(#fff 20%, #f6f6f6 50%, #eee 52%, #f4f4f4 100%);
  background-clip: padding-box;
  box-shadow: 0 0 3px #fff inset, 0 1px 1px rgba(0, 0, 0, 0.1);
  color: #444;
  text-decoration: none;
  white-space: nowrap;
  line-height: 24px;
}

.chosen-container-single .chosen-default {
  color: #999;
}

.chosen-container-single .chosen-single span {
  display: block;
  overflow: hidden;
  margin-right: 26px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chosen-container-single .chosen-single-with-deselect span {
  margin-right: 38px;
}

.chosen-container-single .chosen-single abbr {
  position: absolute;
  top: 6px;
  right: 26px;
  display: block;
  width: 12px;
  height: 12px;
  font-size: 1px;
}

.chosen-container-single .chosen-single div {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 18px;
  height: 100%;
}

.chosen-container-single .chosen-single div b {
  display: block;
  width: 100%;
  height: 100%;
}

.chosen-container-single .chosen-search {
  position: relative;
  z-index: 1010;
  margin: 0;
  padding: 3px 4px;
  white-space: nowrap;
}

.chosen-container-single .chosen-search input[type="text"] {
  margin: 1px 0;
  padding: 4px 20px 4px 5px;
  width: 100%;
  height: auto;
  outline: 0;
  border: 1px solid #aaa;
  font-size: 1em;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0;
}

.chosen-container-single .chosen-drop {
  margin-top: -1px;
  border-radius: 0 0 4px 4px;
  background-clip: padding-box;
}

.chosen-container-single.chosen-container-single-nosearch .chosen-search {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  clip-path: inset(100% 100%);
}

.chosen-container .chosen-results {
  color: #444;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0 4px 4px 0;
  padding: 0 0 0 4px;
  max-height: 240px;
  -webkit-overflow-scrolling: touch;
}

.chosen-container .chosen-results li {
  display: none;
  margin: 0;
  padding: 5px 6px;
  list-style: none;
  line-height: 15px;
  word-wrap: break-word;
  -webkit-touch-callout: none;
}

.chosen-container .chosen-results li.active-result {
  display: list-item;
  cursor: pointer;
}

.chosen-container .chosen-results li.disabled-result {
  display: list-item;
  color: #ccc;
  cursor: default;
}

.chosen-container .chosen-results li.highlighted {
  background-color: #3875d7;
  background-image: linear-gradient(#3875d7 20%, #2a62bc 90%);
  color: #fff;
}

.chosen-container .chosen-results li.no-results {
  color: #777;
  display: list-item;
  background: #f4f4f4;
}

.chosen-container .chosen-results li.group-result {
  display: list-item;
  font-weight: bold;
  cursor: default;
}

.chosen-container .chosen-results li.group-option {
  padding-left: 15px;
}

.chosen-container .chosen-results li em {
  font-style: normal;
  text-decoration: underline;
}

.chosen-container-multi .chosen-choices {
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0 5px;
  width: 100%;
  height: auto;
  border: 1px solid #aaa;
  background-color: #fff;
  background-image: linear-gradient(#eee 1%, #fff 15%);
  cursor: text;
}

.chosen-container-multi .chosen-choices li {
  float: left;
  list-style: none;
}

.chosen-container-multi .chosen-choices li.search-field {
  margin: 0;
  padding: 0;
  white-space: nowrap;
}

.chosen-container-multi .chosen-choices li.search-field input[type="text"] {
  margin: 1px 0;
  padding: 0;
  outline: 0;
  border: 0 !important;
  background: transparent !important;
  box-shadow: none;
  color: #999;
  font-size: 100%;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0;
  width: 25px;
  height: 25px;
  min-height: 25px;
}

.chosen-container-multi .chosen-choices li.search-choice {
  position: relative;
  margin: 3px 5px 3px 0;
  padding: 3px 20px 3px 6px;
  border: 1px solid #aaa;
  max-width: 100%;
  border-radius: 3px;
  background-color: #eeeeee;
  background-image: linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
  background-size: 100% 19px;
  background-repeat: repeat-x;
  background-clip: padding-box;
  box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  color: #333;
  line-height: 13px;
  cursor: default;
}

.chosen-container-multi .chosen-choices li.search-choice span {
  word-wrap: break-word;
  white-space: nowrap;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 12px;
  height: 12px;
  font-size: 1px;
}

.chosen-container-multi .chosen-choices li.search-choice-disabled {
  padding-right: 5px;
  border: 1px solid #ccc;
  background-color: #e4e4e4;
  background-image: linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
  color: #666;
}

.chosen-container-multi .chosen-choices li.search-choice-focus {
  background: #d4d4d4;
}

.chosen-container-multi .chosen-results {
  margin: 0;
  padding: 0;
}

.chosen-container-multi .chosen-drop .result-selected {
  display: list-item;
  color: #ccc;
  cursor: default;
}

.chosen-container-active .chosen-single {
  border: 1px solid #5897fb;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.chosen-container-active.chosen-with-drop .chosen-single {
  border: 1px solid #aaa;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  background-image: linear-gradient(#eee 20%, #fff 80%);
  box-shadow: 0 1px 0 #fff inset;
}

.chosen-container-active.chosen-with-drop .chosen-single div {
  border-left: none;
  background: transparent;
}

.chosen-container-active .chosen-choices {
  border: 1px solid #5897fb;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.chosen-container-active .chosen-choices li.search-field input[type="text"] {
  color: #222 !important;
}

.chosen-disabled {
  opacity: 0.5 !important;
  cursor: default;
}

.chosen-disabled .chosen-single {
  cursor: default;
}

.chosen-disabled .chosen-choices .search-choice .search-choice-close {
  cursor: default;
}

.chosen-rtl {
  text-align: right;
}

.chosen-rtl .chosen-single {
  overflow: visible;
  padding: 0 8px 0 0;
}

.chosen-rtl .chosen-single span {
  margin-right: 0;
  margin-left: 26px;
  direction: rtl;
}

.chosen-rtl .chosen-single-with-deselect span {
  margin-left: 38px;
}

.chosen-rtl .chosen-single div {
  right: auto;
  left: 3px;
}

.chosen-rtl .chosen-single abbr {
  right: auto;
  left: 26px;
}

.chosen-rtl .chosen-choices li {
  float: right;
}

.chosen-rtl .chosen-choices li.search-field input[type="text"] {
  direction: rtl;
}

.chosen-rtl .chosen-choices li.search-choice {
  margin: 3px 5px 3px 0;
  padding: 3px 5px 3px 19px;
}

.chosen-rtl .chosen-choices li.search-choice .search-choice-close {
  right: auto;
  left: 4px;
}

.chosen-rtl.chosen-container-single .chosen-results {
  margin: 0 0 4px 4px;
  padding: 0 4px 0 0;
}

.chosen-rtl .chosen-results li.group-option {
  padding-right: 15px;
  padding-left: 0;
}

.chosen-rtl.chosen-container-active.chosen-with-drop .chosen-single div {
  border-right: none;
}

.chosen-rtl .chosen-search input[type="text"] {
  padding: 4px 5px 4px 20px;
  direction: rtl;
}
