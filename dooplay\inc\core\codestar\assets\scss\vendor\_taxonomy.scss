/**
 * 06. Taxonomy
 */
.csf-taxonomy{
  max-width: 95%;

  > .csf-field{
    border-top: none !important;
  }

  > .csf-field-heading{
    font-size: 1.1em;
    padding: 20px !important;
    border: 1px solid #ddd;
  }

  > .csf-field-subheading{
    font-size: 12px;
    padding: 15px !important;
    border: 1px solid #ddd;
  }

  > .csf-field-submessage{

    .csf-submessage{
      padding: 15px;
      border-left-width: 1px;
      border-left-style: solid;
      border-right-width: 1px;
      border-right-style: solid;
    }
  }

  > .csf-field-notice{
    background-color: transparent;
  }

  .csf-section-title{
    display: block;
    padding: 15px ;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
  }
}

.csf-taxonomy-add-fields{

  > .csf-field{
    padding: 8px 0;

    > .csf-title{
      float: none;
      width: 100%;
      padding: 2px 2px 4px 0;

      h4{
        font-size: 12px;
        font-weight: normal;
      }
    }

    > .csf-fieldset{
      float: none;
      width: 100%;

      > .csf-help{
        right: -5px;
      }
    }
  }

  + p.submit{
    margin-top: 0;
  }
}

.csf-taxonomy-edit-fields{

  > .csf-field{
    padding: 20px 0;

    > .csf-title{
      width: 200px;

      h4{
        font-size: 14px;
        font-weight: 600;
        line-height: 1.3;
        display: inline-block;
        vertical-align: middle;
      }
    }

    > .csf-fieldset{
      width: calc(100% - 220px);

      > .csf-help{
        top: -5px;
        right: -5px;
      }
    }
  }

  > .csf-field-submessage{
    margin: 20px 0;
  }

  > .csf-field-subheading,
  > .csf-field-heading{
    margin: 20px 0;
    border: 1px solid #ddd;
  }
}
