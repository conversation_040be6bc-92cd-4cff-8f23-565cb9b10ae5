!function(t){t(function(){1==t("#dooplay-reCAPTCHA-response").length&&Auth.recaptcha&&t.getScript("https://www.google.com/recaptcha/api.js?render="+Auth.recaptcha,function(){grecaptcha.ready(function(){grecaptcha.execute(Auth.recaptcha,{action:"dooplay_authorize"}).then(function(o){t("#dooplay-reCAPTCHA-response").html('<input type="hidden" name="google-recaptcha-token" value="'+o+'">')})})}),t(document).on("submit","#dooplay_login_user",function(){var e=t("#dooplay_login_btn").data("btntext");return t(".form_dt_user").removeClass("shake"),t("#dooplay_login_btn").prop("disabled",!0),t("#dooplay_login_btn").val(Auth.loading),t.ajax({type:"POST",url:Auth.url,dataType:"json",data:t(this).serialize(),success:function(o){1==o.response?(t("#jsonresponse").html('<p class="success">'+o.message+"</p>"),t("#dooplay_login_btn").val(Auth.wait),setTimeout(function(){o.redirect?window.location.replace(o.redirect):location.reload()},1500)):(t(".form_dt_user").addClass("shake"),t("#dooplay_login_btn").val(e),t("#dooplay_login_btn").prop("disabled",!1),t("#jsonresponse").html('<p class="error">'+o.message+"</p>"))}}),!1}),t(document).on("submit","#dooplay_sign_up",function(){t("#username").val();return password=t("#password").val(),btntexto=t("#dooplay_signup_btn").data("btntext"),t("#dooplay_signup_btn").prop("disabled",!0),t("#dooplay_signup_btn").val(Auth.loading),t(".form_dt_user").removeClass("shake"),t("#jsonresponse").html(""),t.ajax({type:"POST",url:Auth.url,dataType:"json",data:t(this).serialize(),success:function(o){t("#dooplay_sign_up input").prop("disabled",!0),1==o.response?(t("#jsonresponse").html('<p class="success">'+o.message+"</p>"),t("#dooplay_signup_btn").val(Auth.wait),setTimeout(function(){window.location.replace(o.redirect)},1500)):(t("#jsonresponse").html('<p class="error">'+o.message+"</p>"),t(".form_dt_user").addClass("shake"),t("#dooplay_sign_up input").prop("disabled",!1),t("#dooplay_signup_btn").val(btntexto))}}),!1})})}(jQuery);
