!function(n){"use strict";var l={init:function(t){return this.each(function(){this.self=n(this),l.destroy.call(this.self),this.opt=n.extend(!0,{},n.fn.raty.defaults,t),l._adjustCallback.call(this),l._adjustNumber.call(this),l._adjustHints.call(this),this.opt.score=l._adjustedScore.call(this,this.opt.score),"img"!==this.opt.starType&&l._adjustStarType.call(this),l._adjustPath.call(this),l._createStars.call(this),this.opt.cancel&&l._createCancel.call(this),this.opt.precision&&l._adjustPrecision.call(this),l._createScore.call(this),l._apply.call(this,this.opt.score),l._setTitle.call(this,this.opt.score),l._target.call(this,this.opt.score),this.opt.readOnly?l._lock.call(this):(this.style.cursor="pointer",l._binds.call(this))})},_adjustCallback:function(){for(var t=["number","readOnly","score","scoreName","target"],a=0;a<t.length;a++)"function"==typeof this.opt[t[a]]&&(this.opt[t[a]]=this.opt[t[a]].call(this))},_adjustedScore:function(t){return t?l._between(t,0,this.opt.number):t},_adjustHints:function(){if(this.opt.hints||(this.opt.hints=[]),this.opt.halfShow||this.opt.half)for(var t=this.opt.precision?10:2,a=0;a<this.opt.number;a++){var e=this.opt.hints[a];"[object Array]"!==Object.prototype.toString.call(e)&&(e=[e]),this.opt.hints[a]=[];for(var s=0;s<t;s++){var i=e[s],o=e[e.length-1];void 0===o&&(o=null),this.opt.hints[a][s]=void 0===i?o:i}}},_adjustNumber:function(){this.opt.number=l._between(this.opt.number,1,this.opt.numberMax)},_adjustPath:function(){this.opt.path=this.opt.path||"",this.opt.path&&"/"!==this.opt.path.charAt(this.opt.path.length-1)&&(this.opt.path+="/")},_adjustPrecision:function(){this.opt.half=!0},_adjustStarType:function(){var t=["cancelOff","cancelOn","starHalf","starOff","starOn"];this.opt.path="";for(var a=0;a<t.length;a++)this.opt[t[a]]=this.opt[t[a]].replace(".","-")},_apply:function(t){l._fill.call(this,t),t&&(0<t&&this.score.val(t),l._roundStars.call(this,t))},_between:function(t,a,e){return Math.min(Math.max(parseFloat(t),a),e)},_binds:function(){this.cancel&&(l._bindOverCancel.call(this),l._bindClickCancel.call(this),l._bindOutCancel.call(this)),l._bindOver.call(this),l._bindClick.call(this),l._bindOut.call(this)},_bindClick:function(){var s=this;s.stars.on("click.raty",function(t){var a=!0,e=s.opt.half||s.opt.precision?s.self.data("score"):this.alt||n(this).data("alt");s.opt.click&&(a=s.opt.click.call(s,+e,t)),(a||void 0===a)&&(s.opt.half&&!s.opt.precision&&(e=l._roundHalfScore.call(s,e)),l._apply.call(s,e))})},_bindClickCancel:function(){var a=this;a.cancel.on("click.raty",function(t){a.score.removeAttr("value"),a.opt.click&&a.opt.click.call(a,null,t)})},_bindOut:function(){var e=this;e.self.on("mouseleave.raty",function(t){var a=+e.score.val()||void 0;l._apply.call(e,a),l._target.call(e,a,t),l._resetTitle.call(e),e.opt.mouseout&&e.opt.mouseout.call(e,a,t)})},_bindOutCancel:function(){var s=this;s.cancel.on("mouseleave.raty",function(t){var a=s.opt.cancelOff;if("img"!==s.opt.starType&&(a=s.opt.cancelClass+" "+a),l._setIcon.call(s,this,a),s.opt.mouseout){var e=+s.score.val()||void 0;s.opt.mouseout.call(s,e,t)}})},_bindOver:function(){var e=this,t=e.opt.half?"mousemove.raty":"mouseover.raty";e.stars.on(t,function(t){var a=l._getScoreByPosition.call(e,t,this);l._fill.call(e,a),e.opt.half&&(l._roundStars.call(e,a,t),l._setTitle.call(e,a,t),e.self.data("score",a)),l._target.call(e,a,t),e.opt.mouseover&&e.opt.mouseover.call(e,a,t)})},_bindOverCancel:function(){var s=this;s.cancel.on("mouseover.raty",function(t){var a=s.opt.path+s.opt.starOff,e=s.opt.cancelOn;"img"===s.opt.starType?s.stars.attr("src",a):(e=s.opt.cancelClass+" "+e,s.stars.attr("class",a)),l._setIcon.call(s,this,e),l._target.call(s,null,t),s.opt.mouseover&&s.opt.mouseover.call(s,null)})},_buildScoreField:function(){return n("<input />",{name:this.opt.scoreName,type:"hidden"}).appendTo(this)},_createCancel:function(){var t=this.opt.path+this.opt.cancelOff,a=n("<"+this.opt.starType+" />",{title:this.opt.cancelHint,class:this.opt.cancelClass});"img"===this.opt.starType?a.attr({src:t,alt:"x"}):a.attr("data-alt","x").addClass(t),"left"===this.opt.cancelPlace?this.self.prepend("&#160;").prepend(a):this.self.append("&#160;").append(a),this.cancel=a},_createScore:function(){var t=n(this.opt.targetScore);this.score=t.length?t:l._buildScoreField.call(this)},_createStars:function(){for(var t=1;t<=this.opt.number;t++){var a=l._nameForIndex.call(this,t),e={alt:t,src:this.opt.path+this.opt[a]};"img"!==this.opt.starType&&(e={"data-alt":t,class:e.src}),e.title=l._getHint.call(this,t),n("<"+this.opt.starType+" />",e).appendTo(this),this.opt.space&&this.self.append(t<this.opt.number?"&#160;":"")}this.stars=this.self.children(this.opt.starType)},_error:function(t){n(this).text(t),n.error(t)},_fill:function(t){for(var a=0,e=1;e<=this.stars.length;e++){var s,i=this.stars[e-1],o=l._turnOn.call(this,e,t);if(this.opt.iconRange&&this.opt.iconRange.length>a){var r=this.opt.iconRange[a];s=l._getRangeIcon.call(this,r,o),e<=r.range&&l._setIcon.call(this,i,s),e===r.range&&a++}else s=this.opt[o?"starOn":"starOff"],l._setIcon.call(this,i,s)}},_getFirstDecimal:function(t){var a=t.toString().split(".")[1],e=0;return a&&(e=parseInt(a.charAt(0),10),"9999"===a.slice(1,5)&&e++),e},_getRangeIcon:function(t,a){return a?t.on||this.opt.starOn:t.off||this.opt.starOff},_getScoreByPosition:function(t,a){var e=parseInt(a.alt||a.getAttribute("data-alt"),10);if(this.opt.half){var s=l._getWidth.call(this);e=e-1+parseFloat((t.pageX-n(a).offset().left)/s)}return e},_getHint:function(t,a){if(0!==t&&!t)return this.opt.noRatedMsg;var e=l._getFirstDecimal.call(this,t),s=Math.ceil(t),i=this.opt.hints[(s||1)-1],o=i,r=!a||this.move;return this.opt.precision?(r&&(e=0===e?9:e-1),o=i[e]):(this.opt.halfShow||this.opt.half)&&(o=i[e=r&&0===e?1:5<e?1:0]),""===o?"":o||t},_getWidth:function(){var t=this.stars[0].width||parseFloat(this.stars.eq(0).css("font-size"));return t||l._error.call(this,"Could not get the icon width!"),t},_lock:function(){var t=l._getHint.call(this,this.score.val());this.style.cursor="",this.title=t,this.score.prop("readonly",!0),this.stars.prop("title",t),this.cancel&&this.cancel.hide(),this.self.data("readonly",!0)},_nameForIndex:function(t){return this.opt.score&&this.opt.score>=t?"starOn":"starOff"},_resetTitle:function(t){for(var a=0;a<this.opt.number;a++)this.stars[a].title=l._getHint.call(this,a+1)},_roundHalfScore:function(t){var a=parseInt(t,10),e=l._getFirstDecimal.call(this,t);return 0!==e&&(e=5<e?1:.5),a+e},_roundStars:function(t,a){var e,s=(t%1).toFixed(2);if(a||this.move?e=.5<s?"starOn":"starHalf":s>this.opt.round.down&&(e="starOn",this.opt.halfShow&&s<this.opt.round.up?e="starHalf":s<this.opt.round.full&&(e="starOff")),e){var i=this.opt[e],o=this.stars[Math.ceil(t)-1];l._setIcon.call(this,o,i)}},_setIcon:function(t,a){t["img"===this.opt.starType?"src":"className"]=this.opt.path+a},_setTarget:function(t,a){a&&(a=this.opt.targetFormat.toString().replace("{score}",a)),t.is(":input")?t.val(a):t.html(a)},_setTitle:function(t,a){if(t){var e=parseInt(Math.ceil(t),10);this.stars[e-1].title=l._getHint.call(this,t,a)}},_target:function(t,a){if(this.opt.target){var e=n(this.opt.target);e.length||l._error.call(this,"Target selector invalid or missing!");var s=a&&"mouseover"===a.type;if(void 0===t)t=this.opt.targetText;else if(null===t)t=s?this.opt.cancelHint:this.opt.targetText;else{"hint"===this.opt.targetType?t=l._getHint.call(this,t,a):this.opt.precision&&(t=parseFloat(t).toFixed(1));var i=a&&"mousemove"===a.type;s||i||this.opt.targetKeep||(t=this.opt.targetText)}l._setTarget.call(this,e,t)}},_turnOn:function(t,a){return this.opt.single?t===a:t<=a},_unlock:function(){this.style.cursor="pointer",this.removeAttribute("title"),this.score.removeAttr("readonly"),this.self.data("readonly",!1);for(var t=0;t<this.opt.number;t++)this.stars[t].title=l._getHint.call(this,t+1);this.cancel&&this.cancel.css("display","")},cancel:function(a){return this.each(function(){var t=n(this);!0!==t.data("readonly")&&(l[a?"click":"score"].call(t,null),this.score.removeAttr("value"))})},click:function(t){return this.each(function(){!0!==n(this).data("readonly")&&(t=l._adjustedScore.call(this,t),l._apply.call(this,t),this.opt.click&&this.opt.click.call(this,t,n.Event("click")),l._target.call(this,t))})},destroy:function(){return this.each(function(){var t=n(this),a=t.data("raw");a?t.off(".raty").empty().css({cursor:a.style.cursor}).removeData("readonly"):t.data("raw",t.clone()[0])})},getScore:function(){var t,a=[];return this.each(function(){t=this.score.val(),a.push(t?+t:void 0)}),1<a.length?a:a[0]},move:function(r){return this.each(function(){var t=parseInt(r,10),a=l._getFirstDecimal.call(this,r);t>=this.opt.number&&(t=this.opt.number-1,a=10);var e=l._getWidth.call(this)/10,s=n(this.stars[t]),i=s.offset().left+e*a,o=n.Event("mousemove",{pageX:i});this.move=!0,s.trigger(o),this.move=!1})},readOnly:function(a){return this.each(function(){var t=n(this);t.data("readonly")!==a&&(a?(t.off(".raty").children("img").off(".raty"),l._lock.call(this)):(l._binds.call(this),l._unlock.call(this)),t.data("readonly",a))})},reload:function(){return l.set.call(this,{})},score:function(){var t=n(this);return arguments.length?l.setScore.apply(t,arguments):l.getScore.call(t)},set:function(t){return this.each(function(){n(this).raty(n.extend({},this.opt,t))})},setScore:function(t){return this.each(function(){!0!==n(this).data("readonly")&&(t=l._adjustedScore.call(this,t),l._apply.call(this,t),l._target.call(this,t))})}};n.fn.raty=function(t){return l[t]?l[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void n.error("Method "+t+" does not exist!"):l.init.apply(this,arguments)},n.fn.raty.defaults={cancel:!1,cancelClass:"raty-cancel",cancelHint:"Cancel this rating!",cancelOff:"cancel-off.png",cancelOn:"cancel-on.png",cancelPlace:"left",click:void 0,half:!1,halfShow:!0,hints:["+1","+2","+3","+4","+5","+6","+7","+8","+9","+10"],iconRange:void 0,mouseout:void 0,mouseover:void 0,noRatedMsg:"Not rated yet!",number:10,numberMax:20,path:void 0,precision:!1,readOnly:!1,round:{down:.25,full:.6,up:.76},score:void 0,scoreName:"score",single:!1,space:!0,starHalf:"star-half.png",starOff:"star-off.png",starOn:"star-on.png",starType:"img",target:void 0,targetFormat:"{score}",targetKeep:!1,targetScore:void 0,targetText:"",targetType:"hint"}}(jQuery);
