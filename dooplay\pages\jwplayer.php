<?php
/* ----------------------------------------------------
* Template Name: DT - jwplayer
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
*
* @since 2.5.0
*
*/

// Libraries and dynamic data
$google = new DooGdrive;
$source = urldecode(doo_isset($_GET,'source'));
$typeso = doo_isset($_GET,'type');
$postid = doo_isset($_GET,'id');
$images = get_post_meta($postid,'imagenes', true);
$jwpkey = dooplay_get_option('jwkey','IMtAJf5X9E17C1gol8B45QJL5vWOCxYUDyznpA==');
$jw8key = dooplay_get_option('jw8key','64HPbvSQorQcd52B8XFuhMtEoitbvY/EXJmMBfKcXZQU2Rnn');
$libray = dooplay_get_option('player','plyr');
$mp4fle = ($typeso == 'gdrive') ? $google->GetUrl($source) : $source;
$prvimg = dbmovies_get_rand_image($images);
$plyrcl = dooplay_get_option('playercolor','#0b7ef4');

// Compose data for Json
$data = array(
    'file'  => $mp4fle,
    'image' => $prvimg,
    'color' => $plyrcl,
    'link'  => esc_url(home_url()),
    'logo'  => doo_compose_image_option('jwlogo'),
    'auto'  => doo_is_true('playauto','jwp') ? 'true' : 'false',
    'text'  => dooplay_get_option('jwabout','DooPlay Theme WordPress'),
    'lposi' => dooplay_get_option('jwposition','top-right'),
    'flash' =>  DOO_URI.'/assets/jwplayer/jwplayer.flash.swf'
);

// Render JW Player
require_once(DOO_DIR.'/pages/sections/'.$libray.'.php');
