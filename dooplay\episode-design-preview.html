<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Episode List Design Preview</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/custom-episodes.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        #episodes {
            padding: 0;
        }
        .sbox {
            padding: 0;
        }

        /* Demo specific styles */
        .demo-note {
            background: #e3f2fd;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            font-size: 14px;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Enhanced Episode List Design</h1>
            <p>Modern, responsive, and accessible episode listing for Dooplay theme</p>
        </div>
        
        <div id="episodes" class="sbox fixidtab">
            <h2 style="padding: 25px 40px 0; margin: 0; font-size: 24px; color: #333;">Seasons and Episodes</h2>

            <div class="demo-note">
                <strong>Updated Design:</strong>
                • Cleaner, theme-consistent styling
                • Subtle colors that match dark theme
                • Click episode poster or links button to show options
                • Episode title is not clickable (normal link behavior)
                • No separate download section - all links in episode list
                • Improved mobile experience
            </div>
            
            <div id="serie_contenido">
                <div id="seasons">
                    <!-- Season 1 -->
                    <div class="se-c">
                        <div class="se-q">
                            <span class="se-t">1</span>
                            <span class="title">Season 1 <i>2023</i></span>
                        </div>
                        <div class="se-a" style="display:block">
                            <ul class="episodios">
                                <li class="mark-1 episode-with-links">
                                    <div class="episode-main-info">
                                        <div class="imagen"><img src="https://via.placeholder.com/120x68/667eea/ffffff?text=EP1" alt="Episode 1"></div>
                                        <div class="numerando">1 - 1</div>
                                        <div class="episodiotitle">
                                            <a href="#">The Beginning</a>
                                            <span class="date">Jan 15, 2023</span>
                                        </div>
                                    </div>
                                    <div class="episode-links-section">
                                        <div class="links-toggle"><i class="fas fa-download"></i> 3 Links</div>
                                        <div class="episode-links-container">
                                            <div class="episode-link-item">
                                                <div class="link-info">
                                                    <span class="quality">1080P</span>
                                                    <span class="language">EN</span>
                                                    <span class="size">2.1GB</span>
                                                </div>
                                                <div class="link-actions">
                                                    <a href="#" class="download-btn"><i class="fas fa-download"></i></a>
                                                    <a href="#" class="play-btn" onclick="playEpisode('https://example.com/stream1', 'The Beginning'); return false;"><i class="fas fa-play"></i></a>
                                                </div>
                                            </div>
                                            <div class="episode-link-item">
                                                <div class="link-info">
                                                    <span class="quality">720P</span>
                                                    <span class="language">EN</span>
                                                    <span class="size">1.2GB</span>
                                                </div>
                                                <div class="link-actions">
                                                    <a href="#" class="download-btn"><i class="fas fa-download"></i></a>
                                                    <a href="#" class="play-btn" onclick="playEpisode('https://example.com/stream2', 'The Beginning'); return false;"><i class="fas fa-play"></i></a>
                                                </div>
                                            </div>
                                            <div class="episode-link-item">
                                                <div class="link-info">
                                                    <span class="quality">480P</span>
                                                    <span class="language">EN</span>
                                                    <span class="size">650MB</span>
                                                </div>
                                                <div class="link-actions">
                                                    <a href="#" class="download-btn"><i class="fas fa-download"></i></a>
                                                    <a href="#" class="play-btn" onclick="playEpisode('https://example.com/stream3', 'The Beginning'); return false;"><i class="fas fa-play"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                
                                <li class="mark-2 episode-with-links">
                                    <div class="episode-main-info">
                                        <div class="imagen"><img src="https://via.placeholder.com/120x68/764ba2/ffffff?text=EP2" alt="Episode 2"></div>
                                        <div class="numerando">1 - 2</div>
                                        <div class="episodiotitle">
                                            <a href="#">The Journey Continues</a>
                                            <span class="date">Jan 22, 2023</span>
                                        </div>
                                    </div>
                                    <div class="episode-links-section">
                                        <div class="links-toggle"><i class="fas fa-download"></i> 2 Links</div>
                                        <div class="episode-links-container">
                                            <div class="episode-link-item">
                                                <div class="link-info">
                                                    <span class="quality">1080P</span>
                                                    <span class="language">EN</span>
                                                    <span class="size">2.3GB</span>
                                                </div>
                                                <div class="link-actions">
                                                    <a href="#" class="download-btn"><i class="fas fa-download"></i></a>
                                                    <a href="#" class="play-btn" onclick="playEpisode('https://example.com/stream4', 'The Journey Continues'); return false;"><i class="fas fa-play"></i></a>
                                                </div>
                                            </div>
                                            <div class="episode-link-item">
                                                <div class="link-info">
                                                    <span class="quality">720P</span>
                                                    <span class="language">EN</span>
                                                    <span class="size">1.4GB</span>
                                                </div>
                                                <div class="link-actions">
                                                    <a href="#" class="download-btn"><i class="fas fa-download"></i></a>
                                                    <a href="#" class="play-btn" onclick="playEpisode('https://example.com/stream5', 'The Journey Continues'); return false;"><i class="fas fa-play"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                
                                <li class="mark-3 episode-card watched">
                                    <div class="episode-thumbnail">
                                        <img src="https://via.placeholder.com/120x68/f093fb/ffffff?text=EP3" alt="Episode 3">
                                        <div class="play-overlay"><i class="fas fa-play"></i></div>
                                    </div>
                                    <div class="episode-info">
                                        <div class="episode-number">S1E3</div>
                                        <div class="episode-details">
                                            <h3 class="episode-title"><a href="#">The Plot Thickens</a></h3>
                                            <span class="episode-date">Jan 29, 2023</span>
                                        </div>
                                        <div class="watched-indicator"><i class="fas fa-check"></i></div>
                                    </div>
                                </li>
                                
                                <li class="mark-4 episode-card">
                                    <div class="episode-thumbnail">
                                        <img src="https://via.placeholder.com/120x68/667eea/ffffff?text=EP4" alt="Episode 4">
                                        <div class="play-overlay"><i class="fas fa-play"></i></div>
                                    </div>
                                    <div class="episode-info">
                                        <div class="episode-number">S1E4</div>
                                        <div class="episode-details">
                                            <h3 class="episode-title"><a href="#">Revelations</a></h3>
                                            <span class="episode-date">Feb 5, 2023</span>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Season 2 -->
                    <div class="se-c">
                        <div class="se-q">
                            <span class="se-t">2</span>
                            <span class="title">Season 2 <i>2023</i></span>
                        </div>
                        <div class="se-a" style="display:none">
                            <ul class="episodios">
                                <li class="mark-1 episode-card">
                                    <div class="episode-thumbnail">
                                        <img src="https://via.placeholder.com/120x68/764ba2/ffffff?text=S2E1" alt="Season 2 Episode 1">
                                        <div class="play-overlay"><i class="fas fa-play"></i></div>
                                    </div>
                                    <div class="episode-info">
                                        <div class="episode-number">S2E1</div>
                                        <div class="episode-details">
                                            <h3 class="episode-title"><a href="#">New Beginnings</a></h3>
                                            <span class="episode-date">Mar 15, 2023</span>
                                        </div>
                                    </div>
                                </li>
                                
                                <li class="mark-2 episode-card">
                                    <div class="episode-thumbnail">
                                        <img src="https://via.placeholder.com/120x68/f5576c/ffffff?text=S2E2" alt="Season 2 Episode 2">
                                        <div class="play-overlay"><i class="fas fa-play"></i></div>
                                    </div>
                                    <div class="episode-info">
                                        <div class="episode-number">S2E2</div>
                                        <div class="episode-details">
                                            <h3 class="episode-title"><a href="#">The Return</a></h3>
                                            <span class="episode-date">Mar 22, 2023</span>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Special Episodes -->
                    <div class="se-c">
                        <div class="se-q">
                            <span class="se-t"><i class="fas fa-star"></i></span>
                            <span class="title">Specials <i>2023</i></span>
                        </div>
                        <div class="se-a" style="display:none">
                            <ul class="episodios">
                                <li class="mark-1 episode-card">
                                    <div class="episode-thumbnail">
                                        <img src="https://via.placeholder.com/120x68/f093fb/ffffff?text=SP1" alt="Special Episode 1">
                                        <div class="play-overlay"><i class="fas fa-play"></i></div>
                                    </div>
                                    <div class="episode-info">
                                        <div class="episode-number special"><i class="fas fa-star"></i> E1</div>
                                        <div class="episode-details">
                                            <h3 class="episode-title"><a href="#">Behind the Scenes</a></h3>
                                            <span class="episode-date">Dec 25, 2023</span>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/custom-episodes.js"></script>
    
    <script>
        // Demo-specific enhancements
        $(document).ready(function() {
            // Add some demo interactions
            $('.episode-with-links').addClass('animate-in');

            // Show features info
            setTimeout(function() {
                console.log('Episode List with Inline Links Features:');
                console.log('✓ Click poster or links button to show options');
                console.log('✓ Episode title works as normal link');
                console.log('✓ Quality, language, size display');
                console.log('✓ Direct download buttons');
                console.log('✓ Inline video player');
                console.log('✓ Responsive design');
                console.log('✓ Clean, theme-consistent styling');
            }, 1000);

            // Demo alert for poster clicks only
            $('.episode-main-info .imagen').on('click', function(e) {
                console.log('Episode poster clicked - showing links!');
            });
        });
    </script>
</body>
</html>
