<?php
/**
 * <AUTHOR> (<PERSON><PERSON> & <PERSON><PERSON><PERSON>)
 * @since 2.5.0
 */

// Main Data
$itmdb = get_post_meta($post->ID,'ids',true);
$seaso = get_post_meta($post->ID,'temporada',true);
/*=====================================================*/
$query = DDbmoviesHelpers::GetAllEpisodes($itmdb,$seaso);
/*=====================================================*/
// Start Query
$html_out = "<div id='serie_contenido' style='padding-top:0'>";
$html_out .= "<div id='seasons'><div class='se-c'><div class='se-a' style='display:block'><ul class='episodios'>";
if($query && is_array($query)){
    foreach($query as $post_id){
        // Post Data
        $image = dbmovies_get_poster($post_id,'dt_episode_a','dt_backdrop','w154');
        $episo = get_post_meta($post_id,'episodio', true);
        $edate = get_post_meta($post_id,'air_date', true);
        $edate = doo_date_compose($edate, false);
        $name  = get_post_meta($post_id,'episode_name', true);
        $plink = get_permalink($post_id);
        $title = !empty($name) ? $name : __('Episode').' '.$episo;

        // Get episode links
        $episode_links = get_episode_download_links($post_id);
        // The View with Links
        $html_out .= "<li class='mark-{$episo} episode-with-links'>";
        $html_out .= "<div class='episode-main-info'>";
        $html_out .= "<div class='imagen'><img src='{$image}'></div>";
        if($seaso !== '0'){
            $html_out .= "<div class='numerando'>{$seaso} - {$episo}</div>";
        } else{
            $html_out .= "<div class='numerando'><i class='icon-star'></i> - {$episo}</div>";
        }
        $html_out .= "<div class='episodiotitle'>";
        $html_out .= "<a href='{$plink}'>{$title}</a>";
        $html_out .= "<span class='date'>{$edate}</span>";
        $html_out .= "</div>";
        $html_out .= "</div>";

        // Episode Links Section
        if(!empty($episode_links)){
            $html_out .= "<div class='episode-links-section'>";
            $html_out .= "<div class='links-toggle'><i class='fas fa-download'></i> " . count($episode_links) . " Links</div>";
            $html_out .= "<div class='episode-links-container'>";

            foreach($episode_links as $link){
                $html_out .= "<div class='episode-link-item'>";
                $html_out .= "<div class='link-info'>";
                $html_out .= "<span class='quality'>{$link['quality']}</span>";
                $html_out .= "<span class='language'>{$link['language']}</span>";
                $html_out .= "<span class='size'>{$link['size']}</span>";
                $html_out .= "</div>";
                $html_out .= "<div class='link-actions'>";
                $html_out .= "<a href='{$link['download_url']}' class='download-btn' target='_blank'><i class='fas fa-download'></i></a>";
                $html_out .= "<a href='#' class='play-btn' onclick=\"playEpisode('{$link['stream_url']}', '{$title}'); return false;\"><i class='fas fa-play'></i></a>";
                $html_out .= "</div>";
                $html_out .= "</div>";
            }

            $html_out .= "</div>";
            $html_out .= "</div>";
        }

        $html_out .= "</li>";
    }
}else{
    $html_out .= "<li class='none'>".__d('There are still no episodes this season')."</li>";
}
$html_out .= "</ul></div></div></div></div>";
// Compose viewer HTML
echo apply_filters('dooplay_list_seasons', $html_out, $itmdb);
