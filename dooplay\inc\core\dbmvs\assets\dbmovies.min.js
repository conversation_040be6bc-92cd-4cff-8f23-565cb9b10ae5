!(function (m) {
    var r = {},
        d = dbmovies.ajaxurl,
        i = dbmovies.dapiurl,
        l = dbmovies.tapiurl,
        n = dbmovies.dapikey,
        c = dbmovies.tapikey,
        p = dbmovies.apilang,
        a = dbmovies.safemod,
        o = dbmovies.extimer,
        s = dbmovies.inscrll,
        v = dbmovies.rscroll,
        e = dbmovies.csectin,
        u = dbmovies.nocrdt,
        b = dbmovies.gerepis,
        t = navigator.onLine,
        g = "#dbmovies-generartor",
        h = "#dbmovies-updaterpost",
        f = "#dbmovies-json-response",
        _ = "#dbmovies-importer",
        C = "#dbmovies-generator-seasons",
        k = "#dbmovies-generator-episodes",
        y = "#dbmovies-imdb-updater-page";
    function S(e, s) {
        var t = m.animateNumber.numberStepFactories.separator(",");
        m(e).animateNumber({ number: s, numberStep: t }, 1500);
    }
    function w(e) {
        return e.split("").reverse().join("");
    }
    function T(e) {
        var s = e.elem;
        s.nodeType && s.parentNode && (s._animateNumberSetter || E.numberStep)(e.now, e);
    }
    m(function () {
        r.GetStats(),
            r.GenerateSeEp(),
            r.PostGenerateSeEp(),
            r.GenerateAllEpisodesGet(),
            r.UpdaterEditorPost(),
            r.GenerateEditor(),
            r.NavSettings(),
            r.SaveSettings(),
            r.GoTop(),
            r.LogCollapse(),
            r.Auth(),
            r.IMDbUpdater(),
            r.CodeStarTab(),
            r.Application(),
            r.Search(),
            r.Filter(),
            r.LoadMore(),
            r.Importer(),
            r.Cimport(),
            r.GeneratorSeasons(),
            r.GeneratorEpisodes(),
            r.ReubicateForm(),
            r.LogCleaner(),
            r.InternetCon(),
            r.Dashboard(),
            r.MetaUpdater();
    }),
        (r.GetStats = function () {
            1 == m("#dbmvs_global_stats").length &&
                (m(document).ready(function () {
                    r.Stats();
                }),
                setInterval(function () {
                    r.Stats();
                }, 6e4));
        }),
        (r.Stats = function () {
			m("#last_cache").html(dooAj.loading);
            m.getJSON(i + "/?stats=all", function (e) {
                m.each(e, function (e, s) {
                    S("#dbmvs_" + e, s);
                });
				m("#last_cache").html(e.last_cache);
            });
        }),
        (r.MetaUpdater = function () {
            var e,
                s,
                t,
                a = "dbmovies_metaupdater",
                o = "#dbmvs-metaupdater";
            (StatusMU = "#dbmvs-metaupdater-status"),
                (Proccess = "#meta-updater-process"),
                (MControl = "#meta-updater-controls"),
                (MtUPause = "#meta-updater-pause"),
                (RunMTUpT = "#meta-updater-run"),
                1 != o.length &&
                    ((e = r.getCookie("_dbmvs_metaupdater_status")),
                    (s = r.getCookie("_dbmvs_metaupdater_progress")),
                    (t = r.getCookie("_dbmvs_metaupdater_running")),
                    "paused" == e &&
                        (m(StatusMU).val(s),
                        m(Proccess).show(),
                        m(Proccess + "> span.spinner").hide(),
                        m(Proccess + "> .proccess-bar > .into").addClass("flashit"),
                        m(Proccess + "> span.percentage").text(r.NumberFormat(s) + " %"),
                        m(RunMTUpT).hide(),
                        m(MtUPause).hide(),
                        m(MControl).show(),
                        m(Proccess + "> .proccess-bar > .into").animate({ width: s + "%" }, 150, function () {})),
                    1 == t &&
                        "paused" != e &&
                        (m(Proccess + "> span.percentage").text(r.NumberFormat(s) + " %"),
                        m(Proccess + "> .proccess-bar > .into").animate({ width: s + "%" }, 1e3, function () {}),
                        setTimeout(function () {
                            m(o).trigger("click");
                        }, 1e3)),
                    m(document).on("click", "#dbmvs-finish-metaupdater", function () {
                        var e = m(this).data("control");
                        return (
                            confirm("You are about to interrupt the process") &&
                                (m(MtUPause).hide(),
                                m(Proccess).hide(),
                                m(MControl).hide(),
                                m(RunMTUpT).show(),
                                m(Proccess + "> span.spinner").show(),
                                m(Proccess + "> .proccess-bar > .into").removeClass("flashit"),
                                m(StatusMU).val("progress"),
                                m.ajax({
                                    url: d,
                                    type: "POST",
                                    dataType: "json",
                                    data: { action: a, process: e },
                                    success: function (e) {
                                        r.delCookie("_dbmvs_metaupdater_running"),
                                            r.delCookie("_dbmvs_metaupdater_status"),
                                            r.delCookie("_dbmvs_metaupdater_progress"),
                                            m("#dbmvs-log-indicator").after('<li class="shake"><span class="type error">dbmvs</span>' + e.message + "</li>");
                                    },
                                })),
                            !1
                        );
                    }),
                    m(document).on("click", o, function () {
                        var e = m(StatusMU).val(),
                            s = m(this).data("control");
                        return (
                            "progress" == e &&
                                (m(MtUPause).show(),
                                m(Proccess).show(),
                                m(RunMTUpT).hide(),
                                m.ajax({
                                    url: d,
                                    type: "POST",
                                    dataType: "json",
                                    data: { action: a, process: s },
                                    success: function (e) {
                                        1 == e.success
                                            ? (m(o).trigger("click"),
                                              m(Proccess + "> .proccess-bar > .into").animate({ width: e.progress + "%" }, 150, function () {}),
                                              m(Proccess + "> span.percentage").text(r.NumberFormat(e.progress) + " %"),
                                              r.setCookie("_dbmvs_metaupdater_progress", e.progress, 30),
                                              r.setCookie("_dbmvs_metaupdater_running", 1, 30),
                                              r.ComposeItemLog(e.content))
                                            : (r.delCookie("_dbmvs_metaupdater_running"),
                                              r.delCookie("_dbmvs_metaupdater_status"),
                                              r.delCookie("_dbmvs_metaupdater_progress"),
                                              m(Proccess + "> .proccess-bar > .into").animate({ width: "0%" }, 800, function () {}),
                                              setTimeout(function () {
                                                  m(MtUPause).hide(),
                                                      m(Proccess).hide(),
                                                      m(MControl).hide(),
                                                      m(RunMTUpT).show(),
                                                      m(Proccess + "> span.spinner").show(),
                                                      m(Proccess + "> .proccess-bar > .into").removeClass("flashit"),
                                                      m(StatusMU).val("progress");
                                              }, 1e3));
                                    },
                                })),
                            !1
                        );
                    }),
                    m(document).on("click", ".metaupdater-control", function () {
                        switch (m(this).data("control")) {
                            case "continue":
                                m(MControl).hide(),
                                    m(MtUPause).show(),
                                    m(Proccess + "> span.spinner").show(),
                                    m(Proccess + "> .proccess-bar > .into").removeClass("flashit"),
                                    m(StatusMU).val("progress"),
                                    r.setCookie("_dbmvs_metaupdater_status", "progress", 30),
                                    setTimeout(function () {
                                        m(o).trigger("click");
                                    }, 500);
                                break;
                            case "finish":
                                break;
                            case "pause":
                                m(Proccess + "> span.spinner").hide(),
                                    m(Proccess + "> .proccess-bar > .into").addClass("flashit"),
                                    m(MtUPause).hide(),
                                    m(MControl).show(),
                                    m(StatusMU).val("paused"),
                                    r.setCookie("_dbmvs_metaupdater_status", "paused", 30);
                        }
                        return !1;
                    }));
        }),
        (r.TimeSince = function (e) {
            var s = Math.floor((Date.now() - e) / 1e3),
                t = dbmovies.timest.seconds;
            s < 0 && (s = -s);
            e = s;
            return (
                31536e3 <= s
                    ? (t = 1 != (e = Math.floor(s / 31536e3)) ? dbmovies.timest.years : dbmovies.timest.year)
                    : 2592e3 <= s
                    ? (t = 1 != (e = Math.floor(s / 2592e3)) ? dbmovies.timest.months : dbmovies.timest.month)
                    : 604800 <= s
                    ? (t = 1 != (e = Math.floor(s / 604800)) ? dbmovies.timest.weeks : dbmovies.timest.week)
                    : 86400 <= s
                    ? (t = 1 != (e = Math.floor(s / 86400)) ? dbmovies.timest.days : dbmovies.timest.day)
                    : 3600 <= s
                    ? (t = 1 != (e = Math.floor(s / 3600)) ? dbmovies.timest.hours : dbmovies.timest.hour)
                    : 60 <= s && (t = 1 != (e = Math.floor(s / 60)) ? dbmovies.timest.minutes : dbmovies.timest.minute),
                e + " " + t
            );
        }),
        (r.Dashboard = function () {
            var a = m("#dbmovies-inboxes-nonce").val();
            1 == m("#dooplay_dashboard_widget").length &&
                (1 == dbmovies.formrpt
                    ? (m("#doodashcont-reported-click").addClass("active"), m("#doodashcont-reported").addClass("on"))
                    : 1 == dbmovies.formcot &&
                      0 == dbmovies.formrpt &&
                      (m("#doodashcont-reported-click").remove(), m("#doodashcont-reported").remove(), m("#doodashcont-inbox-click").addClass("active"), m("#doodashcont-inbox").addClass("on")),
                0 == dbmovies.formcot && (m("#doodashcont-inbox-click").remove(), m("#doodashcont-inbox").remove())),
                m(document).on("click", ".dooplay-dashaboard-navigation", function () {
                    var e = m(this).attr("data-id");
                    e && (m(".dooplay-dashaboard-navigation").removeClass("active"), m(this).addClass("active"), m(".dashcont").removeClass("on"), m("#doodashcont-" + e).addClass("on"));
                }),
                1 == m("#response-inboxes-report").length && 1 == dbmovies.formrpt && r.GetDataInboxes("report", 1, a),
                1 == m("#response-inboxes-contact").length && 1 == dbmovies.formcot && r.GetDataInboxes("contact", 1, a),
                m(document).on("click", ".inboxes-loadmore", function () {
                    var e = m(this).data("type"),
                        s = m("#inboxes-input-" + e).val();
                    return m("#response-inboxes-" + e).addClass("loader"), m(this).prop("disabled", !0), console.log(this), r.GetDataInboxes(e, s, a, this), !1;
                }),
                m(document).on("click", ".close-inboxes", function () {
                    var e = m(this).data("type"),
                        e = "#post-" + m(this).data("id") + "-" + e;
                    return 1 == m(e + " > div.message").length && (m(e).removeClass("reading"), m(e).addClass("read"), m(e + " > .message").remove()), !1;
                }),
                m(document).on("click", ".inboxes-readme-message.unread", function () {
                    var e = "#response-count-" + m(this).data("type") + "-unread",
                        s = parseInt(m(e).text());
                    m(e).text(s - 1), m(e + "-span").text("(" + (s - 1) + ")");
                }),
                m(document).on("click", ".inboxes-delete-post", function () {
                    var s = m(this).data("type"),
                        e = m(this).data("id"),
                        t = "#post-" + e + "-" + s;
                    return (
                        m(t).hide(),
                        m.ajax({
                            url: d,
                            type: "POST",
                            dataType: "json",
                            data: { action: "dbmovies_inboxes_deleting", post: e, type: s, nonce: a },
                            success: function (e) {
                                1 == e.success
                                    ? (m(t).remove(),
                                      m("#response-count-" + s + "-unread-span").text("(" + e.unread + ")"),
                                      m("#response-count-" + s + "-unread").text(e.unread),
                                      m("#response-count-" + s + "-total").text(e.total),
                                      0 == e.total && m("#response-inboxes-" + s + " > .hidden").before('<div class="inboxes-notice"><p>' + dbmovies.nocont + "</p></div>"))
                                    : m(t).show();
                            },
                        }),
                        !1
                    );
                }),
                m(document).on("click", ".inboxes-readme-message", function () {
                    var e = m(this).data("type"),
                        s = m(this).data("id"),
                        t = "#post-" + s + "-" + e;
                    return (
                        m(this).removeClass("unread"),
                        m(this).addClass("read"),
                        1 != m(t + " > div.message").length
                            ? (m("#response-inboxes-" + e + " > .item > .message").remove(),
                              m("#response-inboxes-" + e + " > .item").removeClass("reading"),
                              m(t).removeClass("unread"),
                              m(t).addClass("reading"),
                              m(t + " > h4").after('<div class="message"><div class="onload"></div></div>'),
                              m.ajax({
                                  url: d,
                                  type: "POST",
                                  dataType: "html",
                                  data: { action: "dbmovies_inboxes_reading", type: e, post: s, nonce: a },
                                  success: function (e) {
                                      m(t).addClass("read"), m(t + " > .message").addClass("fadein"), m(t + " > .message").html(e);
                                  },
                              }))
                            : (m(t + " > .message").removeClass("fadein"),
                              m(t + " > .message").addClass("shake"),
                              setTimeout(function () {
                                  m(t + " > .message").removeClass("shake");
                              }, 800)),
                        !1
                    );
                });
        }),
        (r.GetDataInboxes = function (n, s, e, t) {
            m.getJSON(d + "?action=dbmovies_inboxes&type=" + n + "&page=" + s + "&nonce=" + e, function (e) {
                m("#response-count-" + n + "-unread-span").text("(" + e.unread + ")"), m("#response-count-" + n + "-unread").text(e.unread), m("#response-count-" + n + "-total").text(e.total);
                var i = [];
                m("#response-inboxes-" + n + " > .onload").remove(),
                    m("#response-inboxes-" + n).removeClass("loader"),
                    e.posts &&
                        (m.each(e.posts, function (e, s) {
                            var t = '<div id="post-' + s.ID + "-" + n + '" class="fadein item ' + s.post_status + '" data-id="' + s.ID + '">',
                                a = '<a href="#" class="inboxes-delete-post" data-id="' + s.ID + '" data-type="' + n + '"><i class="dashicons dashicons-trash"></i></a>',
                                o = '<span class="date">' + r.TimeSince(new Date(s.post_date)) + "</span>",
                                s = '<a href="#" data-id="' + s.ID + '" data-type="' + n + '" class="inboxes-readme-message ' + s.post_status + '">' + s.post_title + "</a></h4></div>";
                            i.push(t + '<h4 class="title"><i class="dashicons dashicons-email"></i>' + a + o + s);
                        }),
                        m("#response-inboxes-" + n + " > .hidden").before(i.join("")),
                        m(t).prop("disabled", !1),
                        e.pages > s ? (m("#inboxes-paginator-" + n).removeClass("hidden"), m("#inboxes-input-" + n).val(Math.floor(parseInt(s) + 1))) : m("#inboxes-paginator-" + n).addClass("hidden")),
                    0 == e.total && m("#response-inboxes-" + n + " > .hidden").before('<div class="inboxes-notice"><p>' + dbmovies.nocont + "</p></div>"),
                    e.error && m("#response-inboxes-" + n + " > .hidden").before('<div class="inboxes-notice"><p>' + e.error + "</p></div>");
            });
        }),
        (r.InternetCon = function () {
            t || console.log("No hay conexion a Internet");
        }),
        (r.LogCleaner = function () {
            (Cleaner = "#dbmovies-cleanlog"),
                m(document).on("click", Cleaner, function () {
                    return (
                        m(Cleaner).html(dbmovies.cleangn),
                        setTimeout(function () {
                            m("#dbmovies-logs-box > ul").html('<i id="dbmvs-log-indicator"></i><li class="jump"><span class="type">dbmvs</span>' + dbmovies.cllogs + "</li>"), m(Cleaner).html(dbmovies.cleancl);
                        }, 200),
                        !1
                    );
                });
        }),
        (r.GenerateSeEp = function () {
            m(document).on("click", "#dbmvseaepbtncl", function () {
                var e = m("#dbmvgsepare").val();
                return (
                    m("#dbgesbtn_" + e).show(),
                    m("#dbmvs_seaepi_generator").hide(),
                    setTimeout(function () {
                        m("#dbmvseaepbtncl").addClass("hidden"),
                            m("#dbmvseaepico").removeClass("done"),
                            m("#dbmvseaepico").addClass("loading"),
                            m("#dbmvseaeprgrs").removeClass("flashit"),
                            m("#dbmvgseitem").val("1"),
                            m("#dbmvgsetmdb").val(""),
                            m("#dbmvgsetotl").val(""),
                            m("#dbmvgsepare").val(""),
                            m("#dbmvgsetype").val(""),
                            m("#dbmvgsename").val(""),
                            m("#dbmvgseseas").val("");
                    }, 700),
                    !1
                );
            }),
                m(document).on("click", ".dbmvsarchiveseep", function () {
                    var s, t, a, o;
                    return (
                        n &&
                            (m(this).hide(),
                            m("#dbmvs_seaepi_generator").show(),
                            (s = m(this).data("parent")),
                            (t = m(this).data("tmdb")),
                            (a = m(this).data("season")),
                            (o = m(this).data("type")),
                            m.getJSON(i + "/?check=" + n, function (e) {
                                m("#dbmvgsepare").val(s),
                                    m("#dbmvgsetmdb").val(t),
                                    m("#dbmvgsetype").val(o),
                                    m("#dbmvgseseas").val(a),
                                    ((1 == e.status && 1 <= e.credits) || (1 == e.status && 1 == e.unlimited)) &&
                                        m.getJSON(l + "/tv/" + t + "?language=" + p + "&api_key=" + c, function (e) {
                                            var s = e.name || e.original_name;
                                            switch ((m("#dbmvgsename").val(s), o)) {
                                                case "seasons":
                                                    m("#dbmvgsetotl").val(e.number_of_seasons);
                                                    break;
                                                case "episodes":
                                                    m.each(e.seasons, function (e, s) {
                                                        a == s.season_number && m("#dbmvgsetotl").val(s.episode_count);
                                                    });
                                            }
                                            m("#dbmvseaeptxt").html(""),
                                                m("#dbmvseaeprgrs").show(),
                                                m("#dbmvseaeprgrs").addClass("flashit"),
                                                m("#dbmvseaepbtncl").removeClass("hidden"),
                                                m("#dbmvseaepico").removeClass("loading"),
                                                m("#dbmvseaepico").addClass("done"),
                                                m("#dbmvseaepbtn").prop("disabled", !1);
                                        });
                            })),
                        !1
                    );
                });
        }),
        (r.PostGenerateSeEp = function () {
            m(document).on("submit", "#dbmvseaepigenerator", function () {
                var s = m("#dbmvgsepare").val(),
                    t = m("#dbmvgsetmdb").val(),
                    a = parseInt(m("#dbmvgseitem").val()),
                    o = parseInt(m("#dbmvgsetotl").val()),
                    i = (a / o) * 100;
                return (
                    m("#dbmvseaepbtn").hide(),
                    m("#dbmvseaepbtncl").addClass("hidden"),
                    m("#dbmvseaeprgrs").removeClass("flashit"),
                    m("#dbmvseaepico").removeClass("done"),
                    m("#dbmvseaepico").addClass("loading"),
                    m("#dbmvseaeptxt").addClass("flashit"),
                    m("#dbmvseaeptxt").html(dbmovies.loading),
                    m("#gnrtse_" + s).html(dbmovies.prsseng),
                    m.ajax({
                        url: d,
                        type: "POST",
                        dataType: "json",
                        data: m(this).serialize(),
                        success: function (e) {
                            m("#dbmvgseitem").val(a + 1),
                                1 == e.response
                                    ? ((e = a + " / " + o + " >> " + e.title + " >> " + e.mtime),
                                      console.log(e),
                                      m("#dbmvgseconsolelog").html(e),
                                      m("#dbmvseaeprgrs > span").animate({ width: i + "%" }, 150, function () {}),
                                      a < o
                                          ? setTimeout(function () {
                                                m("#dbmvseaepbtn").trigger("click");
                                            }, 100)
                                          : r.PostGenerateSeEpFix(s, t))
                                    : r.PostGenerateSeEpFix(s, t);
                        },
                    }),
                    !1
                );
            });
        }),
        (r.PostGenerateSeEpFix = function (e, s) {
            setTimeout(function () {
                m("#dbmvgseconsolelog").html(""),
                    m("#dbmvgseitem").val("1"),
                    m("#dbmvs_seaepi_generator").hide(),
                    m("#dbmvseaeprgrs").hide(),
                    m("#dbmvseaepbtn").show(),
                    m("#gnrtse_" + e).html('<code class="ready">' + s + "</code>"),
                    m("#dbmvseaepbtn").prop("disabled", !0),
                    m("#dbmvseaeptxt").removeClass("flashit"),
                    m("#dbmvseaeprgrs > span").animate({ width: "0%" }, 200, function () {});
            }, 300);
        }),
        (r.GenerateAllEpisodesGet = function () {
            "all" == b &&
                m(document).ready(function () {
                    m("#dbmovies-generator-epprepa").trigger("click");
                }),
                m(document).on("click", ".dbmvs_generate_episodes", function () {
                    m(this).hide();
                });
        }),
        (r.UpdaterEditorPost = function () {
            1 == m(h).length &&
                m(document).on("click", h, function () {
                    m("#dbmovies-message").remove(),
                        m("#api_table").addClass("hidden_api"),
                        m("#loading_api").html('<p><span class="spinner"></span>' + dbmovies.loading + "</p>"),
                        n
                            ? m
                                  .getJSON(i + "/?check=" + n, function (e) {
                                      if ((1 == e.status && 1 <= e.credits) || (1 == e.status && 1 == e.unlimited)) {
                                          var s = m("#post_type").val(),
                                              t = m("#ids").val(),
                                              a = m("#temporada").val(),
                                              o = m("#episodio").val();
                                          switch (s) {
                                              case "movies":
                                                  r.UpdateEditorMovies(t);
                                                  break;
                                              case "tvshows":
                                                  r.UpdateEditorTVShows(t);
                                                  break;
                                              case "seasons":
                                                  r.UpdateEditorSeasons(t, a);
                                                  break;
                                              case "episodes":
                                                  r.UpdateEditorEpisodes(t, a, o);
                                          }
                                      } else e.error ? r.ErrorNoticeEditor(e.error) : r.ErrorNoticeEditor(u);
                                  })
                                  .fail(function () {
                                      r.ErrorNoticeEditor(dbmovies.dbmverr);
                                  })
                            : r.ErrorNoticeEditor(dbmovies.misskey);
                });
        }),
        (r.SetPostContent = function (e) {
            var s, t;
            e &&
                "undefined" != typeof tinymce &&
                ((s = m("textarea#content").val()),
                (t = tinymce.get("content")) && t instanceof tinymce.Editor ? '<p><br data-mce-bogus="1"></p>' == t.startContent && (t.setContent(e), t.save({ no_events: !0 })) : s || m("textarea#content").val(e));
        }),
        (r.SetPostImages = function (e) {
            var t;
            !m("#imagenes").val() &&
                e &&
                ((t = ""),
                m.each(e, function (e, s) {
                    return !(9 < e) && void (t += 9 == e ? s.file_path : s.file_path + "\n");
                }),
                m("#imagenes").val(t));
        }),
        (r.TitleReplace = function (e, s, t, a, o) {
            return e.replace("{name}", s).replace("{season}", a).replace("{episode}", o).replace("{year}", t);
        }),
        (r.ImagePreUpload = function (e) {
            1 == dbmovies.pupload && e && 0 == m("#postimagediv p img").length && m("#postimagediv p").html('<ul><li><img src="https://image.tmdb.org/t/p/w500' + e + '"/> </li></ul>');
        }),
        (r.UpdateEditorMovies = function (e) {
            var s = l + "/movie/" + e;
            (apilan = "?language=" + p),
                (imglan = "&include_image_language=" + p),
                (aditin = ",null&append_to_response=images&api_key=" + c),
                m.getJSON(s.concat(apilan, imglan, aditin), function (e) {
                    r.SetPostContent(e.overview),
                        r.SetPostImages(e.images.backdrops),
                        m("#dt_poster").val(e.poster_path),
                        m("#dt_backdrop").val(e.backdrop_path),
                        m("#idtmdb").val(e.id),
                        m("#original_title").val(e.original_title),
                        m("#tagline").val(e.tagline),
                        m("#release_date").val(e.release_date),
                        m("#vote_average").val(e.vote_average),
                        m("#vote_count").val(e.vote_count),
                        m("#runtime").val(e.runtime);
                }),
                m.getJSON(i + "/?key=" + n + "&imdb=" + e, function (e) {
                    m("#imdbRating").val(e.rating), m("#imdbVotes").val(e.votes), m("#Rated").val(e.rated), m("#Country").val(e.country);
                }),
                r.ErrorFixedEditor();
        }),
        (r.UpdateEditorTVShows = function (e) {
            e = l + "/tv/" + e;
            (apilan = "?language=" + p),
                (imglan = "&include_image_language=" + p),
                (aditin = ",null&append_to_response=images&api_key=" + c),
                m.getJSON(e.concat(apilan, imglan, aditin), function (e) {
                    r.SetPostContent(e.overview),
                        r.SetPostImages(e.images.backdrops),
                        m("#dt_poster").val(e.poster_path),
                        m("#dt_backdrop").val(e.backdrop_path),
                        m("#original_name").val(e.original_name),
                        m("#first_air_date").val(e.first_air_date),
                        m("#last_air_date").val(e.last_air_date),
                        m("#number_of_seasons").val(e.number_of_seasons),
                        m("#number_of_episodes").val(e.number_of_episodes),
                        m("#imdbRating").val(e.vote_average),
                        m("#imdbVotes").val(e.vote_count),
                        m("#episode_run_time").val(e.episode_run_time[0]);
                }),
                r.ErrorFixedEditor();
        }),
        (r.UpdateEditorSeasons = function (e, t) {
            e = l + "/tv/" + e;
            (apilan = "?language=" + p),
                (aditin = "&api_key=" + c),
                m.getJSON(e.concat(apilan, aditin), function (e) {
                    m("#serie").val(e.name),
                        m("#title").val() || (m("label#title-prompt-text").addClass("screen-reader-text"), m("#title").val(r.TitleReplace(dbmovies.titseas, e.name, null, t, null))),
                        m.each(e.seasons, function (e, s) {
                            t == s.season_number && (r.SetPostContent(s.overview), r.ImagePreUpload(s.poster_path), m("#dt_poster").val(s.poster_path), m("#air_date").val(s.air_date));
                        });
                }),
                r.ErrorFixedEditor();
        }),
        (r.UpdateEditorEpisodes = function (e, s, t) {
            var a = l + "/tv/" + e;
            (apiuep = l + "/tv/" + e + "/season/" + s + "/episode/" + t),
                (apilan = "?language=" + p),
                (aditin = "&api_key=" + c),
                (imglan = "&include_image_language=" + p),
                (rditin = ",null&append_to_response=images&api_key=" + c),
                m.getJSON(a.concat(apilan, aditin), function (e) {
                    e = e.name;
                    (title = m("#title").val()), m("#serie").val(e), title || (m("label#title-prompt-text").addClass("screen-reader-text"), m("#title").val(r.TitleReplace(dbmovies.titepis, e, null, s, t)));
                }),
                m.getJSON(apiuep.concat(apilan, imglan, rditin), function (e) {
                    var t;
                    r.SetPostContent(e.overview),
                        r.ImagePreUpload(e.still_path),
                        m("#episode_name").val(e.name),
                        m("#dt_backdrop").val(e.still_path),
                        m("#air_date").val(e.air_date),
                        !m("#imagenes").val() &&
                            e.images.stills &&
                            ((t = ""),
                            m.each(e.images.stills, function (e, s) {
                                return !(9 < e) && void (t += 9 == e ? s.file_path : s.file_path + "\n");
                            }),
                            m("#imagenes").val(t));
                }),
                r.ErrorFixedEditor();
        }),
        (r.GenerateEditor = function () {
            1 == m(g).length &&
                m(document).on("click", g, function () {
                    m("#dbmovies-message").remove(),
                        m("#api_table").addClass("hidden_api"),
                        m("#loading_api").html('<p><span class="spinner"></span>' + dbmovies.loading + "</p>"),
                        n
                            ? m
                                  .getJSON(i + "/?check=" + n, function (e) {
                                      if ((1 == e.status && 1 <= e.credits) || (1 == e.status && 1 == e.unlimited)) {
                                          var s = m("#post_type").val(),
                                              t = m("#post_ID").val(),
                                              a = m("#ids").val(),
                                              o = m("#temporada").val(),
                                              i = m("#episodio").val();
                                          switch (s) {
                                              case "movies":
                                              case "tvshows":
                                                  r.AddMainTitle(t, a, s);
                                                  break;
                                              case "seasons":
                                                  r.AddSeason(t, a, o, s);
                                                  break;
                                              case "episodes":
                                                  r.AddEpisode(t, a, o, i, s);
                                          }
                                      } else e.error ? r.ErrorNoticeEditor(e.error) : r.ErrorNoticeEditor(u);
                                  })
                                  .fail(function () {
                                      r.ErrorNoticeEditor(dbmovies.dbmverr);
                                  })
                            : r.ErrorNoticeEditor(dbmovies.misskey);
                });
        }),
        (r.AddMainTitle = function (e, s, t) {
            s
                ? m.ajax({
                      url: d,
                      type: "POST",
                      dataType: "json",
                      data: { idpost: e, tmdbid: s, typept: t, action: "dbmovies_genereditor" },
                      success: function (e) {
                          1 == e.response && window.location.replace(e.editlink), 0 == e.response && r.ErrorNoticeEditor(e.message);
                      },
                  })
                : r.ErrorFixedEditor();
        }),
        (r.AddSeason = function (s, t, a, o) {
            t && a
                ? m
                      .getJSON(l + "/tv/" + t + "?language=" + p + "&api_key=" + c, function (e) {
                          m.ajax({
                              url: d,
                              type: "POST",
                              dataType: "json",
                              data: { idpost: s, tmdbid: t, typept: o, season: a, tvname: e.name, action: "dbmovies_genereditor" },
                              success: function (e) {
                                  1 == e.response && (e.message ? r.ErrorNoticeEditor(e.message) : window.location.replace(e.editlink)), 0 == e.response && r.ErrorNoticeEditor(e.message);
                              },
                          });
                      })
                      .fail(function () {
                          r.ErrorNoticeEditor(dbmovies.tmdberr);
                      })
                : r.ErrorFixedEditor();
        }),
        (r.AddEpisode = function (s, t, a, o, i) {
            t && a && o
                ? m
                      .getJSON(l + "/tv/" + t + "?language=" + p + "&api_key=" + c, function (e) {
                          m.ajax({
                              url: d,
                              type: "POST",
                              dataType: "json",
                              data: { idpost: s, tmdbid: t, typept: i, season: a, episde: o, tvname: e.name, action: "dbmovies_genereditor" },
                              success: function (e) {
                                  1 == e.response && (e.message ? r.ErrorNoticeEditor(e.message) : window.location.replace(e.editlink)), 0 == e.response && r.ErrorNoticeEditor(e.message);
                              },
                          });
                      })
                      .fail(function () {
                          r.ErrorNoticeEditor(dbmovies.tmdberr);
                      })
                : r.ErrorFixedEditor();
        }),
        (r.ErrorNoticeEditor = function (e) {
            m("#postbox-container-2").prepend('<div id="dbmovies-message" class="notice notice-warning"><p>' + e + "</p></div>"), m("#loading_api").html(""), m("#api_table").removeClass("hidden_api");
        }),
        (r.ErrorFixedEditor = function () {
            m("#loading_api").html(""), m("#api_table").removeClass("hidden_api");
        }),
        (r.NavSettings = function () {
            var e = r.getCookie("_dbmvs_tabname"),
                s = "#dbmvs-nav-settings > li.nav-tab";
            1 == m("#dbmvs-nav-settings").length &&
                setTimeout(function () {
                    m("#settab-" + e).trigger("click");
                }, 1),
                m(document).on("click", s, function () {
                    var e = m(this).data("tab");
                    return 0 != e && (m(s).removeClass("nav-tab-active"), m(".tab-content").removeClass("on"), m(this).addClass("nav-tab-active"), m("#dbmv-setting-" + e).addClass("on"), r.setCookie("_dbmvs_tabname", e, 30)), !1;
                });
        }),
        (r.SaveSettings = function () {
            var s = "#dbmvsbtn-savesettings",
                t = "#dbmvsssrespnc";
            m(document).on("submit", "#dbmovies-settings", function () {
                return (
                    m(s).prop("disabled", !0),
                    m(".tab-content").addClass("onprogress"),
                    m.ajax({
                        url: d,
                        type: "POST",
                        dataType: "json",
                        data: m(this).serialize(),
                        success: function (e) {
                            n && 1 != e.reload
                                ? (m(".tab-content").removeClass("onprogress"),
                                  m(t).addClass("fadein"),
                                  m(t).html(e.message),
                                  setTimeout(function () {
                                      m(s).prop("disabled", !1), m(t).html(""), m(t).removeClass("fadein");
                                  }, 1e3))
                                : location.reload();
                        },
                    }),
                    !1
                );
            });
        }),
        (r.GoTop = function () {
            var e = "#dbmovies-back-top";
            1 == m(e).length &&
                (m(e).hide(),
                m(function () {
                    m(window).scroll(function () {
                        200 < m(this).scrollTop() ? m(e).fadeIn() : m(e).fadeOut();
                    }),
                        m(e).click(function () {
                            return m("body,html").animate({ scrollTop: 0 }, 500), !1;
                        });
                }));
        }),
        (r.LogCollapse = function () {
            m(document).on("click", ".dbmovies-log-collapse", function () {
                return m(this).toggleClass("expand"), m("#dbmovies-logs-box").toggleClass("expand"), !1;
            });
        }),
        (r.Auth = function () {
            m.each(["#dbmvs-forms-response", "#dbmovies-importer", "#dbmovies-apistats", ".dbmovies-tvshow-content-generator"], function (e, s) {
                m(s).length &&
                    (n
                        ? (r.ApiRemoteData(!0),
                          setInterval(function () {
                              r.ApiRemoteData(!1);
                          }, 6e4))
                        : (1 == m("#dbmovies-dbmvs-application").length && window.location.replace("admin.php?page=dbmvs-settings"), r.AuthModules()));
            }),
                m(document).on("click", ".dbdata", function () {
                    return !1;
                }),
                m(document).on("click", ".dbdata.consultor", function () {
                    return (
                        m(".dbdata").removeClass("consultor"),
                        r.ApiRemoteData(!1),
                        setTimeout(function () {
                            m(".dbdata").addClass("consultor");
                        }, 1e4),
                        !1
                    );
                });
        }),
        (r.ApiRemoteData = function (s) {
            t
                ? m.getJSON(i + "/?check=" + n, function (e) {
                      "1" == e.status
                          ? (m(".dbmvs-credits").show(),
                            S("#dbmvs-credits", e.credits),
                            S("#dbmvs-credits-used", e.used_credits),
                            S("#dbmvs-requests", e.requests),
                            1 <= e.credits || 1 == e.unlimited
                                ? 1 == s && (m("#dbmvs-logo-status").addClass("ready"), m("#dbmvs-log-indicator").after('<li class="fadein"><span class="type">dbmvs</span>' + dbmovies.welcom + "</li>"))
                                : (r.AuthModules(), m("#dbmvs-log-indicator").after('<li class="fadein bigmessage">' + u + "</li>")))
                          : (r.AuthModules(), m("#dbmvs-log-indicator").after('<li class="fadein bigmessage">' + e.error + "</li>"), m("#dbmvs-logo-status").addClass("flashit error")),
                          m(".dbmovies-logs").show();
                  })
                : (m("#dbmvs-forms-response").remove(), m(".dbmovies-logs").show(), m("#dbmvs-log-indicator").after('<li class="bounce bigmessage">' + dbmovies.nointrn + "</li>"));
        }),
        (r.AuthModules = function () {
            m("#dbmvs-forms-response").remove(), m("#dbmovies-importer").remove(), m(".dbmovies-tvshow-content-generator").remove();
        }),
        (r.IMDbUpdater = function () {
            m(document).on("click", "#dbmvs-imdb", function () {
                var e = parseInt(m(y).val());
                return (
                    m(this).hide(),
                    m("#dbmovies-li-imdb > span.spinner").show(),
                    m("#dbmovies-li-imdb > span.percentage").show(),
                    m.ajax({
                        url: d,
                        type: "POST",
                        dataType: "json",
                        data: { page: e, action: "dbmovies_app_upimdb" },
                        success: function (r) {
                            var d = [];
                            1 == r.response
                                ? (m(y).val(++e),
                                  m(".progressing").animate({ width: r.prgss + "%" }, 50, function () {}),
                                  m("#dbmovies-li-imdb > span.percentage").html(r.prgss + "%"),
                                  m.each(r.imdb, function (e, s) {
                                      var t = s.imdb,
                                          a = s.rating + " - ",
                                          o = (s.votes, s.title),
                                          i = s.plink,
                                          n = '<span class="type">' + s.imdb + "</span>",
                                          i = '<a href="' + i + '">' + o + "</a>",
                                          o = '<span class="microtime">' + r.mtime + "</span>";
                                      t ? d.push('<li class="jump">' + n + a + i + o + "</li>") : d.push('<li class="jump"><span class="error">' + s.message + "</span>" + o + "</li>");
                                  }),
                                  m("#dbmvs-log-indicator").after(d.join("")),
                                  setTimeout(function () {
                                      m("#dbmvs-imdb").trigger("click");
                                  }, 50))
                                : (m("#dbmovies-li-imdb > span.percentage").hide(),
                                  m("#dbmovies-li-imdb > span.spinner").hide(),
                                  m("#dbmvs-imdb").show(),
                                  m(".progressing").animate({ width: "0%" }, 4e3, function () {}),
                                  setTimeout(function () {
                                      location.reload();
                                  }, 4500));
                        },
                    }),
                    !1
                );
            });
        }),
        (r.CodeStarTab = function () {
            e && (m(".cs-section").hide(), m(".cs-nav ul a").removeClass("cs-section-active"), m("#tab-" + e).addClass("cs-section-active"), m("#cs-tab-" + e).show());
        }),
        (r.ReubicateForm = function () {
            var e,
                s = "#dbmvoies-importer-html";
            1 == m(s).length && ((e = m(s).html()), m(".wp-header-end").after(e), m(s).remove());
        }),
        (r.Application = function () {
            var e = r.getCookie("_dbmvs_tabapp"),
                s = ".dbmvs-tab-content";
            1 == m("#dbmovies-dbmvs-application").length &&
                setTimeout(function () {
                    m("#dbmvstabapp-" + e).trigger("click");
                }, 1),
                m(document).on("click", s, function () {
                    var e;
                    return (
                        m(this).hasClass("button-primary") ||
                            ((e = m(this).data("type")),
                            m(".genres").removeClass("on"),
                            m("#genres-box-" + e).addClass("on"),
                            m("#dbmvs-filter-type").val(e),
                            m("#dbmvs-search-type").val(e),
                            m(s).removeClass("button-primary"),
                            m(this).addClass("button-primary"),
                            r.FixingApiResults(),
                            r.setCookie("_dbmvs_tabapp", e, 30)),
                        !1
                    );
                });
        }),
        (r.Cimport = function () {
            m.each(["#dbmvs-year", "#dbmvs-movies-genres", "#dbmvs-tvshows-genres", "#dbmvs-filter-type", "#dbmvs-popularity", "#dbmvs-search-term"], function (e, s) {
                m(document).on("change", s, function () {
                    r.FixingApiResults();
                });
            }),
                m(document).on("click", "#bulk-importer", function () {
                    return (
                        m("#bulk-importer-click").hide(),
                        m(".cimport").each(function () {
                            var e = m(this),
                                s = e.data("type"),
                                t = e.data("tmdb");
                            return e.hide(), e.removeClass("cimport"), m("#tmdb-" + t).removeClass("fadein"), m("#tmdb-" + t).removeClass("non-existent"), m("#tmdb-" + t).addClass("onload jump"), r.TMDbAppImport(s, t, !0, a), !1;
                        }),
                        !1
                    );
                }),
                m(document).on("click", ".cimport", function () {
                    var e = m(this),
                        s = e.data("type"),
                        t = e.data("tmdb");
                    return e.hide(), e.removeClass("cimport"), m("#tmdb-" + t).removeClass("fadein"), m("#tmdb-" + t).removeClass("non-existent"), m("#tmdb-" + t).addClass("onload jump"), r.TMDbAppImport(s, t, !1, !1), !1;
                });
        }),
        (r.FixingApiResults = function () {
            m("#dbmovies-response-data").hide(),
                m("#dbmovies-loadmore").removeClass("search"),
                m("#dbmovies-loadmore").removeClass("filter"),
                m("#dbmvs-results-number").html(0),
                m("#bulk-importer-click").hide(),
                m("#dbmovies-loadmore").hide(),
                m("#dbmvs-page").val(1),
                m("#dbmvs-search-page").val(1),
                m("#dbmvs-total-results").text(0),
                m("#time-execution-seconds").text(0),
                m("#dbmvs-current-page").text(0),
                m(".item").remove();
        }),
        (r.TMDbAppImport = function (e, s, t, a) {
            t &&
                !a &&
                setTimeout(function () {
                    m("#bulk-importer").trigger("click");
                }, o),
                m.ajax({
                    url: d,
                    type: "POST",
                    dataType: "json",
                    data: { ptype: e, ptmdb: s, action: "dbmovies_insert_tmdb" },
                    success: function (e) {
                        r.ComposeItemLog(e),
                            1 == e.response
                                ? (m("#tmdb-" + s).removeClass("onload"), m("#tmdb-" + s).removeClass("fadein"), m("#tmdb-" + s).addClass("existent bounce"), a && m("#bulk-importer").trigger("click"))
                                : (m("#tmdb-" + s).removeClass("onload"), m("#tmdb-" + s).addClass("error"), m("#cimprt-" + s).addClass("cimport"), m("#cimprt-" + s).show());
                    },
                });
        }),
        (r.Search = function () {
            m(document).on("submit", "#dbmovies-form-search", function () {
                return (
                    m("#dbmvs-btn-search").prop("disabled", !0),
                    m("#dbmovies-loadmore").addClass("search disabled"),
                    m("#dbmovies-loadmore").hide(),
                    m("#dbmovies-loadmore-spinner").show(),
                    m.ajax({
                        url: d,
                        type: "POST",
                        dataType: "json",
                        data: m(this).serialize(),
                        success: function (e) {
                            var s = parseInt(m("#dbmvs-search-page").val());
                            m("#current-page").val(e.page),
                                m("#current-year").val(e.year),
                                m("#dtotal-items").val(e.items),
                                m("#dbmovies-response-data").show(),
                                m("#dbmvs-total-results").text(r.NumberFormat(e.total)),
                                m("#time-execution-seconds").text(e.mtime),
                                m("#dbmvs-current-page").text(e.page),
                                m("#dbmovies-loadmore-spinner").hide(),
                                m("#dbmovies-loadmore").removeClass("disabled"),
                                r.ComposeFilterLog(e),
                                1 == e.response && (s < e.pages ? m("#dbmovies-loadmore").show() : m("#dbmovies-loadmore").removeClass("search"), m("#dbmvs-btn-search").prop("disabled", !1), r.ComposeResults(e.results)),
                                0 == e.response && r.FixingApiResults();
                        },
                    }),
                    !1
                );
            });
        }),
        (r.Filter = function () {
            m("#current-page").val(), m("#current-year").val();
            m(document).on("submit", "#dbmovies-form-filter", function () {
                return (
                    m("#bulk-importer-click").hide(),
                    m("#dbmovies-loadmore").hide(),
                    m("#dbmovies-loadmore").addClass("filter disabled"),
                    m("#dbmvs-btn-filter").prop("disabled", !0),
                    m("#dbmovies-loadmore-spinner").show(),
                    m.ajax({
                        url: d,
                        type: "POST",
                        dataType: "json",
                        data: m(this).serialize(),
                        success: function (e) {
                            var s = parseInt(m("#dbmvs-page").val());
                            m("#current-page").val(e.page),
                                m("#current-year").val(e.year),
                                m("#dtotal-items").val(e.items),
                                m("#dbmovies-response-data").show(),
                                m("#dbmovies-loadmore").removeClass("disabled"),
                                m("#dbmvs-total-results").text(r.NumberFormat(e.total)),
                                m("#time-execution-seconds").text(e.mtime),
                                m("#dbmvs-current-page").text(e.page),
                                r.ComposeFilterLog(e),
                                m("#dbmovies-loadmore-spinner").hide(),
                                1 == e.response && (s < e.pages && m("#dbmovies-loadmore").show(), m("#bulk-importer-click").show(), m("#dbmvs-btn-filter").prop("disabled", !1), r.ComposeResults(e.results)),
                                0 == e.response && (alert(e.message), m("#dbmvs-btn-filter").prop("disabled", !1), r.FixingApiResults());
                        },
                    }),
                    !1
                );
            });
        }),
        (r.ComposeResults = function (e) {
            var o = [];
            m.each(e, function (e, s) {
                var t, a;
                m("#tmdb-" + s.id).length ||
                    ((a = ""),
                    "non-existent" == s.db && (a = '<div id="cimprt-' + s.id + '" class="cimport" data-tmdb="' + s.id + '" data-type="' + s.tp + '"></div>'),
                    (t = '<div class="image"><img src="' + s.im + '">' + a + "</div>"),
                    (a = '<div class="data"><h3>' + s.ti + "</h3><span>" + s.dt + "</span></div>"),
                    o.push('<div class="item fadein"><article class="' + s.db + '" id="tmdb-' + s.id + '">' + t + a + "</article></div>"));
            }),
                m("#response-dbmovies").before(o.join(""));
        }),
        (r.ComposeItemLog = function (e) {
            var s,
                t,
                a,
                o = "";
            (o =
                1 == e.response
                    ? ((s = '<span class="type green">' + e.status + "</span>"),
                      (t = '<span class="type">' + e.type + "</span>"),
                      (a = '<a href="' + e.permalink + '" target="_blank">' + e.title + "</a>"),
                      s + t + ('<span class="edit"><a href="' + e.editlink + '" target="_blank">' + dbmovies.editxt + "</a></span>") + a + ('<span class="microtime">' + e.mtime + "</span>"))
                    : e.message),
                m("#dbmvs-log-indicator").after('<li class="bounce">' + o + "</li>");
        }),
        (r.ComposeFilterLog = function (e) {
            var s,
                t,
                a = "";
            (a = 1 == e.response ? ((s = '<span class="type green">' + e.type + "</span>"), (t = '<span class="microtime">' + e.mtime + "</span>"), s + dbmovies.complt + t) : e.message),
                m("#dbmvs-log-indicator").after('<li class="fadein">' + a + "</li>");
        }),
        (r.LoadMore = function () {
            m.each([".dbmvsloadmore.search", ".dbmvsloadmore.filter"], function (e, t) {
                m(document).on("click", t, function () {
                    var e;
                    return (
                        ".dbmvsloadmore.filter" == t && (e = parseInt(m("#dbmvs-page").val())),
                        ".dbmvsloadmore.search" == t && (e = parseInt(m("#dbmvs-page").val())),
                        e &&
                            !m(this).hasClass("disabled") &&
                            (".dbmvsloadmore.filter" == t && (m("#dbmvs-page").val(++e), m("#dbmvs-btn-filter").trigger("click")), ".dbmvsloadmore.search" == t && (m("#dbmvs-search-page").val(++e), m("#dbmvs-btn-search").trigger("click"))),
                        !1
                    );
                }),
                    s &&
                        m(window).scroll(function () {
                            var e = m(this),
                                s = m(".wrapp");
                            m("#dtotal-items").val() < parseInt(v) &&
                                e.scrollTop() >= s.height() - e.height() + 240 &&
                                (m("#dbmovies-loadmore").hasClass("disabled") ||
                                    setTimeout(function () {
                                        m(t).trigger("click");
                                    }, 500));
                        });
            });
        }),
        (r.Importer = function () {
            1 == m(_).length &&
                m(document).on("submit", _, function () {
                    var s = "#dbmovies-btn-importer",
                        t = "#dbmovies-inp-tmdb";
                    return (
                        m(s).prop("disabled", !0),
                        m(t).addClass("disabled"),
                        m(s).val(dbmovies.loading),
                        m(".wp-list-table").addClass("onprogress"),
                        m(_).addClass("onprogress"),
                        m("#dbmovies-loader").addClass("is-active"),
                        m.ajax({
                            url: d,
                            type: "POST",
                            dataType: "json",
                            data: m(this).serialize(),
                            success: function (e) {
                                1 == e.response
                                    ? window.location.replace(e.editlink)
                                    : (alert(e.message),
                                      m(s).prop("disabled", !1),
                                      m(t).removeClass("disabled"),
                                      m(s).val(dbmovies.import),
                                      m(_).removeClass("onprogress"),
                                      m(".wp-list-table").removeClass("onprogress"),
                                      m("#dbmovies-loader").removeClass("is-active"));
                            },
                        }),
                        !1
                    );
                });
        }),
        (r.GeneratorSeasons = function () {
            var i = m("#ids").val();
            m(document).on("click", "#dbmovies-generator-seprepa", function () {
                var e = m("#tmdbseasos").val(),
                    s = m("#tmdbsename").val();
                return (
                    m(this).prop("disabled", !0),
                    e ||
                        s ||
                        m.getJSON(l + "/tv/" + i + "?language=" + p + "&api_key=" + c, function (e) {
                            var s = e.name || e.original_name;
                            m("#tmdbsename").val(s),
                                m.each(e, function (e, s) {
                                    "number_of_seasons" == e && (m("#tmdbseasos").val(s), m("#number_of_seasons").val(s), m("#dbmovies-json-response").html("0/" + s)), "number_of_episodes" == e && m("#number_of_episodes").val(s);
                                }),
                                m("#dbmovies-generator-seprepa").removeClass("button-primary"),
                                m(C).removeClass("button-secundary"),
                                m(C).addClass("button-primary"),
                                m(C).prop("disabled", !1);
                        }),
                    !1
                );
            }),
                m(document).on("click", C, function () {
                    var e = parseInt(m("#tmdbseason").val()),
                        s = parseInt(m("#tmdbseasos").val()),
                        t = m("#tmdbsename").val(),
                        a = m("#postparent").val(),
                        o = ((e - 1) / s) * 100;
                    return (
                        m(this).prop("disabled", !0),
                        m(this).hide(300),
                        m("#dbmovies-loader").addClass("is-active"),
                        r.Progress(o),
                        e <= s
                            ? (m("#tmdbseason").val(e + 1),
                              m(f).html(e + "/" + s),
                              m.ajax({
                                  url: d,
                                  type: "POST",
                                  dataType: "json",
                                  data: { tmdb: i, tmse: e, name: t, post: a, action: "dbmovies_generate_se" },
                                  success: function (e) {
                                      1 == e.response
                                          ? (console.log(e.title + " - " + e.mtime),
                                            m(C).prop("disabled", !1),
                                            setTimeout(function () {
                                                m(C).trigger("click");
                                            }, 75))
                                          : location.reload();
                                  },
                              }))
                            : location.reload(),
                        !1
                    );
                });
        }),
        (r.GeneratorEpisodes = function () {
            var i = m("#ids").val(),
                n = m("#temporada").val();
            m(document).on("click", "#dbmovies-generator-epprepa", function () {
                m("#tmdbepisos").val(), m("#tmdbsename").val();
                return (
                    m(this).prop("disabled", !0),
                    m.getJSON(l + "/tv/" + i + "?language=" + p + "&api_key=" + c, function (a) {
                        m.each(a.seasons, function (e, s) {
                            var t;
                            n == s.season_number && ((t = a.name || a.original_name), m("#tmdbsename").val(t), m("#tmdbepisos").val(s.episode_count), m("#dbmovies-json-response").html("0/" + s.episode_count));
                        }),
                            m("#dbmovies-generator-epprepa").removeClass("button-primary"),
                            m(k).removeClass("button-secundary"),
                            m(k).addClass("button-primary"),
                            m(k).prop("disabled", !1),
                            "all" == b &&
                                setTimeout(function () {
                                    m(k).trigger("click");
                                }, 1500);
                    }),
                    !1
                );
            }),
                m(document).on("click", k, function () {
                    var e = parseInt(m("#tmdbepisod").val()),
                        s = parseInt(m("#tmdbepisos").val()),
                        t = m("#tmdbsename").val(),
                        a = m("#postparent").val(),
                        o = ((e - 1) / s) * 100;
                    return (
                        m(this).prop("disabled", !0),
                        m(this).hide(300),
                        m("#dbmovies-loader").addClass("is-active"),
                        r.Progress(o),
                        e <= s
                            ? (m("#tmdbepisod").val(e + 1),
                              m(f).html(e + "/" + s),
                              m.ajax({
                                  url: d,
                                  type: "POST",
                                  dataType: "json",
                                  data: { tmdb: i, tmse: n, tmep: e, name: t, post: a, action: "dbmovies_generate_ep" },
                                  success: function (e) {
                                      1 == e.response
                                          ? (console.log(e.title + " - " + e.mtime),
                                            m(k).prop("disabled", !1),
                                            setTimeout(function () {
                                                m(k).trigger("click");
                                            }, 75))
                                          : "all" == b
                                          ? window.close()
                                          : location.reload();
                                  },
                              }))
                            : "all" == b
                            ? window.close()
                            : location.reload(),
                        !1
                    );
                });
        }),
        (r.Progress = function (e) {
            m("#dbmovies-progress").animate({ width: e + "%" }, 150, function () {});
        }),
        (r.NumberFormat = function (e) {
            (x = (e += "").split(".")), (x1 = x[0]), (x2 = 1 < x.length ? "." + x[1] : "");
            for (var s = /(\d+)(\d{3})/; s.test(x1); ) x1 = x1.replace(s, "$1,$2");
            return x1 + x2;
        }),
        (r.setCookie = function (e, s, t) {
            var a = new Date();
            a.setTime(a.getTime() + 24 * t * 60 * 60 * 1e3);
            a = "expires=" + a.toUTCString();
            document.cookie = e + "=" + s + ";" + a + ";path=/";
        }),
        (r.getCookie = function (e) {
            for (var s = e + "=", t = decodeURIComponent(document.cookie).split(";"), a = 0; a < t.length; a++) {
                for (var o = t[a]; 0 == o.charAt(0); ) o = o.substring(1);
                if (0 == o.indexOf(s)) return o.substring(s.length, o.length);
            }
            return !1;
        }),
        (r.delCookie = function (e) {
            document.cookie = e + "=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";
        });
    var E = {
        numberStep: function (e, s) {
            e = Math.floor(e);
            m(s.elem).text(e);
        },
    };
    m.Tween && m.Tween.propHooks ? (m.Tween.propHooks.number = { set: T }) : (m.fx.step.number = T),
        (m.animateNumber = {
            numberStepFactories: {
                append: function (a) {
                    return function (e, s) {
                        var t = Math.floor(e);
                        m(s.elem)
                            .prop("number", e)
                            .text(t + a);
                    };
                },
                separator: function (r, d, l) {
                    return (
                        (r = r || " "),
                        (d = d || 3),
                        (l = l || ""),
                        function (e, s) {
                            var t,
                                a,
                                o = e < 0,
                                i = Math.floor((o ? -1 : 1) * e).toString(),
                                n = m(s.elem);
                            i.length > d &&
                                ((a = (function (e, s) {
                                    for (var t, a, o, i = e.split("").reverse(), n = [], r = 0, d = Math.ceil(e.length / s); r < d; r++) {
                                        for (t = "", o = 0; o < s && (a = r * s + o) !== e.length; o++) t += i[a];
                                        n.push(t);
                                    }
                                    return n;
                                })(i, d)),
                                (s = (t = a).length - 1),
                                (a = w(t[s])),
                                (t[s] = w(parseInt(a, 10).toString())),
                                (i = t.join(r)),
                                (i = w(i))),
                                n.prop("number", e).text((o ? "-" : "") + i + l);
                        }
                    );
                },
            },
        }),
        (m.fn.animateNumber = function () {
            for (var e, s, t = arguments[0], a = m.extend({}, E, t), o = m(this), i = [a], n = 1, r = arguments.length; n < r; n++) i.push(arguments[n]);
            return (
                t.numberStep &&
                    ((e = this.each(function () {
                        this._animateNumberSetter = t.numberStep;
                    })),
                    (s = a.complete),
                    (a.complete = function () {
                        e.each(function () {
                            delete this._animateNumberSetter;
                        }),
                            s && s.apply(this, arguments);
                    })),
                o.animate.apply(o, i)
            );
        });
})(jQuery);
