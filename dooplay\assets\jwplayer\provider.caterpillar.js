webpackJsonpjwplayer([1],[,,,,,function(e,t,n){"use strict";var r=n(34),i=n(96),a=n(97);r.operations=i,r.operations.pipeline=a.pipeline,r.operations.pipelineAsync=a.pipelineAsync,e.exports=r},,function(e,t,n){(function(t){var r="object"==typeof t?t:"object"==typeof window?window:"object"==typeof self?self:this,i=r.regeneratorRuntime&&Object.getOwnPropertyNames(r).indexOf("regeneratorRuntime")>=0,a=i&&r.regeneratorRuntime;if(r.regeneratorRuntime=void 0,e.exports=n(208),i)r.regeneratorRuntime=a;else try{delete r.regeneratorRuntime}catch(s){r.regeneratorRuntime=void 0}}).call(t,function(){return this}())},,,,,function(e,t,n){"use strict";function r(e,t){if(1===e.length)return e[0];for(var n=t||f.reduce(e,function(e,t){return e+t.byteLength},0),r=new Uint8Array(n),i=0,a=0;a<e.length;a++)r.set(e[a],i),i+=e[a].length;return r}function i(e,t){var n=new Uint8Array(e.byteLength+t.byteLength);return n.set(e),n.set(t,e.byteLength),n}function a(e,t){var n=new Uint8Array(e.byteLength+t);return n.set(e),n}function s(e,t,n){e[t]=n>>>24&255,e[t+1]=n>>>16&255,e[t+2]=n>>>8&255,e[t+3]=255&n}function o(e,t){var n=e[t],r=e[t+1],i=e[t+2],a=e[t+3];return n<<24|r<<16|i<<8|a}function u(e,t){var n=e[t],r=e[t+1];return n<<8|r}function c(e){return f.map(e,function(e){return e.toString(16)})}t.__esModule=!0,t.combineUint8Arrays=r,t.concatUint8Array=i,t.extendUint8Array=a,t.writeUint32=s,t.readUint32=o,t.readUint16=u,t.toHex=c;var f=n(1)},,,function(e,t,n){var r,i;r=[n(1),n(18),n(2),n(23),n(16)],i=function(e,t,n,r,i){function a(t){if(this._currentTextTrackIndex=-1,t){if(this._renderNatively=i.renderNatively(this.getName().name),this._textTracks?this._textTracks=e.reject(this._textTracks,function(e){if(this._renderNatively&&"nativecaptions"===e._id||"metadata"===e.kind)return this._tracksById[e._id]=null,!0},this):this._initTextTracks(),t.length){var r=0,a=t.length;for(r;r<a;r++){var s=t[r];if(!s._id){if("captions"===s.kind||"metadata"===s.kind){if(s._id="native"+s.kind,!s.label&&"captions"===s.kind){var o=i.createLabel(s,this._unknownCount);s.name=o.label,this._unknownCount=o.unknownCount}}else s._id=i.createId(s,this._textTracks.length);s.inuse=!0}if(s.inuse&&!this._tracksById[s._id])if("metadata"===s.kind)s.mode="hidden",s.oncuechange=A.bind(this),this._tracksById[s._id]=s;else if(k(s.kind)){var u,c=s.mode;if(s.mode="hidden",!s.cues.length&&s.embedded)continue;if(s.mode=c,this._cuesByTrackId[s._id]&&!this._cuesByTrackId[s._id].loaded){for(var f=this._cuesByTrackId[s._id].cues;u=f.shift();)T(s,u);s.mode=c,this._cuesByTrackId[s._id].loaded=!0}E.call(this,s)}}}this._renderNatively&&(this.textTrackChangeHandler=this.textTrackChangeHandler||y.bind(this),this.addTracksListener(this.video.textTracks,"change",this.textTrackChangeHandler),n.isEdge()&&(this.addTrackHandler=this.addTrackHandler||g.bind(this),this.addTracksListener(this.video.textTracks,"addtrack",this.addTrackHandler))),this._textTracks.length&&this.trigger("subtitlesTracks",{tracks:this._textTracks})}}function s(e){if(this._renderNatively=i.renderNatively(this.getName().name),this._renderNatively){var t=e===this._itemTracks;t||r.cancelXhr(this._itemTracks),this._itemTracks=e,e&&(t||(this.disableTextTrack(),I.call(this),this.addTextTracks(e)))}}function o(){return this._currentTextTrackIndex}function u(t){this._textTracks&&(0===t&&e.each(this._textTracks,function(e){e.mode=e.embedded?"hidden":"disabled"}),this._currentTextTrackIndex!==t-1&&(this.disableTextTrack(),this._currentTextTrackIndex=t-1,this._renderNatively&&(this._textTracks[this._currentTextTrackIndex]&&(this._textTracks[this._currentTextTrackIndex].mode="showing"),this.trigger("subtitlesTrackChanged",{currentTrack:this._currentTextTrackIndex+1,tracks:this._textTracks}))))}function c(e){if(e.text&&e.begin&&e.end){var t=e.trackid.toString(),n=this._tracksById&&this._tracksById[t];n||(n={kind:"captions",_id:t,data:[]},this.addTextTracks([n]),this.trigger("subtitlesTracks",{tracks:this._textTracks}));var i;e.useDTS&&(n.source||(n.source=e.source||"mpegts")),i=e.begin+"_"+e.text;var a=this._metaCuesByTextTime[i];if(!a){a={begin:e.begin,end:e.end,text:e.text},this._metaCuesByTextTime[i]=a;var s=r.convertToVTTCues([a])[0];n.data.push(s)}}}function f(e){this._tracksById||this._initTextTracks();var t="native"+e.type,n=this._tracksById[t],r="captions"===e.type?"Unknown CC":"ID3 Metadata",i=e.cue;if(!n){var a={kind:e.type,_id:t,label:r,embedded:!0};n=S.call(this,a),this._renderNatively||"metadata"===n.kind?this.setTextTracks(this.video.textTracks):_.call(this,[n])}M.call(this,n,i)&&(this._renderNatively||"metadata"===n.kind?T(n,i):n.data.push(i))}function l(e){var t=this._tracksById[e.name];if(t){t.source=e.source;for(var n=e.captions||[],i=[],a=!1,s=0;s<n.length;s++){var o=n[s],u=e.name+"_"+o.begin+"_"+o.end;this._metaCuesByTextTime[u]||(this._metaCuesByTextTime[u]=o,i.push(o),a=!0)}a&&i.sort(function(e,t){return e.begin-t.begin});var c=r.convertToVTTCues(i);Array.prototype.push.apply(t.data,c)}}function d(e,t,n){e&&(h(e,t,n),this.instreamMode||(e.addEventListener?e.addEventListener(t,n):e["on"+t]=n))}function h(e,t,n){e&&(e.removeEventListener?e.removeEventListener(t,n):e["on"+t]=null)}function p(){r.cancelXhr(this._itemTracks);var e=this._tracksById&&this._tracksById.nativemetadata;(this._renderNatively||e)&&(x.call(this,this.video.textTracks),e&&(e.oncuechange=null)),this._itemTracks=null,this._textTracks=null,this._tracksById=null,this._cuesByTrackId=null,this._metaCuesByTextTime=null,this._unknownCount=0,this._activeCuePosition=null,this._renderNatively&&(this.removeTracksListener(this.video.textTracks,"change",this.textTrackChangeHandler),x.call(this,this.video.textTracks))}function m(){if(this._textTracks){var e=this._textTracks[this._currentTextTrackIndex];e&&(e.mode=e.embedded||"nativecaptions"===e._id?"hidden":"disabled")}}function v(){if(this._textTracks){var e=this._textTracks[this._currentTextTrackIndex];e&&(e.mode="showing")}}function y(){var t=this.video.textTracks,n=e.filter(t,function(e){return(e.inuse||!e._id)&&k(e.kind)});if(!this._textTracks||C.call(this,n))return void this.setTextTracks(t);var r=-1,i=0;for(i;i<this._textTracks.length;i++)if("showing"===this._textTracks[i].mode){r=i;break}r!==this._currentTextTrackIndex&&this.setSubtitlesTrack(r+1)}function g(){this.setTextTracks(this.video.textTracks)}function _(e){if(e){this._textTracks||this._initTextTracks(),this._renderNatively=i.renderNatively(this.getName().name);for(var t=0;t<e.length;t++){var n=e[t];if(!n.kind||k(n.kind)){var a=S.call(this,n);E.call(this,a),n.file&&(n.data=[],r.loadFile(n,this.addVTTCuesToTrack.bind(this,a),L))}}!this._renderNatively&&this._textTracks&&this._textTracks.length&&this.trigger("subtitlesTracks",{tracks:this._textTracks})}}function b(e,t){if(this._renderNatively){var n=this._tracksById[e._id];if(!n)return this._cuesByTrackId||(this._cuesByTrackId={}),void(this._cuesByTrackId[e._id]={cues:t,loaded:!1});if(!this._cuesByTrackId[e._id]||!this._cuesByTrackId[e._id].loaded){var r;for(this._cuesByTrackId[e._id]={cues:t,loaded:!0};r=t.shift();)T(n,r)}}}function T(e,t){if(!n.isEdge()||!window.TextTrackCue)return void e.addCue(t);var r=new window.TextTrackCue(t.startTime,t.endTime,t.text);e.addCue(r)}function x(t){t.length&&e.each(t,function(e){e.mode="hidden";for(var t=e.cues.length;t--;)e.removeCue(e.cues[t]);e.embedded||(e.mode="disabled"),e.inuse=!1})}function k(e){return"subtitles"===e||"captions"===e}function w(){this._textTracks=[],this._tracksById={},this._metaCuesByTextTime={},this._cuesByTrackId={},this._cachedVTTCues={},this._unknownCount=0}function S(t){var n,r=i.createLabel(t,this._unknownCount),a=r.label;if(this._unknownCount=r.unknownCount,this._renderNatively||"metadata"===t.kind){var s=this.video.textTracks;n=e.findWhere(s,{label:a}),n?(n.kind=t.kind,n.language=t.language||""):n=this.video.addTextTrack(t.kind,a,t.language||""),n["default"]=t["default"],n.mode="disabled",n.inuse=!0}else n=t,n.data=n.data||[];return n._id||(n._id=i.createId(t,this._textTracks.length)),n}function E(e){this._textTracks.push(e),this._tracksById[e._id]=e}function I(){if(this._textTracks){var t=e.filter(this._textTracks,function(e){return e.embedded||"subs"===e.groupid});this._initTextTracks(),e.each(t,function(e){this._tracksById[e._id]=e}),this._textTracks=t}}function A(n){var r=n.currentTarget.activeCues;if(r&&r.length){var i=r[r.length-1].startTime;if(this._activeCuePosition!==i){var a=[];if(e.each(r,function(e){e.startTime<i||(e.data||e.value?a.push(e):e.text&&this.trigger("meta",{metadataTime:i,metadata:JSON.parse(e.text)}))},this),a.length){var s=t.parseID3(a);this.trigger("meta",{metadataTime:i,metadata:s})}this._activeCuePosition=i}}}function M(e,t){var n=e.kind;this._cachedVTTCues[e._id]||(this._cachedVTTCues[e._id]={});var r,i=this._cachedVTTCues[e._id];switch(n){case"captions":r=Math.floor(20*t.startTime);var a=Math.floor(20*t.endTime),s=i[r]||i[r+1]||i[r-1];return!(s&&Math.abs(s-a)<=1)&&(i[r]=a,!0);case"metadata":var o=t.data?new Uint8Array(t.data).join(""):t.text;return r=t.startTime+o,!i[r]&&(i[r]=t.endTime,!0)}}function C(e){if(e.length>this._textTracks.length)return!0;for(var t=0;t<e.length;t++){var n=e[t];if(!n._id||!this._tracksById[n._id])return!0}return!1}function L(e){n.log("CAPTIONS("+e+")")}var P={_itemTracks:null,_textTracks:null,_tracksById:null,_cuesByTrackId:null,_cachedVTTCues:null,_metaCuesByTextTime:null,_currentTextTrackIndex:-1,_unknownCount:0,_renderNatively:!1,_activeCuePosition:null,_initTextTracks:w,addTracksListener:d,clearTracks:p,disableTextTrack:m,enableTextTrack:v,getSubtitlesTrack:o,removeTracksListener:h,addTextTracks:_,setTextTracks:a,setupSideloadedTracks:s,setSubtitlesTrack:u,textTrackChangeHandler:null,addTrackHandler:null,addCuesToTrack:l,addCaptionsCue:c,addVTTCue:f,addVTTCuesToTrack:b};return P}.apply(t,r),!(void 0!==i&&(e.exports=i))},,,,,,function(e,t,n){"use strict";function r(e){return e&&e["@@transducer/reduced"]}function i(e,t){f.run(function(){e(t)})}function a(e){return console.log("error in channel transformer",e.stack),h}function s(e,t,n){var r=(t||a)(n);return r!==h&&e.add(r),e}function o(){}function u(e){return function(t){return{"@@transducer/step":function(n,r){try{return t["@@transducer/step"](n,r)}catch(i){return s(n,e,i)}},"@@transducer/result":function(n){try{return t["@@transducer/result"](n)}catch(r){return s(n,e,r)}}}}}var c=n(35),f=n(36),l=64,d=1024,h=null,p=function(e){this.value=e},m=function(e,t){this.handler=e,this.value=t},v=function(e,t,n,r){this.buf=n,this.xform=r,this.takes=e,this.puts=t,this.dirty_takes=0,this.dirty_puts=0,this.closed=!1};v.prototype._put=function(e,t){if(e===h)throw new Error("Cannot put CLOSED on a channel.");if(!t.is_active())return null;if(this.closed)return t.commit(),new p((!1));var n,a;if(this.buf&&!this.buf.is_full()){t.commit();for(var s=r(this.xform["@@transducer/step"](this.buf,e));;){if(0===this.buf.count())break;if(n=this.takes.pop(),n===c.EMPTY)break;n.is_active()&&(e=this.buf.remove(),a=n.commit(),i(a,e))}return s&&this.close(),new p((!0))}for(;;){if(n=this.takes.pop(),n===c.EMPTY)break;if(n.is_active())return t.commit(),a=n.commit(),i(a,e),new p((!0))}if(this.dirty_puts>l?(this.puts.cleanup(function(e){return e.handler.is_active()}),this.dirty_puts=0):this.dirty_puts++,t.is_blockable()){if(this.puts.length>=d)throw new Error("No more than "+d+" pending puts are allowed on a single channel.");this.puts.unbounded_unshift(new m(t,e))}return null},v.prototype._take=function(e){if(!e.is_active())return null;var t,n,a,s;if(this.buf&&this.buf.count()>0){for(e.commit(),s=this.buf.remove();;){if(this.buf.is_full())break;if(t=this.puts.pop(),t===c.EMPTY)break;n=t.handler,n.is_active()&&(a=n.commit(),a&&i(a,!0),r(this.xform["@@transducer/step"](this.buf,t.value))&&this.close())}return new p(s)}for(;;){if(t=this.puts.pop(),s=t.value,t===c.EMPTY)break;if(n=t.handler,n.is_active())return e.commit(),a=n.commit(),a&&i(a,!0),new p(s)}if(this.closed)return e.commit(),new p(h);if(this.dirty_takes>l?(this.takes.cleanup(function(e){return e.is_active()}),this.dirty_takes=0):this.dirty_takes++,e.is_blockable()){if(this.takes.length>=d)throw new Error("No more than "+d+" pending takes are allowed on a single channel.");this.takes.unbounded_unshift(e)}return null},v.prototype.close=function(){if(!this.closed){if(this.closed=!0,this.buf)for(this.xform["@@transducer/result"](this.buf);;){if(0===this.buf.count())break;if(t=this.takes.pop(),t===c.EMPTY)break;if(t.is_active()){n=t.commit();var e=this.buf.remove();i(n,e)}}for(;;){var t=this.takes.pop();if(t===c.EMPTY)break;if(t.is_active()){var n=t.commit();i(n,h)}}for(;;){var r=this.puts.pop();if(r===c.EMPTY)break;if(r.handler.is_active()){var a=r.handler.commit();a&&i(a,!1)}}}},v.prototype.is_closed=function(){return this.closed},o.prototype["@@transducer/init"]=function(){throw new Error("init not available")},o.prototype["@@transducer/result"]=function(e){return e},o.prototype["@@transducer/step"]=function(e,t){return e.add(t),e},t.chan=function(e,t,n){if(t){if(!e)throw new Error("Only buffered channels can use transducers");t=t(new o)}else t=new o;return t=u(n)(t),new v(c.ring(32),c.ring(32),e,t)},t.Box=p,t.Channel=v,t.CLOSED=h},,,,,,,,,function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var i=n(1),a=function(){function e(){r(this,e)}return e.isTimeBuffered=function(t,n){for(var r={start:n,end:n},i=0;i<t.length;i++)if(e.doRangesOverlap(e.timeRangeToNumberRange(t,i),r,!0))return!0;return!1},e.isSegmentTimesBuffered=function(t,n){for(var r=0;r<t.length;r++){var i=e.timeRangeToNumberRange(t,r);if(e.doRangesOverlap(i,n,!0)&&e.getRangeOverlap(i,n)/n.duration>.95)return!0}return!1},e.getRanges=function(t,n,r){var a=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return i.filter(t,function(t){return e.doRangesOverlap({start:n,end:r},t,a)})},e.timeRangeToNumberRange=function(e,t){return{start:e.start(t),end:e.end(t)}},e.doRangesOverlap=function(e,t,n){return n?e.start<=t.end&&e.end>=t.start:e.start<t.end&&e.end>t.start},e.getRangeOverlap=function(e,t){var n=e.start<t.start?e:t,r=n===t?e:t,i=n.end-r.start;return i>0?i:0},e}();t["default"]=a},,,,function(e,t,n){"use strict";function r(e,t){var n=o.chan(s.fixed(1));return new c.Process(e,function(e){e===o.CLOSED?n.close():c.put_then_callback(n,e,function(e){n.close()})},t).run(),n}function i(e,t){t=t||[];var n=e.apply(null,t);return r(n,e)}function a(e,t,n){var r;return 0===e&&(e=null),r="number"==typeof e?s.fixed(e):e,o.chan(r,t,n)}var s=n(35),o=n(21),u=n(57),c=n(98),f=n(99);e.exports={buffers:{fixed:s.fixed,dropping:s.dropping,sliding:s.sliding},spawn:r,go:i,chan:a,DEFAULT:u.DEFAULT,CLOSED:o.CLOSED,put:c.put,take:c.take,offer:c.offer,poll:c.poll,sleep:c.sleep,alts:c.alts,putAsync:c.put_then_callback,takeAsync:c.take_then_callback,NO_VALUE:c.NO_VALUE,timeout:f.timeout}},function(e,t){"use strict";function n(e,t,n,r,i){for(var a=0;;){if(a>=i)break;n[r+a]=e[t+a],a++}}var r={toString:function(){return"[object EMPTY]"}},i=function(e,t,n,r){this.length=n,this.array=r,this.head=e,this.tail=t};i.prototype._unshift=function(e){var t=this.array,n=this.head;t[n]=e,this.head=(n+1)%t.length,this.length++},i.prototype._resize=function(){var e=this.array,t=2*e.length,r=new Array(t),i=this.head,a=this.tail,s=this.length;a<i?(n(e,a,r,0,s),this.tail=0,this.head=s,this.array=r):a>i?(n(e,a,r,0,e.length-a),n(e,0,r,e.length-a,i),this.tail=0,this.head=s,this.array=r):a===i&&(this.tail=0,this.head=0,this.array=r)},i.prototype.unbounded_unshift=function(e){this.length+1===this.array.length&&this._resize(),this._unshift(e)},i.prototype.pop=function(){if(0===this.length)return r;var e=this.array,t=this.tail,n=e[t];return e[t]=null,this.tail=(t+1)%e.length,this.length--,n},i.prototype.cleanup=function(e){for(var t=this.length,n=0;n<t;n++){var r=this.pop();e(r)&&this._unshift(r)}};var a=function(e,t){this.buf=e,this.n=t};a.prototype.is_full=function(){return this.buf.length>=this.n},a.prototype.remove=function(){return this.buf.pop()},a.prototype.add=function(e){this.buf.unbounded_unshift(e)},a.prototype.count=function(){return this.buf.length};var s=function(e,t){this.buf=e,this.n=t};s.prototype.is_full=function(){return!1},s.prototype.remove=function(){return this.buf.pop()},s.prototype.add=function(e){this.buf.length<this.n&&this.buf._unshift(e)},s.prototype.count=function(){return this.buf.length};var o=function(e,t){this.buf=e,this.n=t};o.prototype.is_full=function(){return!1},o.prototype.remove=function(){return this.buf.pop()},o.prototype.add=function(e){this.buf.length===this.n&&this.buf.pop(),this.buf._unshift(e)},o.prototype.count=function(){return this.buf.length};var u=t.ring=function(e){return new i(0,0,0,new Array(e))};t.fixed=function(e){return new a(u(e),e)},t.dropping=function(e){return new s(u(e),e)},t.sliding=function(e){return new o(u(e),e)},t.EMPTY=r},function(e,t,n){(function(e){"use strict";function r(){u=!0,c=!1;for(var e=0;;){var t=o.pop();if(t===a.EMPTY)break;if(t(),e>=s)break;e++}u=!1,o.length>0&&i()}var i,a=n(35),s=1024,o=a.ring(32),u=!1,c=!1;if("undefined"!=typeof MessageChannel){var f=new MessageChannel;f.port1.onmessage=function(e){r()},i=function(){c&&u||(c=!0,f.port2.postMessage(0))}}else i="undefined"!=typeof e?function(){c&&u||(c=!0,e(r))}:function(){c&&u||(c=!0,setTimeout(r,0))};t.run=function(e){o.unbounded_unshift(e),i()},t.queue_delay=function(e,t){setTimeout(e,t)}}).call(t,n(50).setImmediate)},function(e,t){"use strict";t.__esModule=!0;var n,r,i,a,s,o,u,c,f,l,d,h,p,m,v,y,g,_,b,T,x,k,w,S,E,I,A,M,C,L,P,D,O,R,B,U,N;!function(){var e;S={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]};for(e in S)S.hasOwnProperty(e)&&(S[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);E=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),A=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),I=new Uint8Array([0,0,0,1]),M=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),C=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),L={video:M,audio:C},O=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),D=new Uint8Array([0,0,0,0,0,0,0,0]),R=new Uint8Array([0,0,0,0,0,0,0,0]),B=R,U=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),N=R,P=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}(),n=function(e){var t,n,r,i=[],a=0;for(t=1;t<arguments.length;t++)i.push(arguments[t]);for(t=i.length;t--;)a+=i[t].byteLength;for(n=new Uint8Array(a+8),r=new DataView(n.buffer,n.byteOffset,n.byteLength),r.setUint32(0,n.byteLength),n.set(e,4),t=0,a=8;t<i.length;t++)n.set(i[t],a),a+=i[t].byteLength;return n},r=function(){return n(S.dinf,n(S.dref,O))},i=function(e){return n(S.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},a=function(){return n(S.ftyp,E,I,E,A)},y=function(e){return n(S.hdlr,L[e])},s=function(e){return n(S.mdat,e)},v=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(t[12]=e.samplerate>>>24&255,t[13]=e.samplerate>>>16&255,t[14]=e.samplerate>>>8&255,t[15]=255&e.samplerate),n(S.mdhd,t)},m=function(e){return n(S.mdia,v(e),y(e.type),u(e))},o=function(e){return n(S.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},u=function(e){return n(S.minf,"video"===e.type?n(S.vmhd,P):n(S.smhd,D),r(),_(e))},c=function(e,t){for(var r=[],i=t.length;i--;)r[i]=x(t[i]);return n.apply(null,[S.moof,o(e)].concat(r))},f=function(e){for(var t=e.length,r=[];t--;)r[t]=h(e[t]);return n.apply(null,[S.moov,d(4294967295)].concat(r).concat(l(e)))},l=function(e){for(var t=e.length,r=[];t--;)r[t]=k(e[t]);return n.apply(null,[S.mvex].concat(r))},d=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return n(S.mvhd,t)},g=function(e){var t,r,i=e.samples||[],a=new Uint8Array(4+i.length);for(r=0;r<i.length;r++)t=i[r].flags,a[r+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return n(S.sdtp,a)},_=function(e){return n(S.stbl,b(e),n(S.stts,N),n(S.stsc,B),n(S.stsz,U),n(S.stco,R))},function(){var e,t;b=function(r){return n(S.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===r.type?e(r):t(r))},e=function(e){var t,r=e.sps||[],i=e.pps||[],a=[],s=[];for(t=0;t<r.length;t++)a.push((65280&r[t].byteLength)>>>8),a.push(255&r[t].byteLength),a=a.concat(Array.prototype.slice.call(r[t]));for(t=0;t<i.length;t++)s.push((65280&i[t].byteLength)>>>8),s.push(255&i[t].byteLength),s=s.concat(Array.prototype.slice.call(i[t]));return n(S.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),n(S.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([r.length]).concat(a).concat([i.length]).concat(s))),n(S.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))},t=function(e){return n(S.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),i(e))}}(),T=function(){return n(S.styp,E,I,E)},p=function(e){var t=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return n(S.tkhd,t)},x=function(e){var t,r,i,a,s;return t=n(S.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),r=n(S.tfdt,new Uint8Array([0,0,0,0,e.baseMediaDecodeTime>>>24&255,e.baseMediaDecodeTime>>>16&255,e.baseMediaDecodeTime>>>8&255,255&e.baseMediaDecodeTime])),s=88,"audio"===e.type?(i=w(e,s),n(S.traf,t,r,i)):(a=g(e),i=w(e,a.length+s),n(S.traf,t,r,i,a))},h=function(e){return e.duration=e.duration||4294967295,n(S.trak,p(e),m(e))},k=function(e){var t=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(t[t.length-1]=0),n(S.trex,t)},function(){var e,t,r;r=function(e,t){var n=0,r=0,i=0,a=0;return e.length&&(void 0!==e[0].duration&&(n=1),void 0!==e[0].size&&(r=2),void 0!==e[0].flags&&(i=4),void 0!==e[0].compositionTimeOffset&&(a=8)),[0,0,n|r|i|a,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},t=function(e,t){var i,a,s,o;for(a=e.samples||[],t+=20+16*a.length,i=r(a,t),o=0;o<a.length;o++)s=a[o],i=i.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size,s.flags.isLeading<<2|s.flags.dependsOn,s.flags.isDependedOn<<6|s.flags.hasRedundancy<<4|s.flags.paddingValue<<1|s.flags.isNonSyncSample,61440&s.flags.degradationPriority,15&s.flags.degradationPriority,(4278190080&s.compositionTimeOffset)>>>24,(16711680&s.compositionTimeOffset)>>>16,(65280&s.compositionTimeOffset)>>>8,255&s.compositionTimeOffset]);return n(S.trun,new Uint8Array(i))},e=function(e,t){var i,a,s,o;for(a=e.samples||[],t+=20+8*a.length,i=r(a,t),o=0;o<a.length;o++)s=a[o],i=i.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size]);return n(S.trun,new Uint8Array(i))},w=function(n,r){return"audio"===n.type?e(n,r):t(n,r)}}(),t["default"]={ftyp:a,mdat:s,moof:c,moov:f,initSegment:function(e){var t,n=a(),r=f(e);return t=new Uint8Array(n.byteLength+r.byteLength),t.set(n),t.set(r,n.byteLength),t}}},function(e,t){"use strict";t.__esModule=!0;var n={BDA_UNITIALIZED_MPEG2STREAMTYPE:-1,Reserved1:0,ISO_IEC_11172_2_VIDEO:1,ISO_IEC_13818_2_VIDEO:2,ISO_IEC_11172_3_AUDIO:3,ISO_IEC_13818_3_AUDIO:4,ISO_IEC_13818_1_PRIVATE_SECTION:5,ISO_IEC_13818_1_PES:6,ISO_IEC_13522_MHEG:7,ANNEX_A_DSM_CC:8,ITU_T_REC_H_222_1:9,ISO_IEC_13818_6_TYPE_A:10,ISO_IEC_13818_6_TYPE_B:11,ISO_IEC_13818_6_TYPE_C:12,ISO_IEC_13818_6_TYPE_D:13,ISO_IEC_13818_1_AUXILIARY:14,ISO_IEC_13818_1_RESERVED:15,ISO_IEC_14496_3_AUDIO:17,USER_PRIVATE:16,ISO_IEC_14496_1_IN_PES:18,ISO_IEC_14496_1_IN_SECTION:19,ISO_IEC_13818_6_DOWNLOAD:20,METADATA_IN_PES:21,METADATA_IN_SECTION:22,METADATA_IN_DATA_CAROUSEL:23,METADATA_IN_OBJECT_CAROUSEL:24,METADATA_IN_DOWNLOAD_PROTOCOL:25,IRPM_STREAMM:26,ITU_T_H264:27,ISO_IEC_USER_PRIVATE:128,DOLBY_AC3_AUDIO:129,DOLBY_DIGITAL_PLUS_AUDIO_ATSC:135};t["default"]=n},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(){if(0!==c.size(this.requestsInFlight)){var e=c.reduce(this.requestsInFlight,function(e,t){return e+(t.avgBandwidth.average()||0)},0);this.averageDownlink_.push(e)}}t.__esModule=!0,t.Network=t.RequestType=void 0;var s=n(65),o=r(s),u=n(40),c=n(1),f=n(3),l=n(5),d=n(44),h=t.RequestType=void 0;!function(e){e[e.Text=0]="Text",e[e.ByteArray=1]="ByteArray"}(h||(t.RequestType=h={}));var p=function v(e,t,n,r){i(this,v),this.canceled=!1,this.uri=e,this.requester=t,this.estimatedSize=n,this.avgBandwidth=new o["default"](20),this.cancelCallbackPromise=r},m=function(){function e(t){i(this,e),this.requests_=[],this.destroyed_=!1,this.credentials="omit",this.requestsInFlight={},c.extend(this,f),this.recordBandwidth_=c.throttle(a.bind(this),100),this.averageDownlink_=new o["default"](20),this.averageLatency_=new o["default"](10),this.credentials=t&&t.withCredentials?"include":"omit"}return e.prototype.status=function(){var e=c.filter(this.requestsInFlight,function(e){return"media-file"===e.requester});return{requestsOutstanding:c.size(e),bandwidth:Math.max(this.averageDownlink_.average(),0),bandwidthMax:Math.max(this.averageDownlink_.max(),0),latency:this.averageLatency_.average()/1e3}},e.prototype.maxLatency=function(){return this.averageLatency_.max()},e.prototype.request=function(e){function t(e){var t=d.indexOf(e);return d.splice(t,1),e}var n=this;if(this.destroyed_)throw"Network Engine destroyed";for(var r=e.retryParameters||{},i=r.maxAttempts||1,a=r.backoffFactor||2,s=null==r.baseDelay?500:r.baseDelay,o=c.uniqueId("request_"),u=this.send_(e,0,o),f=function(t){var r=t%e.uris.length;u=u["catch"](function(){return n.resend_(e,s,r,o)}),s*=a},l=1;l<i;l++)f(l);var d=this.requests_;d.push(u);var h=e.requestType;return{requestId:o,requestChannels:u.then(function(){return t(u)})["catch"](function(e){return t(u),e}).then(function(e){return n.processResponse_(h,e)})}},e.prototype.processResponse_=function(t,n){if(n.error)throw n.error;this.averageLatency_.push(e.calculateLatency_(n.startTime));var r=l.chan(1e5),i=l.chan();return this.processXhrResponse_(n,r,i),{chan:r,metaChan:i,url:n.url,requestId:n.requestId}},e.prototype.send_=function(t,n,r){var i=t.uris[n],a=t.requester,s={requestId:r,startTime:c.now(),url:i,response:void 0,error:void 0},o=({credentials:this.credentials},this.makeRequest_(i,t.requestType,r)),u=new p(i,a,100,o.cancelCallback);return this.requestsInFlight[r]=u,o.completePromise["catch"](function(e){return e.status?s.error=e.status+" "+e.message.replace(e.status,""):s.url&&"http:"===s.url.substring(0,5)&&"https:"===document.location.protocol?s.error="Unable to fetch HTTP resource over HTTPS":s.error="Crossdomain access denied",null}).then(function(t){if(s.response=t,!t||!e.responseOk_(t.status))throw s.error=s.error||t.status+" "+t.statusText,s;return s})},e.prototype.makeRequest_=function(e,t,n){return this.xhrSend_(e,t)},e.prototype.xhrSend_=function(e,t){var n=t===h.ByteArray?"arraybuffer":"text",r=void 0,i=void 0,a=void 0,s=new Promise(function(e,t){r=e,i=t}),o=new Promise(function(e){a=e});a(function(){u.abort()});var u=d.ajax(e,r,function(e,t,n){i({status:n.status,message:e})},{responseType:n});return{completePromise:s,cancelCallback:o}},e.prototype.resend_=function(e,t,n,r){var i=this,a=e.retryParameters||{},s=null==a.fuzzFactor?.5:a.fuzzFactor,o=2*Math.random()-1,c=o*s,f=t*(1+c);return(0,u.wait)(f).then(function(){return i.send_(e,n,r)})},e.prototype.cancelRequest=function(e){var t=this.requestsInFlight[e];return!!t&&(t.cancelCallbackPromise.then(function(e){e()}),t.canceled=!0,!0)},e.prototype.recordUncachedBandwidth_=function(e,t,n){var r=c.now()-e;if(r>5){var i=Math.floor(t/r);return this.requestsInFlight[n].avgBandwidth.push(i),this.recordBandwidth_(),!0}return!1},e.prototype.processXhrResponse_=function(e,t,n){var r=e.requestId,i=e.response.response,a=e.startTime,s=i.byteLength||i.length;l.putAsync(t,i,function(){return t.close()}),l.putAsync(n,{size:s},function(){return n.close()}),this.recordUncachedBandwidth_(a,s,r),delete this.requestsInFlight[r]},e.responseOk_=function(e){return e>=200&&e<300},e.calculateLatency_=function(e){var t=c.now()-e;return Math.min(Math.max(10,t||0),3e3)},e.prototype.destroy=function(){this.destroyed_=!0;var e=c.map(this.requests_,function(e){return e["catch"](c.noop)});return Promise.all(e)},e}();t.Network=m},function(e,t,n){"use strict";function r(e){return new Promise(function(t){setTimeout(t,e)})}function i(e){return new Promise(function(t){a.takeAsync(e,t)})}t.__esModule=!0,t.wait=r,t.channelToPromise=i;var a=n(5)},,,,,,,,,,function(e,t,n){(function(e,r){function i(e,t){this._id=e,this._clearFn=t}var a=n(85).nextTick,s=Function.prototype.apply,o=Array.prototype.slice,u={},c=0;t.setTimeout=function(){return new i(s.call(setTimeout,window,arguments),clearTimeout)},t.setInterval=function(){return new i(s.call(setInterval,window,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(window,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},t.setImmediate="function"==typeof e?e:function(e){var n=c++,r=!(arguments.length<2)&&o.call(arguments,1);return u[n]=!0,a(function(){u[n]&&(r?e.apply(null,r):e.call(null),t.clearImmediate(n))}),n},t.clearImmediate="function"==typeof r?r:function(e){delete u[e]}}).call(t,n(50).setImmediate,n(50).clearImmediate)},,,,function(e,t,n){var r,i;r=[n(2)],i=function(e){return{container:null,volume:function(t){t=e.between(t/100,0,1),this.video.volume=t},mute:function(e){this.video.muted=!!e},resize:function(t,n,r){if(!(t&&n&&this.video.videoWidth&&this.video.videoHeight))return!1;if("uniform"===r){var i=t/n,a=this.video.videoWidth/this.video.videoHeight,s=null;Math.abs(i-a)<.09&&(s="fill"),e.style(this.video,{objectFit:s})}return!1},getContainer:function(){return this.container},setContainer:function(e){this.container=e,e.appendChild(this.video);
},remove:function(){this.stop(),this.destroy(),this.container===this.video.parentNode&&this.container.removeChild(this.video)},getVideo:function(e){var t=document.getElementById(e),n=(t?t.querySelector("video"):void 0)||document.createElement("video");return n.className="jw-video jw-reset",this.video=n,n}}}.apply(t,r),!(void 0!==i&&(e.exports=i))},function(e,t,n){var r,i;r=[n(3),n(17),n(6)],i=function(e,t,n){var r=256;return{attached:!0,beforeComplete:!1,stallCheckTimeout_:-1,lastStalledTime_:NaN,trigger:function(t,n){if(this.attached)return e.trigger.call(this,t,n)},setState:function(e){return t.setState.call(this,e)},checkComplete:function(){return this.beforeComplete},playbackComplete_:function(){this.stopStallCheck(),this.setState(n.COMPLETE),this.beforeComplete=!1,this.trigger("complete")},attachMedia:function(){this.attached=!0,this.eventsOn_(),this.beforeComplete&&this.playbackComplete_()},detachMedia:function(){return this.stopStallCheck(),this.attached=!1,this.eventsOff_(),this.video},stopStallCheck:function(){clearTimeout(this.stallCheckTimeout_)},startStallCheck:function(){this.stopStallCheck(),this.stallCheckTimeout_=setTimeout(this.stalledHandler.bind(this),r)},stalledHandler:function(){return this.lastStalledTime_!==this.video.currentTime?void(this.lastStalledTime_=this.video.currentTime):void(this.video.paused||this.video.ended||this.state!==n.LOADING&&this.state!==n.ERROR&&(this.seeking||this.setState(n.STALLED)))}}}.apply(t,r),!(void 0!==i&&(e.exports=i))},function(e,t,n){var r,i;r=[n(2),n(6)],i=function(e,t){return{seeking:!1,loadedmetadata:function(){var e={duration:this.getDuration(),height:this.video.videoHeight,width:this.video.videoWidth},t=this.drmUsed;t&&(e.drm=t),this.trigger("meta",e)},timeupdate:function(){this.stopStallCheck();var e=this.video.videoHeight;e!==this._helperLastVideoHeight&&this.trigger("adaptation",{size:{width:this.video.videoWidth,height:this.video.videoHeight}}),this._helperLastVideoHeight=e,this.state===t.STALLED&&this.setState(t.PLAYING),this.startStallCheck();var n=this.getCurrentTime(),r={position:n,duration:this.getDuration()};if(this.getPtsOffset){var i=this.getPtsOffset();i>=0&&(r.metadata={mpegts:i+n})}(this.state===t.PLAYING||this.seeking)&&this.trigger("time",r)},click:function(e){this.trigger("click",e)},volumechange:function(){var e=this.video;this.trigger("volume",{volume:Math.round(100*e.volume)}),this.trigger("mute",{mute:e.muted})},seeked:function(){this.seeking&&(this.seeking=!1,this.trigger("seeked"))},playing:function(){this.setState(t.PLAYING)},pause:function(){this.state!==t.COMPLETE&&(this.video.ended||this.video.currentTime!==this.video.duration&&this.setState(t.PAUSED))},progress:function(){var t=this.getDuration();if(!(t<=0||t===1/0)){var n=this.video.buffered;if(n&&0!==n.length){var r=e.between(n.end(n.length-1)/t,0,1);this.trigger("bufferChange",{bufferPercent:100*r,position:this.getCurrentTime(),duration:t})}}},ended:function(){if(this.stopStallCheck(),this._helperLastVideoHeight=0,this.state!==t.IDLE&&this.state!==t.COMPLETE){if(this.beforeComplete=!0,this.attached=!0,this.trigger("beforeComplete"),!this.attached)return;this.playbackComplete_()}},loadeddata:function(){this.setTextTracks(this.video.textTracks),this.video.setAttribute("jw-loaded","data")},error:function(){var e=this.video.error&&this.video.error.code||-1,t={1:"Unknown operation aborted",2:"Unknown network error",3:"Unknown decode error",4:"Source not supported"}[e]||"Unknown";this.trigger("mediaError",{code:e,message:"Error playing file: "+t})}}}.apply(t,r),!(void 0!==i&&(e.exports=i))},function(e,t,n){"use strict";function r(e){return Math.floor(Math.random()*(e+1))}function i(e){var t,n=new Array(e);for(t=0;t<e;t++)n[t]=0;for(t=1;t<e;t++){var i=r(t);n[t]=n[i],n[i]=t}return n}var a=n(21).Box,s=function(e,t){this.f=t,this.flag=e};s.prototype.is_active=function(){return this.flag.value},s.prototype.is_blockable=function(){return!0},s.prototype.commit=function(){return this.flag.value=!1,this.f};var o=function(e,t){this.value=e,this.channel=t},u=Object.prototype.hasOwnProperty,c={toString:function(){return"[object DEFAULT]"}};t.do_alts=function(e,t,n){var r=e.length;if(0===r)throw new Error("Empty alt list");var f=!(!n||!n.priority);if(!f)var l=i(r);for(var d=new a((!0)),h=0;h<r;h++){var p,m,v=e[f?h:l[h]];if(v instanceof Array){var y=v[1];p=v[0],m=p._put(y,function(e){return new s(d,function(n){t(new o(n,e))})}(p))}else p=v,m=p._take(function(e){return new s(d,function(n){t(new o(n,e))})}(p));if(m instanceof a){t(new o(m.value,p));break}}m instanceof a||!n||!u.call(n,"default")||d.value&&(d.value=!1,t(new o(n["default"],c)))},t.DEFAULT=c},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.Manifest=t.MediaData=void 0;var i=n(104),a=n(103),s=n(59),o=n(102),u=n(1),c=n(3);n(4),t.MediaData=function f(e){r(this,f),this.type=e.type,this.groupId=e.groupid,this.instreamId=e.instreamid,this.name=e.name,this.uri=e.uri,this.language=e.language,this.associatedLanguage=e.assoclanguage,this["default"]="YES"===e["default"],"undefined"!=typeof e.autoselect&&(this["default"]=this.autoSelect=!0),this.forced="YES"===e.forced,this.characteristics=e.characteristics?e.characteristics.split(","):[]},t.Manifest=function(){function e(t,n,i){r(this,e),this.ptsToSecondsMap_={},this.streamType="LIVE",this.lastManifestRefreshTime_={},this.url="",this.levels_=[],this.sortedLevels_=[],this.refreshesWithoutChanges=0,u.extend(this,c),this.network=t,this.video_=n,this.minDvrWindow=i}return e.prototype.load=function(e,t){var n=this;return this.url=e,this.fetchManifest_({manifestURL:e,loadStreamLabel:t}).then(function(e){return"VOD"!==n.streamType&&(n.liveManifestLoader=new o.LiveManifestLoader(n,n.network,n.video_)),e})},e.prototype.getLevelFromLabel=function(e,t){for(var n=0;n<e.length;n++)if(e[n].label===t)return n;return 0},e.prototype.requestTS=function(e,t,n){var r=this,i=this.sortedLevels_[n];return this.fetchDataPromise_.then(function(){if(i.disabled)return Promise.resolve([]);var n=i.streamInfo.originalIndex;return"VOD"===r.streamType?r.refreshManifest(n).then(function(){return i.getSegmentTimeRange(e,t)}):r.liveManifestLoader.pollManifest(n).then(function(){var n=i.getSegmentTimeRange(e,t);if(n.length)return n;var r=i.segments[0];return r&&r.start>t?i.segments:[]})})},e.prototype.getSegmentFromIndex=function(e){var t=e.split("_"),n=t[0],r=t[1],i=this.levels_[parseInt(n)];return i.mediaSequenceMap[r]},e.prototype.updateTS=function(e,t,n){var r=this.levels_[e],i=r.mediaSequenceMap[t];i&&"boxed"===n.type&&n.duration&&(i.duration=n.duration,i.pts=n.start,this.updateTimeline_(r))},e.prototype.setPtsMapping=function(e,t,n){this.ptsToSecondsMap_[e]||(this.ptsToSecondsMap_[e]={discontinuity:e,pts:t,seconds:n})},e.prototype.getPtsMapping=function(e){return this.ptsToSecondsMap_[e]},e.prototype.updateLevel=function(e){var t=this.levels_[e.level];t.segmentFileSize.push(e.size);var n=t.segmentFileSize.max()/t.streamInfo.targetDuration;t.streamInfo.bandwidth=Math.max(t.streamInfo.bandwidth,n)},e.prototype.refreshManifest=function(e){var t=this;return"VOD"===this.streamType&&this.levels_[e].segments.length?Promise.resolve(this.currentManifestMetadata):(this.fetchDataPromise_=this.fetchManifestData_({manifestURL:this.url,loadStreamIndex:e}).then(function(e){return t.refreshLevels_(e)}).then(function(e){return t.appendManifestInfoToLevels_(e)}).then(function(e){var n=e.media.streamInfo.originalIndex,r=t.levels_[n];return t.referenceTimeline_=r,t.updateTimeline_(r),t.currentManifestMetadata}).then(function(e){var n=u.any(e.levels,u.property("updatedInLastRefresh"));return t.refreshesWithoutChanges=n?0:t.refreshesWithoutChanges+1,e}),this.fetchDataPromise_)},e.prototype.destroy=function(){this.liveManifestLoader&&this.liveManifestLoader.destroy(),this.network&&(this.network=null)},e.prototype.fetchManifest_=function(e){var t=this;return this.fetchDataPromise_=this.fetchManifestData_(e).then(function(e){return t.setupLevels_(e)})["catch"](function(e){return e}).then(function(e){var n=u.isString(e);if(n)throw e;var r=u.where(t.levels_,{disabled:!1});if(!r.length)throw"No levels to play";if(!t.referenceTimeline_||t.referenceTimeline_.disabled)throw"Playlist could not be loaded";var i=t.referenceTimeline_.streamInfo;return t.sortedLevels_=t.getSortedLevels_(r),t.currentManifestMetadata={initialLevel:t.initialLevel,sortedLevels:t.sortedLevels_,levels:r,trackTypes:r[0].streamInfo.trackTypes,streamType:t.streamType,start:i.start,end:i.end},t.currentManifestMetadata}),this.fetchDataPromise_},e.prototype.doneLoading=function(){return this.fetchDataPromise_},e.prototype.fetchManifestData_=function(e){function t(t,n){var i=e.loadStreamIndex;u.isNumber(i)&&!u.isNaN(i)||(i=r.getLevelFromLabel(t,e.loadStreamLabel));var a=t[i],s=n.getPlaylist();return s.streamInfos=t,r.downloadAndParseThrottle_(a.urls,a).then(function(e){return{master:s,media:e.parser.getPlaylist()}})["catch"](function(){return{master:s,media:{streamInfo:a}}})}function n(e){var n=e.parser;if(e.children=r.groupRedundantStreams_(e.children),e.children.length)return r.parsedMaster_=e,t(e.children,n);var i=n.getPlaylist();return{master:void 0,media:i}}var r=this;return this.url===e.manifestURL&&this.parsedMaster_?t(this.parsedMaster_.children,this.parsedMaster_.parser):this.downloadAndParseThrottle_([e.manifestURL]).then(n)},e.prototype.downloadAndParseThrottle_=function(t,n){var r=n?e.makeRedundantStreamKey(n):t[0],i=u.now(),a=this.lastManifestRefreshTime_[r];if(a&&i-a.time<500)return a.promise;var s=this.downloadAndParse_(t,n);return this.lastManifestRefreshTime_[r]={time:i,promise:s},s},e.prototype.downloadAndParse_=function(e,t){var n=new i.ManifestParser(t),r=new a.ManifestLoader(this.network),s=[];return r.on("baseUrlFound",n.setBaseUrl,n),n.on("mediaPlaylistFound",function(e){s.push(e)}),r.on("manifestChunk",n.parse,n),r.load(e).then(function(){return n.off(),r.off(),{children:s,parser:n}})},e.prototype.suspendManifestReloading=function(){"LIVE"===this.streamType&&this.liveManifestLoader&&this.liveManifestLoader.cancelAllManifestReloads()},e.prototype.refreshLevels_=function(e){var t=this,n=e.media;if(!n.streamInfo)return null;var r=u.where(this.levels_,{disabled:!1});if(r.length>1&&(this.disableLevel_(n),r=u.where(this.levels_,{disabled:!1})),r.length!==this.sortedLevels_.length){this.sortedLevels_=this.getSortedLevels_(r),this.currentManifestMetadata.levels=r,this.currentManifestMetadata.sortedLevels=this.sortedLevels_;var i=r[0],a=i.streamInfo.originalIndex,s=u.indexOf(this.sortedLevels_,this.levels_[a]);return this.trigger("levels-disabled",{levels:this.sortedLevels_,nextIndex:s}),this.fetchManifestData_({manifestURL:this.url,loadStreamIndex:a}).then(function(e){return t.refreshLevels_(e)})}return Promise.resolve(e)},e.prototype.setupLevels_=function(e){var t=this;e.master||(e.master=new i.Playlist,e.master.streamInfos=[e.media.streamInfo]),this.levels_.length||(this.levels_=u.map(e.master.streamInfos,function(t){var n=new s.Level;return n.streamInfo=t,n.streamInfo.video&&e.master.renditions.video&&(n.streamInfo.video=e.master.renditions.video[n.streamInfo.video]),n.streamInfo.audio&&e.master.renditions.audio&&(n.streamInfo.audio=e.master.renditions.audio[n.streamInfo.audio]),n})),u.each(this.levels_,function(e,t){e.streamInfo.originalIndex=t});var n=e.media;if(!n.streamInfo)return Promise.reject("Invalid manifest file");this.disableLevels_(n);var r=n.streamInfo.originalIndex,a=this.levels_[r],o=a.streamInfo;this.referenceTimeline_=a,o.start=n.streamInfo.start,o.end=n.streamInfo.end,o.targetDuration=n.streamInfo.targetDuration,o.hasEndlist?this.streamType="VOD":o.end-o.start>=this.minDvrWindow&&(this.streamType="DVR"),u.each(n.mediaElements,function(e){e.level=r,a.appendSegment(e)}),this.initialLevel=a;var c=u.where(this.levels_,{disabled:!1});if(!c.length)throw"No levels to play";if(this.sortedLevels_=this.getSortedLevels_(c),a.disabled){var f=c[0].streamInfo.originalIndex;return this.fetchManifestData_({manifestURL:this.url,loadStreamIndex:f}).then(function(e){return t.setupLevels_(e)})}return Promise.resolve()},e.prototype.setCurrentQuality=function(e){if(e!==-1){var t=this.sortedLevels_[e];this.refreshManifest(t.streamInfo.originalIndex)}},e.prototype.disableLevels_=function(e){e&&this.disableLevel_(e);var t=u.any(this.levels_,function(e){return u.any(e.streamInfo.trackTypes,function(e){return"video"===e.type})}),n=u.any(this.levels_,function(e){return u.contains(e.streamInfo.codecs,"mp4a.40.2")});u.each(this.levels_,function(e){var r=1===e.streamInfo.trackTypes.length&&"audio"===e.streamInfo.trackTypes[0].type,i=e.streamInfo.codecs&&u.contains(e.streamInfo.codecs,"mp4a.40.5");(t&&r||n&&i)&&(e.disabled=!0)})},e.prototype.disableLevel_=function(e){var t=e.mediaElements;if(!t||!t.length||!t[0].url){var n=e.streamInfo.originalIndex;this.levels_[n].disabled=!0}},e.prototype.getSortedLevels_=function(e){var t=u.clone(e);return t.sort(function(e,t){return t.streamInfo.resolution.height&&e.streamInfo.resolution.height?t.streamInfo.resolution.height-e.streamInfo.resolution.height:t.streamInfo.bandwidth-e.streamInfo.bandwidth}),t},e.prototype.appendManifestInfoToLevels_=function(e){var t=e.media,n=t.streamInfo.originalIndex,r=this.levels_[n];if(r.mediaSequence=t.mediaSequence,"DVR"===this.streamType||"LIVE"===this.streamType){t.segments=t.mediaElements||[],r.updateSegments.call(t);var i=this.syncLiveLevels_(t);i||this.referenceTimeline_.segments.length&&(t.mediaElements[0].start=u.last(this.referenceTimeline_.segments).end,this.ptsToSecondsMap_={}),t.streamInfo.hasEndlist&&(u.each(this.levels_,function(e){e.streamInfo.hasEndlist=!0}),this.streamType=this.currentManifestMetadata.streamType="VOD")}var a=!1;return u.each(t.mediaElements,function(e){var t=r.appendSegment(e);t&&(a=!0),e.level=r.streamInfo.originalIndex}),r.updatedInLastRefresh=a,r.streamInfo.targetDuration=t.streamInfo.targetDuration,e},e.prototype.updateTimeline_=function(e){e.updateSegments(),e===this.referenceTimeline_&&(this.currentManifestMetadata.start=e.streamInfo.start,this.currentManifestMetadata.end=e.streamInfo.end)},e.prototype.syncLiveLevels_=function(e){var t=this,n=!1;return u.each(this.levels_,function(r){if(t.attemptSync_(e,r))n=!0;else{var i=t.liveManifestLoader.refreshRequests[r.streamInfo.originalIndex];i&&i.cycles||r.flushSegmentInfo()}}),n},e.prototype.attemptSync_=function(e,t){var n=e.mediaElements[0],r=t.getLastSegment();if(0===t.segments.length)return!1;if(r.mediaSequenceId+1===n.mediaSequenceId)return n.start=r.end,!0;for(var i=0,a=0;i<e.mediaElements.length&&a<t.segments.length;){var s=e.mediaElements[i],o=t.segments[a];if(s.mediaSequenceId===o.mediaSequenceId){var u=o.start-s.start;return e.mediaElements[0].start+=u,!0}s.mediaSequenceId<o.mediaSequenceId?i++:a++}return!1},e.prototype.groupRedundantStreams_=function(t){for(var n={},r=t,i=Array.isArray(r),a=0,r=i?r:r[Symbol.iterator]();;){var s;if(i){if(a>=r.length)break;s=r[a++]}else{if(a=r.next(),a.done)break;s=a.value}var o=s,u=e.makeRedundantStreamKey(o),c=n[u];c?c.urls=c.urls.concat(o.urls):n[u]=o}return Object.keys(n).map(function(e){return n[e]})},e.makeRedundantStreamKey=function(e){var t=e.resolution;return t&&t.width&&t.height?""+t.width+"x"+t.height:e.bandwidth?""+e.bandwidth:"no resolution"},e}()},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.SegmentOriginalData=t.Dimensions=t.Segment=t.StreamInfo=t.Level=void 0;var a=n(65),s=r(a),o=n(30),u=r(o),c=n(1),f=(t.Level=function(){function e(){i(this,e),this.segments=[],this.mediaSequence=0,this.mediaSequenceMap={},this.streamInfo=new f,this.updatedInLastRefresh=!1,this.disabled=!1,this.segmentFileSize=new s["default"](30)}return e.prototype.containsSegment=function(e){return!!e&&void 0!==this.mediaSequenceMap[e.mediaSequenceId]},e.prototype.flushSegmentInfo=function(){this.segments=[],this.mediaSequenceMap={}},e.prototype.appendSegment=function(e){return!this.containsSegment(e)&&(this.segments.push(e),this.mediaSequenceMap[e.mediaSequenceId]=e,!0)},e.prototype.updateSegments=function(){if(this.segments.length){for(var e=-1,t=this.segments,n=void 0,r=0;r<t.length;r++){n=t[r];var i=t[r+1];n.end=n.start+n.duration,i&&(i.start=n.end),n.mediaSequenceId>=this.mediaSequence&&e===-1&&(e=n.start)}this.streamInfo.end=n.end,e!==-1&&(this.streamInfo.start=e)}},e.prototype.getSegmentTimeRange=function(e,t){return u["default"].getRanges(this.segments,e,t,!0)},e.prototype.getLastSegment=function(){return c.last(this.segments)},e}(),t.StreamInfo=function h(){i(this,h),this.label="",this.urls=[],this.bandwidth=1e5,this.start=0,this.end=0,this.codecs=[],this.trackTypes=[],this.resolution=new l,this.targetDuration=5,this.originalIndex=0,this.video="",this.audio="",this.programId=0,this.hasEndlist=!1}),l=(t.Segment=function p(){i(this,p),this.duration=0,this.title="",this.start=0,this.end=0,this.discontinuity=0,this.mediaSequenceId=0,this.url="",this.key="",this.aesKeyUri="",this.aesIV=[],this.originalData=new d,this.byteRange={},this.pts=0,this.size=0,this.isLast=!1},t.Dimensions=function m(){i(this,m),this.width=0,this.height=0}),d=t.SegmentOriginalData=function v(){i(this,v),this.duration=0,this.start=0,this.end=0}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.StreamingEngine=void 0;var a=n(7),s=r(a),o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u=n(105),c=n(100),f=r(c),l=n(30),d=r(l),h=n(215),p=n(1),m=n(2),v=n(3),y=n(5),g=n(20),_=window,b=_.VTTCue||_.TextTrackCue,T=1,x=10,k=60,w=50,S=200,E=30;t.StreamingEngine=function(){function e(t,n,r,a,s){var o=this;i(this,e),this.lastTransmuxSessionComplete_=Promise.resolve([]),this.onTickInterval=-1,this.bufferEventSent_=!1,this.startOfStream_=!0,this.lastDuration_=NaN,this.onTickBlocked=!1,p.extend(this,v),this.network=n,this.video=r,this.mediaSource=a,this.manifestRepresentation=t,this.mediaDataModel=new u.MDM(t,n),this.adaptive=new f["default"],this.edition_=s,this.supports=new g(s),this.manifestRepresentation.on("levels-disabled",function(e){var t=e.nextIndex;e.levels.length>1&&t++,o.adaptive.updateLevels(e.nextIndex),o.trigger("levels",{levels:e.levels,currentQuality:t})}),this.adaptive.on("quality-change",function(e){o.manifestRepresentation.setCurrentQuality(e)}),this.mediaDataModel.on("segment-download-error",function(e){o.trackTypes_||o.beginStream()})}return e.prototype.updateDuration_=function(e){return"VOD"!==e.streamType&&e.end>this.video.duration&&this.msee.setDuration(e.end),this.onDurationChange(e),e},e.prototype.clearBackBuffer_=function(e){var t=this;if(!this.mediaSource.sourceBuffers.length||!this.lastTransmuxSession_.tmsArray.length)return Promise.resolve(e);var n=p.last(this.lastTransmuxSession_.tmsArray).ts.isLast;if(this.mediaSource.sourceBuffers[0].buffered.length&&!n){var r=function(){var n=t.mediaSource.sourceBuffers[0].buffered.start(0),r=Math.max(t.video.currentTime-k,0);if(r-n>x)return{v:t.removeBuffer(0,r).then(function(){return t.mediaDataModel.removeAllTmsInRange(0,r),e})["catch"](function(){return e})}}();if("object"===("undefined"==typeof r?"undefined":o(r)))return r.v}return Promise.resolve(e)},e.prototype.skipDiscontinuousZones=function(e){var t=this.video.currentTime,n=this.mediaDataModel.getSegmentAtTime(t);if(!n||this.minForwardBuffer()>.2)return e;var r=this.manifestRepresentation.levels_[n.level].segments,i=r.indexOf(n),a=r[i+1];if(!a||i===-1)return e;if(n.errorInSegment)return this.video.currentTime=n.end+.01,e;if(a.errorInSegment&&a.start-t<.2)return this.video.currentTime=a.isLast?this.video.duration:a.end+.01,e;var s=this.firstBufferedRange_(t),o=this.mediaDataModel.currentTransmuxSession;return t>=n.start&&t<n.end&&o.isBuffered&&s-t<5&&s>t&&(this.video.currentTime=s),n.discontinuity!==a.discontinuity&&this.video.buffered.length>1&&(this.video.currentTime=a.start),e},e.prototype.firstBufferedRange_=function(e){for(var t=this.msee.sourceBuffers_.boxed.buffered,n=t.length,r=0;r<n;r++){var i=t.start(r);if(e>=i&&e<=t.end(r)||e<=i)return i}return e},e.prototype.onTick_=function(){var e=this;if(this.msee){if(this.video&&this.video.error)return this.destroy();if(this.currentBlockingAppendBufferPromise)return void(this.onTickBlocked=!0);this.onTickBlocked=!1,this.adaptive.whatToDownload(this).then(this.buffer_.bind(this)),this.manifestRepresentation.doneLoading().then(function(t){return e.updateDuration_(t)}).then(function(t){return e.skipDiscontinuousZones(t)}).then(function(t){return e.clearBackBuffer_(t)})["catch"](function(t){return e.trigger("error",t)})}},e.prototype.removeBuffer=function(e,t){return this.msee?(this.mediaDataModel.removeInactiveTmsInRange(e,t),this.msee.remove("boxed",e,t)["catch"](function(){})):Promise.resolve()},e.prototype.setCurrentQuality=function(e){var t=this.manifestRepresentation.sortedLevels_;this.adaptive.setQuality(e,t)},e.prototype.addListeners=function(){this.removeVideoListeners_(),this.onTimeUpdateListener=this.onTimeUpdate_.bind(this),this.onSeekingListener=this.onSeeking_.bind(this),this.video.addEventListener("timeupdate",this.onTimeUpdateListener,!1),this.video.addEventListener("seeking",this.onSeekingListener,!1),this.onTickThrottle_=p.throttle(this.onTick_.bind(this),w),this.resumeOnTick()},e.prototype.removeVideoListeners_=function(){this.video.removeEventListener("timeupdate",this.onTimeUpdateListener),this.video.removeEventListener("seeking",this.onSeekingListener)},e.prototype.removeListeners=function(){this.removeVideoListeners_(),this.suspendOnTick(),this.adaptive.off(),this.manifestRepresentation&&this.manifestRepresentation.off(),this.off()},e.getLiveStartTime=function(e){var t=e.end-e.start,n=m.between(.75*t,E/2,E),r=Math.max(e.start,e.end-n),i=e.initialLevel.getSegmentTimeRange(r,r);return i.length?i[0].start:0},e.prototype.setupMSEE_=function(e){var t=this;return new Promise(function(n,r){function i(){t.mediaSource.removeEventListener("sourceopen",i);try{t.msee=new h(t.mediaSource,null),1===e.codecs.length&&"mp4a.40.34"===e.codecs[0]?t.msee.init({boxed:"audio/mpeg"}):t.msee.init({boxed:'video/mp4; codecs="'+e.codecs.toString()+'"'})}catch(a){return void r(new Error("The provided mime type or codecs are not supported in this browser"))}n(t.msee)}t.mediaSource.addEventListener("sourceopen",i),"open"===t.mediaSource.readyState&&i()})},e.prototype.setMseReady_=function(){var e=this;return this.mseReady=this.mediaDataModel.getTrackTypes().then(function(t){var n={type:"boxed",codecs:p.pluck(t,"codecString")};return e.trackTypes_=[n,{type:"metadata"},{type:"captions"}],e.trigger("trackTypes",t),n}).then(function(t){return e.setupMSEE_(t)})},e.prototype.setManifest=function(e,t){var n=this,r=this.manifestRepresentation.load(e,t).then(function(e){return n.onDurationChange(e)}).then(function(e){var r=void 0;return"Auto"===t?r=0:(r=p.indexOf(e.sortedLevels,n.manifestRepresentation.initialLevel),r+=1),n.setCurrentQuality(r),n.trigger("levels",{levels:e.sortedLevels,currentQuality:r}),n.trigger("trackTypes",e.trackTypes),e})["catch"](function(e){throw"Cannot load M3U8: "+e});return this.setMseReady_(),Promise.all([this.mseReady,r]).then(function(e){var t=e[1];n.msee.setDuration(t.end)})["catch"](function(){return null}),r},e.prototype.beginStream=function(){this.adaptive.whatToDownload(this,this.startOfStream_).then(this.buffer_.bind(this))["catch"](function(e){}),this.startOfStream_=!1,this.addListeners();var t=this;this.video.addEventListener("progress",function n(e){var r=e.target;t.msee&&t.minForwardBuffer()>T&&(r.removeEventListener("progress",n),t.bufferEventSent_=!0,t.trigger("bufferFull"))},!1),"LIVE"!==this.manifestRepresentation.streamType&&"DVR"!==this.manifestRepresentation.streamType||this.video.addEventListener("loadedmetadata",function r(n){var i=n.target;i.removeEventListener("loadedmetadata",r),t.manifestRepresentation&&(i.currentTime=e.getLiveStartTime(t.manifestRepresentation.currentManifestMetadata))})},e.prototype.buffer_=function(e){var t=this,n=e.segment;if(n&&!(n.end<this.video.currentTime&&this.video.currentTime!==this.video.duration))return n.aesKeyUri&&!this.supports("drm")?void this.trigger("error","AES decryption not supported in "+this.edition_+" edition"):(this.addExtInfCue_(n),this.mediaDataModel.get(n).then(function(e){return t.bufferTransmuxSegment_(e)["catch"](function(e){return null})})["catch"](function(e){return t.trigger("error",e)}))},e.prototype.addExtInfCue_=function(e){if(e.extInfTitle){var t=e.start,n=e.end,r="metadata",i=JSON.stringify({extInfTitle:e.extInfTitle}),a=new b(t,n,i);this.trigger("cueAdded",{type:r,cue:a})}},e.getCaptionOffset=function(e,t){return(e-t)/9e4},e.prototype.getStreamOffsets_=function(e){var t=this;return e?e.offsetsKnown.then(function(n){var r=n.boxed;return t.videoPtsOffset_=r.start,p.each(n,function(e){e.start=t.videoPtsOffset_}),{session:e,offsets:n}}):Promise.resolve({})},e.prototype.alignStreams_=function(e){var t=e.session,n=e.offsets;if(t&&t.tmsArray.length){var r=t.tmsArray[0].ts,i=this.manifestRepresentation.getPtsMapping(r.discontinuity);i||(this.manifestRepresentation.setPtsMapping(r.discontinuity,n.boxed.start,r.start),i=this.manifestRepresentation.getPtsMapping(r.discontinuity));var a=p.map(this.trackTypes_,function(e){var t=e.type,a=r.start,s=n[t].start,o=(s-i.pts)/9e4+i.seconds;return Math.abs(o-a)<5&&(a=o),{type:t,start:a,end:r.end,pts:r.pts}});return{session:t,alignedStreams:a}}},e.prototype.clearBufferBeforeAppending_=function(e){if(e&&e.session){var t=e.session,n=e.alignedStreams,r=p.pluck(n,"start"),i=Math.max.apply(Math,r)+1,a=i,s=this.video.seekable.length;return s&&(a=this.video.seekable.end(s-1)),a<=i?(t.forwardBufferCleared=!0,Promise.resolve(e)):this.removeBuffer(i,a).then(function(){return t.forwardBufferCleared=!0,e})}},e.prototype.addAllTracks_=function(e){var t=this;if(e){var n=e.session,r=e.alignedStreams,i=n.tmsArray.length&&n.tmsArray[0].ts;if(i){var a=p.map(r,function(e){var t=this.manifestRepresentation.getPtsMapping(i.discontinuity);return"metadata"===e.type||"captions"===e.type?this.appendTextTrack(n,e.type,e.start,t):this.appendMediaBuffer(n,e.type,e.start)},this);return this.lastTransmuxSessionComplete_=Promise.all(a),this.lastTransmuxSessionComplete_.then(function(){return n.transmuxer.isBoxedChannelClosed()?(n.tmsArray.length&&n.tmsArray[n.tmsArray.length-1].ts.isLast&&(t.msee.endOfStream(),t.bufferEventSent_||(t.bufferEventSent_=!0,t.trigger("bufferFull"))),n):n})}}},e.prototype.markTracksLoaded_=function(e){return e&&this.mediaDataModel?void p.each(e.tmsArray,this.mediaDataModel.complete,this.mediaDataModel):Promise.resolve()},e.prototype.appendMediaBuffer=function(e,t,n){var r=this;return new Promise(function(i){var a=e.mediaChannels[t];y.go(s["default"].mark(function o(){var r,u,c,f=this;return s["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:r=void 0,this.msee.setTimestampOffset(t,n),u=function(){if(!f.msee)return"break";if(!r.payload||!r.payload.length)return"continue";var n=void 0;r.appendWindowEnd&&f.msee.setAppendWindowEnd(t,r.appendWindowEnd);var i=f.currentBlockingAppendBufferPromise=f.msee.appendBuffer(t,r.payload).then(function(){e.isBuffered=!0,n()})["catch"](function(e){return n(),null});r.appendWindowEnd&&f.msee.setAppendWindowEnd(t,1/0),n=function(){i===f.currentBlockingAppendBufferPromise&&(f.currentBlockingAppendBufferPromise=void 0,f.onTickBlocked&&f.onTick_())}};case 3:return s.next=5,y.take(a);case 5:if(s.t0=r=s.sent,s.t1=y.CLOSED,s.t0===s.t1){s.next=16;break}c=u(),s.t2=c,s.next="break"===s.t2?12:"continue"===s.t2?13:14;break;case 12:return s.abrupt("break",16);case 13:return s.abrupt("continue",3);case 14:s.next=3;break;case 16:i();case 17:case"end":return s.stop()}},o,this)}).bind(r))})},e.prototype.appendTextTrack=function(t,n,r,i){var a=this;return new Promise(function(o){var u=t.mediaChannels[n];y.go(s["default"].mark(function c(){var a,f,l,d,h,p,m,v,g,_,T,x,k;return s["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:a=void 0,f=this.videoPtsOffset_,l=r,i&&(f=i.pts,l=i.seconds);case 4:return s.next=6,y.take(u);case 6:if(s.t0=a=s.sent,s.t1=y.CLOSED,s.t0===s.t1){s.next=34;break}if(this.msee&&!t.transmuxer.isBoxedChannelClosed()&&t.tmsArray.length){s.next=11;break}return s.abrupt("break",34);case 11:if("error"===a.type&&(this.trigger("error",a.message),this.removeListeners()),d=a.payload){s.next=15;break}return s.abrupt("continue",4);case 15:s.t2=n,s.next="captions"===s.t2?18:"metadata"===s.t2?26:32;break;case 18:return h=l+e.getCaptionOffset(d.start,f),p=l+e.getCaptionOffset(d.end,f),m=new b(h,p,d.text),v=d.positioning,isNaN(v.line)||(m.line=v.line),isNaN(v.position)||(m.position=v.position,m.align="start"),this.trigger("cueAdded",{type:n,cue:m}),s.abrupt("break",32);case 26:for(g=this.mediaDataModel.getSegmentByFileId(a.sourceFile),_=g.start,T=g.end,a.pts&&(_=(a.pts-i.pts)/9e4+i.seconds),x=0;x<d.length;x++)k=new b(_,T,""),k.data=d[x].data,this.trigger("cueAdded",{type:n,cue:k});return s.abrupt("break",32);case 32:s.next=4;break;case 34:o();case 35:case"end":return s.stop()}},c,this)}).bind(a))})},e.prototype.bufferTransmuxSegment_=function(e){var t=this;return e===this.lastTransmuxSession_?Promise.resolve(e):(this.lastTransmuxSession_=e,this.mseReady.then(function(){return t.lastTransmuxSessionComplete_}).then(function(){return t.getStreamOffsets_(e)}).then(function(e){return t.alignStreams_(e)}).then(function(e){return t.clearBufferBeforeAppending_(e)}).then(function(e){return t.addAllTracks_(e)}).then(function(e){return t.markTracksLoaded_(e)})["catch"](function(e){return t.trigger("error",e)}))},e.prototype.resumeOnTick=function(){this.mseReady.then(function(){this.onTickInterval===-1&&(this.onTickInterval=setInterval(this.onTickThrottle_,S))}.bind(this))["catch"](function(e){})},e.prototype.seek=function(e){d["default"].isTimeBuffered(this.video.buffered,e)||(this.removeBuffer(0,this.video.duration),this.mediaDataModel.currentTransmuxSession.cancelTransmuxSession()),this.video.currentTime=e},e.prototype.suspendOnTick=function(){clearInterval(this.onTickInterval),this.onTickInterval=-1},e.prototype.onTimeUpdate_=function(){this.onTickThrottle_()},e.prototype.onSeeking_=function(){if(this.video.buffered.length<1)return void this.onTickThrottle_()},e.prototype.onDurationChange=function(e){var t=0;return t="LIVE"===e.streamType?1/0:"DVR"===e.streamType?e.start-e.end:e.end,t!==this.lastDuration_&&(this.trigger("meta",{duration:t}),this.lastDuration_=t),e},e.prototype.getForwardBuffer_=function(e){var t=this;return this.msee?(p.isUndefined(e)&&(e=this.video.currentTime),t.msee.bufferedAheadOf("boxed",e)):0},e.prototype.maxForwardBuffer=function(e){return this.getForwardBuffer_(e)},e.prototype.minForwardBuffer=function(e){return this.getForwardBuffer_(e)},e.prototype.destroy=function(){this.removeListeners(),this.startOfStream_=!0,this.msee&&this.msee.destroy(),this.mediaDataModel=void 0,this.manifestRepresentation=void 0,this.msee=void 0},e}()},function(e,t,n){"use strict";function r(e){for(var t=0,n=0,r=10,o=[];t+s<e.length;){var u=a.utf8ArrayToStr(e.subarray(t,t+=3));if("ID3"===u){var c=e.subarray(t,t+=2),f=e.subarray(t,t+=1)[0];if(0!==(128&f)||c[0]<3)break;var l=a.syncSafeInt(e.subarray(t,t+=4));0!==(64&f)&&(r+=a.syncSafeInt(e.subarray(t,t+=4))),o=o.concat(i(e,n+r,l)),t+=l}else{if("3DI"!==u){t-=3;break}t+=7}n=t}return{position:t,cues:o}}function i(e,t,n){for(var r=e.length,i=t,s=[];i<t+n;){if(r-i<8)return s;var o=i;i+=4;var u=a.syncSafeInt(e.subarray(i,i+=4));if(r-i<u+2)return s;var c=e.subarray(i,i+=1)[0];if(c>0)return s;var f=e[i];3!==f&&0!==f||(--u,
i++);var l=e.buffer.slice(o-10,i+u+1);s.push({data:l}),i+=u+1}return s}t.__esModule=!0,t["default"]=r;var a=n(18),s=9},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n){return f["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return console.assert(n.closed===!1,"Audio channel should not be closed"),r.next=3,d.put(n,{type:"mediaSegment",sourceFile:e,frames:t});case 3:case"end":return r.stop()}},m[0],this)}function s(e,t){return f["default"].mark(function n(){var r,i,s,u,c,l,h,m,y,g,_;return f["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r=void 0,i=void 0,s=0,u=0,c=void 0,l=new Uint8Array(0),h=void 0,m=void 0;case 8:return n.t0=d.CLOSED,n.next=11,d.take(e);case 11:if(n.t1=i=n.sent,n.t0===n.t1){n.next=36;break}if("error"!==i.type){n.next=18;break}return n.next=16,d.put(t,i);case 16:return t.close(),n.abrupt("return");case 18:if("info"!==i.type){n.next=22;break}return n.next=21,d.put(t,i);case 21:return n.abrupt("continue",8);case 22:if(r=i,r.pts&&(s=r.pts,u=r.dts,h=l.byteLength>0),c=l.byteLength?p.concatUint8Array(l,r.payload):r.payload,y=o(c,s,u,m,r.sourceFile,h),g=y.frames,m||(m=y.defaultFrameHeader),!g.length){n.next=33;break}return n.delegateYield(a(r.sourceFile,g,t),"t2",30);case 30:_=v.last(g),s=_.pts+y.frameDuration,u=_.dts+y.frameDuration;case 33:l=c.subarray(y.excessBytesIndex),n.next=8;break;case 36:t.close();case 37:case"end":return n.stop()}},n,this)})}function o(e,t,n,r,i,a){for(var s=0,o=[],c=void 0,f=t,l=n,d=-1,h=0;s<e.length;){var p=u(e.subarray(s),r);if(p.frameHeader&&(r=p.frameHeader),1!==p.result){if(2===p.result)break;c=p.frame,d===-1&&(d=Math.round(9e4*c.sampleCount/c.samplerate)),a&&(f-=d,l-=d,a=!1),c.pts=f,c.dts=l,c.frameDuration=d,c.sourceFile=i,f+=d,l+=d,o.push(c),s+=p.frameSize,h=s}else s+=p.frameSize}return{defaultFrameHeader:r,frameDuration:d,frames:o,excessBytesIndex:h}}function u(e,t){if(e.byteLength<9)return{frameSize:0,result:2,frameHeader:t,frame:void 0};var n=e[0]<<8|246&e[1],r=65520===n;if(!r)return{frameSize:1,result:1,frameHeader:t,frame:void 0};if(!t){var i=(e[2]>>>6&3)+1,a=e[2]>>>2&15,s=(1&e[2])<<2|e[3]>>>6&3,o=!(1&e[1]),u=o?9:7;t={audioobjecttype:i,channelcount:s,samplerate:y[a],samplesize:16,samplingfrequencyindex:a,headerLength:u}}var c=(3&e[3])<<11|(255&e[4])<<3|(224&e[5])>>5;if(0===c)throw new Error("AAC file has a frame length 0");if(e.byteLength<c)return{frameSize:c,result:2,frameHeader:t,frame:void 0};var f=3&e[6];return{frameSize:c,result:0,frameHeader:t,frame:{audioobjecttype:t.audioobjecttype,channelcount:t.channelcount,data:e.subarray(t.headerLength,c),headerLength:t.headerLength,dts:0,pts:0,frameDuration:0,frameLength:c,sampleCount:1024*(f+1),samplerate:t.samplerate,samplesize:t.samplesize,samplingfrequencyindex:t.samplingfrequencyindex,sourceFile:""}}}t.__esModule=!0;var c=n(7),f=i(c);t["default"]=s,t.parseFrame=u;var l=n(5),d=r(l),h=n(12),p=r(h),m=[a].map(f["default"].mark),v=n(1),y=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350,0,0]},function(e,t){"use strict";t.__esModule=!0;var n={H264_NAL_UNIT_TYPE:6,H265_NAL_UNIT_TYPE:39,SEI_PAYLOAD_ITU_T_35:4,ITU_T_T35_COUNTRY_CODE:181,ITU_T_T35_PROVIDER_CODE:49,USER_IDENTIFIER_ATSC1_DATA:**********,USER_DATA_TYPE_CC_DATA:3,NTSC_CC_FIELD_1:0,NTSC_CC_FIELD_2:1,DTVCC_PACKET_DATA:2,DTVCC_PACKET_START:3,RBSP_TRAILING_BITS:128};t["default"]=n},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var i=n(62),a=n(2),s=function(){function e(){r(this,e)}return e.probeAAC=function(t){for(var n=0,r=t.length-e.AAC_HEADER_SIZE;n<r;){var a=t[n]<<8|65526&t[n+1];if(a===e.AAC_SYNCWORD){var s=(0,i.parseFrame)(t.subarray(n),void 0).frame;return!(s.samplingfrequencyindex>12||s.channelcount>7)}n++}return!1},e.probeMP3=function(t){for(var n=0,r=void 0,i=t.length-e.MP3_HEADER_SIZE;n<i;){if(r=t[n]<<8|65504&t[n+1],r===e.MP3_SYNCWORD){var a=3&t[n+1],s=3&t[n+1],o=(240&t[n+2])>>4,u=(12&t[n+2])>>2,c=(2&t[n+3])>>1,f=3===s?"LAYER1":"OTHER",l=1===s?4:1,d=void 0,h=void 0,p=void 0,m=void 0,v=void 0;switch(a){case 3:d=e.MP3_SAMPLE_RATE_MAP.MPEG1[u],h=e.MP3_BITRATE_MAP.MPEG1[s-1][o],p=e.MP3_SAMPLES_COEFFICENT.MPEG1[s-1];break;case 2:d=e.MP3_SAMPLE_RATE_MAP.MPEG2[u],h=e.MP3_BITRATE_MAP.OTHER[f][o],p=e.MP3_SAMPLES_COEFFICENT.OTHER[s-1];break;default:d=e.MP3_SAMPLE_RATE_MAP["MPEG2.5"][u],h=e.MP3_BITRATE_MAP.OTHER[f][o],p=e.MP3_SAMPLES_COEFFICENT.OTHER[s-1]}h*=1e3,m=Math.floor((p*h/d+c)*l),v=n+m;for(var y=-1*e.MP3_FRAME_TOLERANCE;y<=e.MP3_FRAME_TOLERANCE;y++)if((t[v+y]<<8|65504&t[v+y+1])===e.MP3_SYNCWORD)return!0;return!1}n++}return!1},e.probeMPEGTS=function(t){var n=t[0],r=t[e.TS_PACKET_SIZE];return n===r&&r===e.TS_SYNCBYTE},e.probeID3=function(e){return"ID3"===a.utf8ArrayToStr(e.subarray(0,3))},e}();t["default"]=s,s.AAC_HEADER_SIZE=18,s.AAC_SYNCWORD=65520,s.MP3_HEADER_SIZE=8,s.MP3_SYNCWORD=65504,s.MP3_FRAME_TOLERANCE=3,s.MP3_SAMPLE_RATE_MAP={MPEG1:[44100,48e3,32e3,0],MPEG2:[22050,24e3,16e3,0],"MPEG2.5":[11025,12e3,8e3,0]},s.MP3_BITRATE_MAP={MPEG1:[[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,0],[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,0],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,0]],OTHER:{LAYER1:[0,32,48,56,64,80,96,112,128,144,160,176,192,224,256,0],OTHER:[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,0]}},s.MP3_SAMPLES_COEFFICENT={MPEG1:[12,144,144],OTHER:[12,144,72]},s.TS_PACKET_SIZE=188,s.TS_SYNCBYTE=71},function(e,t){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var r=function(){function e(t){n(this,e),this._samplesCount=0,this._insertIndex=0,this._sum=0,this._samples=new Array(t),this._size=t}return e.prototype.push=function(e){this._sum+=e-(this._samples[this._insertIndex]||0),this._samples[this._insertIndex]=e,this._samplesCount=Math.max(this._samplesCount,this._insertIndex+1),this._insertIndex=(this._insertIndex+1)%this._samples.length},e.prototype.average=function(){return 0===this._samplesCount?0:this._sum/this._samplesCount},e.prototype.max=function(){return 0===this._samplesCount?0:this._samples.reduce(function(e,t){return Math.max(e,t)})},e}();t["default"]=r},,,,,,,,,,,,,,,,,,,,function(e,t){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function i(e){if(f===setTimeout)return setTimeout(e,0);if((f===n||!f)&&setTimeout)return f=setTimeout,setTimeout(e,0);try{return f(e,0)}catch(t){try{return f.call(null,e,0)}catch(t){return f.call(this,e,0)}}}function a(e){if(l===clearTimeout)return clearTimeout(e);if((l===r||!l)&&clearTimeout)return l=clearTimeout,clearTimeout(e);try{return l(e)}catch(t){try{return l.call(null,e)}catch(t){return l.call(this,e)}}}function s(){m&&h&&(m=!1,h.length?p=h.concat(p):v=-1,p.length&&o())}function o(){if(!m){var e=i(s);m=!0;for(var t=p.length;t;){for(h=p,p=[];++v<t;)h&&h[v].run();v=-1,t=p.length}h=null,m=!1,a(e)}}function u(e,t){this.fun=e,this.array=t}function c(){}var f,l,d=e.exports={};!function(){try{f="function"==typeof setTimeout?setTimeout:n}catch(e){f=n}try{l="function"==typeof clearTimeout?clearTimeout:r}catch(e){l=r}}();var h,p=[],m=!1,v=-1;d.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];p.push(new u(e,t)),1!==p.length||m||i(o)},u.prototype.run=function(){this.fun.apply(null,this.array)},d.title="browser",d.browser=!0,d.env={},d.argv=[],d.version="",d.versions={},d.on=c,d.addListener=c,d.once=c,d.off=c,d.removeListener=c,d.removeAllListeners=c,d.emit=c,d.binding=function(e){throw new Error("process.binding is not supported")},d.cwd=function(){return"/"},d.chdir=function(e){throw new Error("process.chdir is not supported")},d.umask=function(){return 0}},,function(e,t,n){var r,i;(function(){function n(e){function t(t,n,r,i,a,s){for(;a>=0&&a<s;a+=e){var o=i?i[a]:a;r=n(r,t[o],o,t)}return r}return function(n,r,i,a){r=k(r,a,4);var s=!C(n)&&x.keys(n),o=(s||n).length,u=e>0?0:o-1;return arguments.length<3&&(i=n[s?s[u]:u],u+=e),t(n,r,i,s,u,o)}}function a(e){return function(t,n,r){n=w(n,r);for(var i=M(t),a=e>0?0:i-1;a>=0&&a<i;a+=e)if(n(t[a],a,t))return a;return-1}}function s(e,t,n){return function(r,i,a){var s=0,o=M(r);if("number"==typeof a)e>0?s=a>=0?a:Math.max(a+o,s):o=a>=0?Math.min(a+1,o):a+o+1;else if(n&&a&&o)return a=n(r,i),r[a]===i?a:-1;if(i!==i)return a=t(p.call(r,s,o),x.isNaN),a>=0?a+s:-1;for(a=e>0?s:o-1;a>=0&&a<o;a+=e)if(r[a]===i)return a;return-1}}function o(e,t){var n=R.length,r=e.constructor,i=x.isFunction(r)&&r.prototype||l,a="constructor";for(x.has(e,a)&&!x.contains(t,a)&&t.push(a);n--;)a=R[n],a in e&&e[a]!==i[a]&&!x.contains(t,a)&&t.push(a)}var u=this,c=u._,f=Array.prototype,l=Object.prototype,d=Function.prototype,h=f.push,p=f.slice,m=l.toString,v=l.hasOwnProperty,y=Array.isArray,g=Object.keys,_=d.bind,b=Object.create,T=function(){},x=function(e){return e instanceof x?e:this instanceof x?void(this._wrapped=e):new x(e)};"undefined"!=typeof e&&e.exports&&(t=e.exports=x),t._=x,x.VERSION="1.8.3";var k=function(e,t,n){if(void 0===t)return e;switch(null==n?3:n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)};case 4:return function(n,r,i,a){return e.call(t,n,r,i,a)}}return function(){return e.apply(t,arguments)}},w=function(e,t,n){return null==e?x.identity:x.isFunction(e)?k(e,t,n):x.isObject(e)?x.matcher(e):x.property(e)};x.iteratee=function(e,t){return w(e,t,1/0)};var S=function(e,t){return function(n){var r=arguments.length;if(r<2||null==n)return n;for(var i=1;i<r;i++)for(var a=arguments[i],s=e(a),o=s.length,u=0;u<o;u++){var c=s[u];t&&void 0!==n[c]||(n[c]=a[c])}return n}},E=function(e){if(!x.isObject(e))return{};if(b)return b(e);T.prototype=e;var t=new T;return T.prototype=null,t},I=function(e){return function(t){return null==t?void 0:t[e]}},A=Math.pow(2,53)-1,M=I("length"),C=function(e){var t=M(e);return"number"==typeof t&&t>=0&&t<=A};x.each=x.forEach=function(e,t,n){t=k(t,n);var r,i;if(C(e))for(r=0,i=e.length;r<i;r++)t(e[r],r,e);else{var a=x.keys(e);for(r=0,i=a.length;r<i;r++)t(e[a[r]],a[r],e)}return e},x.map=x.collect=function(e,t,n){t=w(t,n);for(var r=!C(e)&&x.keys(e),i=(r||e).length,a=Array(i),s=0;s<i;s++){var o=r?r[s]:s;a[s]=t(e[o],o,e)}return a},x.reduce=x.foldl=x.inject=n(1),x.reduceRight=x.foldr=n(-1),x.find=x.detect=function(e,t,n){var r;if(r=C(e)?x.findIndex(e,t,n):x.findKey(e,t,n),void 0!==r&&r!==-1)return e[r]},x.filter=x.select=function(e,t,n){var r=[];return t=w(t,n),x.each(e,function(e,n,i){t(e,n,i)&&r.push(e)}),r},x.reject=function(e,t,n){return x.filter(e,x.negate(w(t)),n)},x.every=x.all=function(e,t,n){t=w(t,n);for(var r=!C(e)&&x.keys(e),i=(r||e).length,a=0;a<i;a++){var s=r?r[a]:a;if(!t(e[s],s,e))return!1}return!0},x.some=x.any=function(e,t,n){t=w(t,n);for(var r=!C(e)&&x.keys(e),i=(r||e).length,a=0;a<i;a++){var s=r?r[a]:a;if(t(e[s],s,e))return!0}return!1},x.contains=x.includes=x.include=function(e,t,n,r){return C(e)||(e=x.values(e)),("number"!=typeof n||r)&&(n=0),x.indexOf(e,t,n)>=0},x.invoke=function(e,t){var n=p.call(arguments,2),r=x.isFunction(t);return x.map(e,function(e){var i=r?t:e[t];return null==i?i:i.apply(e,n)})},x.pluck=function(e,t){return x.map(e,x.property(t))},x.where=function(e,t){return x.filter(e,x.matcher(t))},x.findWhere=function(e,t){return x.find(e,x.matcher(t))},x.max=function(e,t,n){var r,i,a=-(1/0),s=-(1/0);if(null==t&&null!=e){e=C(e)?e:x.values(e);for(var o=0,u=e.length;o<u;o++)r=e[o],r>a&&(a=r)}else t=w(t,n),x.each(e,function(e,n,r){i=t(e,n,r),(i>s||i===-(1/0)&&a===-(1/0))&&(a=e,s=i)});return a},x.min=function(e,t,n){var r,i,a=1/0,s=1/0;if(null==t&&null!=e){e=C(e)?e:x.values(e);for(var o=0,u=e.length;o<u;o++)r=e[o],r<a&&(a=r)}else t=w(t,n),x.each(e,function(e,n,r){i=t(e,n,r),(i<s||i===1/0&&a===1/0)&&(a=e,s=i)});return a},x.shuffle=function(e){for(var t,n=C(e)?e:x.values(e),r=n.length,i=Array(r),a=0;a<r;a++)t=x.random(0,a),t!==a&&(i[a]=i[t]),i[t]=n[a];return i},x.sample=function(e,t,n){return null==t||n?(C(e)||(e=x.values(e)),e[x.random(e.length-1)]):x.shuffle(e).slice(0,Math.max(0,t))},x.sortBy=function(e,t,n){return t=w(t,n),x.pluck(x.map(e,function(e,n,r){return{value:e,index:n,criteria:t(e,n,r)}}).sort(function(e,t){var n=e.criteria,r=t.criteria;if(n!==r){if(n>r||void 0===n)return 1;if(n<r||void 0===r)return-1}return e.index-t.index}),"value")};var L=function(e){return function(t,n,r){var i={};return n=w(n,r),x.each(t,function(r,a){var s=n(r,a,t);e(i,r,s)}),i}};x.groupBy=L(function(e,t,n){x.has(e,n)?e[n].push(t):e[n]=[t]}),x.indexBy=L(function(e,t,n){e[n]=t}),x.countBy=L(function(e,t,n){x.has(e,n)?e[n]++:e[n]=1}),x.toArray=function(e){return e?x.isArray(e)?p.call(e):C(e)?x.map(e,x.identity):x.values(e):[]},x.size=function(e){return null==e?0:C(e)?e.length:x.keys(e).length},x.partition=function(e,t,n){t=w(t,n);var r=[],i=[];return x.each(e,function(e,n,a){(t(e,n,a)?r:i).push(e)}),[r,i]},x.first=x.head=x.take=function(e,t,n){if(null!=e)return null==t||n?e[0]:x.initial(e,e.length-t)},x.initial=function(e,t,n){return p.call(e,0,Math.max(0,e.length-(null==t||n?1:t)))},x.last=function(e,t,n){if(null!=e)return null==t||n?e[e.length-1]:x.rest(e,Math.max(0,e.length-t))},x.rest=x.tail=x.drop=function(e,t,n){return p.call(e,null==t||n?1:t)},x.compact=function(e){return x.filter(e,x.identity)};var P=function(e,t,n,r){for(var i=[],a=0,s=r||0,o=M(e);s<o;s++){var u=e[s];if(C(u)&&(x.isArray(u)||x.isArguments(u))){t||(u=P(u,t,n));var c=0,f=u.length;for(i.length+=f;c<f;)i[a++]=u[c++]}else n||(i[a++]=u)}return i};x.flatten=function(e,t){return P(e,t,!1)},x.without=function(e){return x.difference(e,p.call(arguments,1))},x.uniq=x.unique=function(e,t,n,r){x.isBoolean(t)||(r=n,n=t,t=!1),null!=n&&(n=w(n,r));for(var i=[],a=[],s=0,o=M(e);s<o;s++){var u=e[s],c=n?n(u,s,e):u;t?(s&&a===c||i.push(u),a=c):n?x.contains(a,c)||(a.push(c),i.push(u)):x.contains(i,u)||i.push(u)}return i},x.union=function(){return x.uniq(P(arguments,!0,!0))},x.intersection=function(e){for(var t=[],n=arguments.length,r=0,i=M(e);r<i;r++){var a=e[r];if(!x.contains(t,a)){for(var s=1;s<n&&x.contains(arguments[s],a);s++);s===n&&t.push(a)}}return t},x.difference=function(e){var t=P(arguments,!0,!0,1);return x.filter(e,function(e){return!x.contains(t,e)})},x.zip=function(){return x.unzip(arguments)},x.unzip=function(e){for(var t=e&&x.max(e,M).length||0,n=Array(t),r=0;r<t;r++)n[r]=x.pluck(e,r);return n},x.object=function(e,t){for(var n={},r=0,i=M(e);r<i;r++)t?n[e[r]]=t[r]:n[e[r][0]]=e[r][1];return n},x.findIndex=a(1),x.findLastIndex=a(-1),x.sortedIndex=function(e,t,n,r){n=w(n,r,1);for(var i=n(t),a=0,s=M(e);a<s;){var o=Math.floor((a+s)/2);n(e[o])<i?a=o+1:s=o}return a},x.indexOf=s(1,x.findIndex,x.sortedIndex),x.lastIndexOf=s(-1,x.findLastIndex),x.range=function(e,t,n){null==t&&(t=e||0,e=0),n=n||1;for(var r=Math.max(Math.ceil((t-e)/n),0),i=Array(r),a=0;a<r;a++,e+=n)i[a]=e;return i};var D=function(e,t,n,r,i){if(!(r instanceof t))return e.apply(n,i);var a=E(e.prototype),s=e.apply(a,i);return x.isObject(s)?s:a};x.bind=function(e,t){if(_&&e.bind===_)return _.apply(e,p.call(arguments,1));if(!x.isFunction(e))throw new TypeError("Bind must be called on a function");var n=p.call(arguments,2),r=function(){return D(e,r,t,this,n.concat(p.call(arguments)))};return r},x.partial=function(e){var t=p.call(arguments,1),n=function(){for(var r=0,i=t.length,a=Array(i),s=0;s<i;s++)a[s]=t[s]===x?arguments[r++]:t[s];for(;r<arguments.length;)a.push(arguments[r++]);return D(e,n,this,this,a)};return n},x.bindAll=function(e){var t,n,r=arguments.length;if(r<=1)throw new Error("bindAll must be passed function names");for(t=1;t<r;t++)n=arguments[t],e[n]=x.bind(e[n],e);return e},x.memoize=function(e,t){var n=function(r){var i=n.cache,a=""+(t?t.apply(this,arguments):r);return x.has(i,a)||(i[a]=e.apply(this,arguments)),i[a]};return n.cache={},n},x.delay=function(e,t){var n=p.call(arguments,2);return setTimeout(function(){return e.apply(null,n)},t)},x.defer=x.partial(x.delay,x,1),x.throttle=function(e,t,n){var r,i,a,s=null,o=0;n||(n={});var u=function(){o=n.leading===!1?0:x.now(),s=null,a=e.apply(r,i),s||(r=i=null)};return function(){var c=x.now();o||n.leading!==!1||(o=c);var f=t-(c-o);return r=this,i=arguments,f<=0||f>t?(s&&(clearTimeout(s),s=null),o=c,a=e.apply(r,i),s||(r=i=null)):s||n.trailing===!1||(s=setTimeout(u,f)),a}},x.debounce=function(e,t,n){var r,i,a,s,o,u=function(){var c=x.now()-s;c<t&&c>=0?r=setTimeout(u,t-c):(r=null,n||(o=e.apply(a,i),r||(a=i=null)))};return function(){a=this,i=arguments,s=x.now();var c=n&&!r;return r||(r=setTimeout(u,t)),c&&(o=e.apply(a,i),a=i=null),o}},x.wrap=function(e,t){return x.partial(t,e)},x.negate=function(e){return function(){return!e.apply(this,arguments)}},x.compose=function(){var e=arguments,t=e.length-1;return function(){for(var n=t,r=e[t].apply(this,arguments);n--;)r=e[n].call(this,r);return r}},x.after=function(e,t){return function(){if(--e<1)return t.apply(this,arguments)}},x.before=function(e,t){var n;return function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=null),n}},x.once=x.partial(x.before,2);var O=!{toString:null}.propertyIsEnumerable("toString"),R=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"];x.keys=function(e){if(!x.isObject(e))return[];if(g)return g(e);var t=[];for(var n in e)x.has(e,n)&&t.push(n);return O&&o(e,t),t},x.allKeys=function(e){if(!x.isObject(e))return[];var t=[];for(var n in e)t.push(n);return O&&o(e,t),t},x.values=function(e){for(var t=x.keys(e),n=t.length,r=Array(n),i=0;i<n;i++)r[i]=e[t[i]];return r},x.mapObject=function(e,t,n){t=w(t,n);for(var r,i=x.keys(e),a=i.length,s={},o=0;o<a;o++)r=i[o],s[r]=t(e[r],r,e);return s},x.pairs=function(e){for(var t=x.keys(e),n=t.length,r=Array(n),i=0;i<n;i++)r[i]=[t[i],e[t[i]]];return r},x.invert=function(e){for(var t={},n=x.keys(e),r=0,i=n.length;r<i;r++)t[e[n[r]]]=n[r];return t},x.functions=x.methods=function(e){var t=[];for(var n in e)x.isFunction(e[n])&&t.push(n);return t.sort()},x.extend=S(x.allKeys),x.extendOwn=x.assign=S(x.keys),x.findKey=function(e,t,n){t=w(t,n);for(var r,i=x.keys(e),a=0,s=i.length;a<s;a++)if(r=i[a],t(e[r],r,e))return r},x.pick=function(e,t,n){var r,i,a={},s=e;if(null==s)return a;x.isFunction(t)?(i=x.allKeys(s),r=k(t,n)):(i=P(arguments,!1,!1,1),r=function(e,t,n){return t in n},s=Object(s));for(var o=0,u=i.length;o<u;o++){var c=i[o],f=s[c];r(f,c,s)&&(a[c]=f)}return a},x.omit=function(e,t,n){if(x.isFunction(t))t=x.negate(t);else{var r=x.map(P(arguments,!1,!1,1),String);t=function(e,t){return!x.contains(r,t)}}return x.pick(e,t,n)},x.defaults=S(x.allKeys,!0),x.create=function(e,t){var n=E(e);return t&&x.extendOwn(n,t),n},x.clone=function(e){return x.isObject(e)?x.isArray(e)?e.slice():x.extend({},e):e},x.tap=function(e,t){return t(e),e},x.isMatch=function(e,t){var n=x.keys(t),r=n.length;if(null==e)return!r;for(var i=Object(e),a=0;a<r;a++){var s=n[a];if(t[s]!==i[s]||!(s in i))return!1}return!0};var B=function(e,t,n,r){if(e===t)return 0!==e||1/e===1/t;if(null==e||null==t)return e===t;e instanceof x&&(e=e._wrapped),t instanceof x&&(t=t._wrapped);var i=m.call(e);if(i!==m.call(t))return!1;switch(i){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!==+e?+t!==+t:0===+e?1/+e===1/t:+e===+t;case"[object Date]":case"[object Boolean]":return+e===+t}var a="[object Array]"===i;if(!a){if("object"!=typeof e||"object"!=typeof t)return!1;var s=e.constructor,o=t.constructor;if(s!==o&&!(x.isFunction(s)&&s instanceof s&&x.isFunction(o)&&o instanceof o)&&"constructor"in e&&"constructor"in t)return!1}n=n||[],r=r||[];for(var u=n.length;u--;)if(n[u]===e)return r[u]===t;if(n.push(e),r.push(t),a){if(u=e.length,u!==t.length)return!1;for(;u--;)if(!B(e[u],t[u],n,r))return!1}else{var c,f=x.keys(e);if(u=f.length,x.keys(t).length!==u)return!1;for(;u--;)if(c=f[u],!x.has(t,c)||!B(e[c],t[c],n,r))return!1}return n.pop(),r.pop(),!0};x.isEqual=function(e,t){return B(e,t)},x.isEmpty=function(e){return null==e||(C(e)&&(x.isArray(e)||x.isString(e)||x.isArguments(e))?0===e.length:0===x.keys(e).length)},x.isElement=function(e){return!(!e||1!==e.nodeType)},x.isArray=y||function(e){return"[object Array]"===m.call(e)},x.isObject=function(e){var t=typeof e;return"function"===t||"object"===t&&!!e},x.each(["Arguments","Function","String","Number","Date","RegExp","Error"],function(e){x["is"+e]=function(t){return m.call(t)==="[object "+e+"]"}}),x.isArguments(arguments)||(x.isArguments=function(e){return x.has(e,"callee")}),"function"!=typeof/./&&"object"!=typeof Int8Array&&(x.isFunction=function(e){return"function"==typeof e||!1}),x.isFinite=function(e){return isFinite(e)&&!isNaN(parseFloat(e))},x.isNaN=function(e){return x.isNumber(e)&&e!==+e},x.isBoolean=function(e){return e===!0||e===!1||"[object Boolean]"===m.call(e)},x.isNull=function(e){return null===e},x.isUndefined=function(e){return void 0===e},x.has=function(e,t){return null!=e&&v.call(e,t)},x.noConflict=function(){return u._=c,this},x.identity=function(e){return e},x.constant=function(e){return function(){return e}},x.noop=function(){},x.property=I,x.propertyOf=function(e){return null==e?function(){}:function(t){return e[t]}},x.matcher=x.matches=function(e){return e=x.extendOwn({},e),function(t){return x.isMatch(t,e)}},x.times=function(e,t,n){var r=Array(Math.max(0,e));t=k(t,n,1);for(var i=0;i<e;i++)r[i]=t(i);return r},x.random=function(e,t){return null==t&&(t=e,e=0),e+Math.floor(Math.random()*(t-e+1))},x.now=Date.now||function(){return(new Date).getTime()};var U={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},N=x.invert(U),F=function(e){var t=function(t){return e[t]},n="(?:"+x.keys(e).join("|")+")",r=RegExp(n),i=RegExp(n,"g");return function(e){return e=null==e?"":""+e,r.test(e)?e.replace(i,t):e}};x.escape=F(U),x.unescape=F(N),x.result=function(e,t,n){var r=null==e?void 0:e[t];return void 0===r&&(r=n),x.isFunction(r)?r.call(e):r};var q=0;x.uniqueId=function(e){var t=++q+"";return e?e+t:t},x.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var X=/(.)^/,V={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},Y=/\\|'|\r|\n|\u2028|\u2029/g,j=function(e){return"\\"+V[e]};x.template=function(e,t,n){!t&&n&&(t=n),t=x.defaults({},t,x.templateSettings);var r=RegExp([(t.escape||X).source,(t.interpolate||X).source,(t.evaluate||X).source].join("|")+"|$","g"),i=0,a="__p+='";e.replace(r,function(t,n,r,s,o){return a+=e.slice(i,o).replace(Y,j),i=o+t.length,n?a+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'":r?a+="'+\n((__t=("+r+"))==null?'':__t)+\n'":s&&(a+="';\n"+s+"\n__p+='"),t}),a+="';\n",t.variable||(a="with(obj||{}){\n"+a+"}\n"),a="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+a+"return __p;\n";try{var s=new Function(t.variable||"obj","_",a)}catch(o){throw o.source=a,o}var u=function(e){return s.call(this,e,x)},c=t.variable||"obj";return u.source="function("+c+"){\n"+a+"}",u},x.chain=function(e){var t=x(e);return t._chain=!0,t};var Q=function(e,t){return e._chain?x(t).chain():t};x.mixin=function(e){x.each(x.functions(e),function(t){var n=x[t]=e[t];x.prototype[t]=function(){var e=[this._wrapped];return h.apply(e,arguments),Q(this,n.apply(x,e))}})},x.mixin(x),x.each(["pop","push","reverse","shift","sort","splice","unshift"],function(e){var t=f[e];x.prototype[e]=function(){var n=this._wrapped;return t.apply(n,arguments),"shift"!==e&&"splice"!==e||0!==n.length||delete n[0],Q(this,n)}}),x.each(["concat","join","slice"],function(e){var t=f[e];x.prototype[e]=function(){return Q(this,t.apply(this._wrapped,arguments))}}),x.prototype.value=function(){return this._wrapped},x.prototype.valueOf=x.prototype.toJSON=x.prototype.value,x.prototype.toString=function(){return""+this._wrapped},r=[],i=function(){return x}.apply(t,r),!(void 0!==i&&(e.exports=i))}).call(this)},,,function(e,t,n){var r,i;r=[n(56),n(54),n(55),n(15),n(1),n(6),n(3),n(101)],i=function(e,t,n,r,i,a,s,o){function u(e){for(var t=e.seekable?e.seekable.length:0,n=0;t--;)n=Math.max(n,e.seekable.end(t));return n}function c(c,l){function d(){h(),S.on("levelsChanged",function(e){v(e)}).on("levels",function(e){m(e),k=e.currentQuality||0}).on("bufferFull",function(e){x.trigger("bufferFull",e)}).on("meta",function(e){x.trigger("meta",e)}).on("hls-metadata-bufferchange",function(e){x.trigger("hls-metadata-bufferchange",e)}).on("error",function(e){x.trigger("mediaError",{code:-1,message:e})}).on("trackTypes",function(e){var t=1===e.length&&("audio"===e[0]||"audio"===e[0].type);x.trigger("mediaType",{mediaType:t?"audio":"video"})}).on("cueAdded",function(e){x.addVTTCue(e)}),x.on("adaptation",function(e){var t=S.getConfiguration();if(t){var n=t.enableAdaptation,r=t.currentLevel,i=t.levels[r],a=n?"auto":"manual",s=n?"auto":"api";this.initialChoice&&(a="auto",s="initial choice",this.initialChoice=!1),this.trigger("visualQuality",{level:{index:r,label:i.label,bitrate:i.bitrate,width:e.size.width,height:e.size.height},reason:s,mode:a}),this.trigger("providerFirstFrame",{})}},x),i.each(w,function(e,t){T.addEventListener(t,e,!1)})}function h(){S.off(),x.off("adaptation"),i.each(w,function(e,t){T.removeEventListener(t,e)}),x.removeTracksListener(T.textTracks,"change",x.textTrackChangeHandler)}function p(e){m(e),v(e)}function m(e){b!==e.levels&&(b=e.levels,x.trigger("levels",e))}function v(e){k!==e.currentQuality&&(k=e.currentQuality,x.trigger("levelsChanged",e))}function y(e){"none"!==e.preload&&x.loadedItem!==e&&g(e)}function g(e){if(x.loadedItem!==e){d();var t=new window.MediaSource;T.src=URL.createObjectURL(t);var n=e.sources[0].withCredentials,r=e.minDvrWindow,i=e.preload;S.setup(T,t,i,n,r),x.loadedItem=e,S.inited()||x.init(e);var a=e.sources[0].file,s=x;if(e.starttime){var o=function(t){var n=t.target;n.removeEventListener("loadedmetadata",o),s.seek(e.starttime)};x.video.removeEventListener("loadedmetadata",o),x.video.addEventListener("loadedmetadata",o,!1)}x.initialChoice=!0,S.initStream(a,l.qualityLabel)}}function _(){x.loadedItem=null,h(),x.clearTracks(),S.destroy()}var b,T=t.getVideo.call(this,c),x=this,k=0,w={};i.each(e,function(e,t){"function"==typeof e?w[t]=e.bind(x):x[t]=e});var S=new o.Caterpillar(f);i.extend(this,s,t,n,r,{trigger:function(e,t){if(this.attached)return"mediaError"===e&&(3===t.code&&"mp4a.40.5"===S.getAudioCodec()&&(t.message="Error playing file: HE-AAC not supported"),this.eventsOff_()),s.trigger.call(this,e,t)},eventsOn_:d,eventsOff_:function(){h(),S.destroy()},play:function(){if(this.state===a.PAUSED&&S.getDuration()===1/0){var e=this.loadedItem;return _(),this.init(e),void this.load(e)}this.seeking&&this.setState(a.LOADING),S.play()},pause:function(){this.stopStallCheck(),S.pause(),this.setState(a.PAUSED)},stop:function(){this.stopStallCheck(),this.attached&&(_(),T.pause(),T.removeAttribute("src"),T.load(),this.setState(a.IDLE))},seek:function(e){if(this.attached){e<0&&(e+=this.video.duration);var t=u(T),n=!!t,r=this;if(!n){var i=function(t){var n=t.target;n.removeEventListener("loadedmetadata",i),r.seek(e)};return this.video.removeEventListener("loadedmetadata",i),void this.video.addEventListener("loadedmetadata",i,!1)}e=Math.min(e,t-.01),this.trigger("seek",{position:this.getCurrentTime(),offset:e});var a=S.getDuration();this.trigger("time",{duration:a,position:S.getExternalTime(e)}),0!==a&&a!==1/0&&(this.seeking=!0,S.seek(e))}},setCurrentQuality:function(e){if(k!==e&&!(e<0)&&b&&!(b.length<=e)){if(S.setCurrentQuality(e),b&&b[e]){var t=b[e].label;l.qualityLabel=t}p({levels:b,currentQuality:e})}},init:function(e){this.attached&&(_(),y(e),this.setupSideloadedTracks(e.tracks))},load:function(e){this.attached&&(g(e),S.beginStream(),this.setState(a.LOADING))},getDuration:function(){return S.getDuration()},getCurrentTime:function(){return S.getCurrentTime()},getPtsOffset:function(){return S.getPtsOffset()},destroy:function(){b=null,k=null,S&&(_(),S=null)},getQualityLevels:function(){return b},getCurrentQuality:function(){return k},getName:i.constant({name:"caterpillar"})})}var f;return c.getName=function(){return{name:"caterpillar"}},c.setEdition=function(e){f=e},c}.apply(t,r),!(void 0!==i&&(e.exports=i))},,,,,,function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){return{is_closed:function(){return t.is_closed()},close:function(){t.close()},_put:function(e,n){return t._put(e,n)},_take:function(n){var r=t._take({is_active:function(){return n.is_active()},commit:function(){var t=n.commit();return function(n){return t(n===Y?Y:e(n))}}});if(r){var i=r.value;return new O(i===Y?Y:e(i))}return null}}}function a(e,t){return{is_closed:function(){return t.is_closed()},close:function(){t.close()},_put:function(n,r){return t._put(e(n),r)},_take:function(e){return t._take(e)}}}function s(e,t,n){var r=V(n);return B(P["default"].mark(function i(){var n;return P["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=3,U(t);case 3:if(n=i.sent,n!==Y){i.next=7;break}return r.close(),i.abrupt("break",12);case 7:if(!e(n)){i.next=10;break}return i.next=10,N(r,n);case 10:i.next=0;break;case 12:case"end":return i.stop()}},i,this)})),r}function o(e,t){return{is_closed:function(){return t.is_closed()},close:function(){t.close()},_put:function(n,r){return e(n)?t._put(n,r):new O((!t.is_closed()))},_take:function(e){return t._take(e)}}}function u(e,t){return s(function(t){return!e(t)},t)}function c(e,t){return o(function(t){return!e(t)},t)}function f(e,t,n){var r,i,a,s;return P["default"].wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=3,U(t);case 3:if(r=o.sent,r!==Y){o.next=9;break}return n.close(),o.abrupt("break",22);case 9:i=e(r),a=i.length,s=0;case 12:if(!(s<a)){o.next=18;break}return o.next=15,N(n,i[s]);case 15:s++,o.next=12;break;case 18:if(!n.is_closed()){o.next=20;break}return o.abrupt("break",22);case 20:o.next=0;break;case 22:case"end":return o.stop()}},D[0],this)}function l(e,t,n){var r=V(n);return B(f,[e,t,r]),r}function d(e,t,n){var r=V(n);return B(f,[e,r,t]),r}function h(e,t,n){return B(P["default"].mark(function r(){var i;return P["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=3,U(e);case 3:if(i=r.sent,i!==Y){r.next=7;break}return n||t.close(),r.abrupt("break",13);case 7:return r.next=9,N(t,i);case 9:if(r.sent){r.next=11;break}return r.abrupt("break",13);case 11:r.next=0;break;case 13:case"end":return r.stop()}},r,this)})),t}function p(e,t,n,r){var i=V(n),a=V(r);return B(P["default"].mark(function s(){var n;return P["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=3,U(t);case 3:if(n=r.sent,n!==Y){r.next=8;break}return i.close(),a.close(),r.abrupt("break",12);case 8:return r.next=10,N(e(n)?i:a,n);case 10:r.next=0;break;case 12:case"end":return r.stop()}},s,this)})),[i,a]}function m(e,t,n){return B(P["default"].mark(function r(){var i,a;return P["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:i=t;case 1:return r.next=4,U(n);case 4:if(a=r.sent,a!==Y){r.next=9;break}return r.abrupt("return",i);case 9:i=e(i,a);case 10:r.next=1;break;case 12:case"end":return r.stop()}},r,this)}),[],!0)}function v(e,t,n){return B(P["default"].mark(function r(){var i,a;return P["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:i=t.length,a=0;case 2:if(!(a<i)){r.next=8;break}return r.next=5,N(e,t[a]);case 5:a++,r.next=2;break;case 8:n||e.close();case 9:case"end":return r.stop()}},r,this)}))}function y(e){var t=V(e.length);return v(t,e),t}function g(e,t,n){for(var r,i=V(n),a=t.length,s=new Array(a),o=V(1),u=new Array(a),c=0;c<a;c++)u[c]=function(e){return function(t){s[e]=t,r--,0===r&&q(o,s.slice(0))}}(c);return B(P["default"].mark(function f(){var n,s;return P["default"].wrap(function(c){
for(;;)switch(c.prev=c.next){case 0:for(r=a,n=0;n<a;n++)try{F(t[n],u[n])}catch(f){r--}return c.next=5,U(o);case 5:s=c.sent,n=0;case 7:if(!(n<a)){c.next=14;break}if(s[n]!==Y){c.next=11;break}return i.close(),c.abrupt("return");case 11:n++,c.next=7;break;case 14:return c.next=16,N(i,e.apply(null,s));case 16:c.next=0;break;case 18:case"end":return c.stop()}},f,this)})),i}function _(e,t){var n=V(t),r=e.slice(0);return B(P["default"].mark(function i(){var e,t,a;return P["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(0!==r.length){i.next=3;break}return i.abrupt("break",15);case 3:return i.next=5,X(r);case 5:if(e=i.sent,t=e.value,t!==Y){i.next=11;break}return a=r.indexOf(e.channel),r.splice(a,1),i.abrupt("continue",0);case 11:return i.next=13,N(n,t);case 13:i.next=0;break;case 15:n.close();case 16:case"end":return i.stop()}},i,this)})),n}function b(e,t){var n=e.slice(0);return m(function(e,t){return e.push(t),e},n,t)}function T(e,t,n){var r=V(n);return B(P["default"].mark(function i(){var n,a;return P["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:n=0;case 1:if(!(n<e)){i.next=12;break}return i.next=4,U(t);case 4:if(a=i.sent,a!==Y){i.next=7;break}return i.abrupt("break",12);case 7:return i.next=9,N(r,a);case 9:n++,i.next=1;break;case 12:r.close();case 13:case"end":return i.stop()}},i,this)})),r}function x(e,t){var n=V(t),r=j;return B(P["default"].mark(function i(){var t;return P["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=3,U(e);case 3:if(t=i.sent,t!==Y){i.next=6;break}return i.abrupt("break",13);case 6:if(t!==r){i.next=8;break}return i.abrupt("continue",0);case 8:return r=t,i.next=11,N(n,t);case 11:i.next=0;break;case 13:n.close();case 14:case"end":return i.stop()}},i,this)})),n}function k(e,t,n){var r=V(n),i=[],a=j;return B(P["default"].mark(function s(){var n,o;return P["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=3,U(t);case 3:if(n=s.sent,n!==Y){s.next=12;break}if(!(i.length>0)){s.next=8;break}return s.next=8,N(r,i);case 8:return r.close(),s.abrupt("break",23);case 12:if(o=e(n),o!==a&&a!==j){s.next=17;break}i.push(n),s.next=20;break;case 17:return s.next=19,N(r,i);case 19:i=[n];case 20:a=o;case 21:s.next=0;break;case 23:case"end":return s.stop()}},s,this)})),r}function w(e,t,n){var r=V(n);return B(P["default"].mark(function i(){var n,a,s;return P["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:n=new Array(e),a=0;case 3:if(!(a<e)){i.next=17;break}return i.next=6,U(t);case 6:if(s=i.sent,s!==Y){i.next=13;break}if(!(a>0)){i.next=11;break}return i.next=11,N(r,n.slice(0,a));case 11:return r.close(),i.abrupt("return");case 13:n[a]=s;case 14:a++,i.next=3;break;case 17:return i.next=19,N(r,n);case 19:i.next=0;break;case 21:case"end":return i.stop()}},i,this)})),r}function S(e){var t=0;for(var n in e)t++;return t}function E(e){var t=e[K];return void 0===t&&(t=e[K]=Q()),t}function I(e){function t(e){return function(t){n--,0===n&&q(i,!0),t||r.untap(e.channel)}}var n,r=new G(e),i=V(1);return B(P["default"].mark(function a(){var s,o,u,c,f;return P["default"].wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=3,U(e);case 3:if(s=a.sent,c=r.taps,s!==Y){a.next=9;break}for(o in c)u=c[o],u.keepOpen||u.channel.close();return r.untapAll(),a.abrupt("break",17);case 9:n=S(c),f=n;for(o in c)u=c[o],q(u.channel,s,t(u));if(!(f>0)){a.next=15;break}return a.next=15,U(i);case 15:a.next=0;break;case 17:case"end":return a.stop()}},a,this)})),r}function A(e){var t=new H(e);return B(P["default"].mark(function n(){var r,i,a,s,o,u;return P["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r=t._getAllState();case 1:return n.next=4,X(r.reads);case 4:if(i=n.sent,a=i.value,s=i.channel,a!==Y){n.next=11;break}return delete t.stateMap[E(s)],r=t._getAllState(),n.abrupt("continue",1);case 11:if(s!==t.change){n.next=14;break}return r=t._getAllState(),n.abrupt("continue",1);case 14:if(o=r.solos,!(o.indexOf(s)>-1)&&(0!==o.length||r.mutes.indexOf(s)>-1)){n.next=21;break}return n.next=18,N(e,a);case 18:if(u=n.sent){n.next=21;break}return n.abrupt("break",23);case 21:n.next=1;break;case 23:case"end":return n.stop()}},n,this)})),t}function M(){return null}function C(e,t,n){n=n||M;var r=new $(e,t,n);return B(P["default"].mark(function i(){var n,a,s,o,u;return P["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=3,U(e);case 3:if(n=i.sent,a=r.mults,n!==Y){i.next=8;break}for(s in a)a[s].muxch().close();return i.abrupt("break",17);case 8:if(s=t(n),o=a[s],!o){i.next=15;break}return i.next=13,N(o.muxch(),n);case 13:u=i.sent,u||delete a[s];case 15:i.next=0;break;case 17:case"end":return i.stop()}},i,this)})),r}var L=n(7),P=r(L),D=[f].map(P["default"].mark),O=n(21).Box,R=n(34),B=R.go,U=R.take,N=R.put,F=R.takeAsync,q=R.putAsync,X=R.alts,V=R.chan,Y=R.CLOSED,j={},Q=function(){var e=0;return function(){return e++,""+e}}(),K="__csp_channel_id",G=function(e){this.taps={},this.ch=e},z=function(e,t){this.channel=e,this.keepOpen=t};G.prototype.muxch=function(){return this.ch},G.prototype.tap=function(e,t){var n=E(e);this.taps[n]=new z(e,t)},G.prototype.untap=function(e){delete this.taps[E(e)]},G.prototype.untapAll=function(){this.taps={}},I.tap=function(e,t,n){return e.tap(t,n),t},I.untap=function(e,t){e.untap(t)},I.untapAll=function(e){e.untapAll()};var H=function(e){this.ch=e,this.stateMap={},this.change=V(),this.soloMode=A.MUTE};H.prototype._changed=function(){q(this.change,!0)},H.prototype._getAllState=function(){var e,t=this.stateMap,n=[],r=[],i=[];for(var a in t){var s=t[a],o=s.state,u=s.channel;o[A.SOLO]&&n.push(u),o[A.MUTE]&&r.push(u),o[A.PAUSE]&&i.push(u)}var c,f;if(this.soloMode===A.PAUSE&&n.length>0){for(f=n.length,e=new Array(f+1),c=0;c<f;c++)e[c]=n[c];e[f]=this.change}else{e=[];for(a in t)s=t[a],u=s.channel,i.indexOf(u)<0&&e.push(u);e.push(this.change)}return{solos:n,mutes:r,reads:e}},H.prototype.admix=function(e){this.stateMap[E(e)]={channel:e,state:{}},this._changed()},H.prototype.unmix=function(e){delete this.stateMap[E(e)],this._changed()},H.prototype.unmixAll=function(){this.stateMap={},this._changed()},H.prototype.toggle=function(e){for(var t=e.length,n=0;n<t;n++){var r=e[n][0],i=E(r),a=e[n][1],s=this.stateMap[i];s||(s=this.stateMap[i]={channel:r,state:{}});for(var o in a)s.state[o]=a[o]}this._changed()},H.prototype.setSoloMode=function(e){if(W.indexOf(e)<0)throw new Error("Mode must be one of: ",W.join(", "));this.soloMode=e,this._changed()},A.MUTE="mute",A.PAUSE="pause",A.SOLO="solo";var W=[A.MUTE,A.PAUSE];A.add=function(e,t){e.admix(t)},A.remove=function(e,t){e.unmix(t)},A.removeAll=function(e){e.unmixAll()},A.toggle=function(e,t){e.toggle(t)},A.setSoloMode=function(e,t){e.setSoloMode(t)};var $=function(e,t,n){this.ch=e,this.topicFn=t,this.bufferFn=n,this.mults={}};$.prototype._ensureMult=function(e){var t=this.mults[e],n=this.bufferFn;return t||(t=this.mults[e]=I(V(n(e)))),t},$.prototype.sub=function(e,t,n){var r=this._ensureMult(e);return I.tap(r,t,n)},$.prototype.unsub=function(e,t){var n=this.mults[e];n&&I.untap(n,t)},$.prototype.unsubAll=function(e){void 0===e?this.mults={}:delete this.mults[e]},C.sub=function(e,t,n,r){return e.sub(t,n,r)},C.unsub=function(e,t,n){e.unsub(t,n)},C.unsubAll=function(e,t){e.unsubAll(t)},e.exports={mapFrom:i,mapInto:a,filterFrom:s,filterInto:o,removeFrom:u,removeInto:c,mapcatFrom:l,mapcatInto:d,pipe:h,split:p,reduce:m,onto:v,fromColl:y,map:g,merge:_,into:b,take:T,unique:x,partition:w,partitionBy:k,mult:I,mix:A,pub:C}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t,n,r,i){if(e<=0)throw new Error("n must be positive");for(var a=c.chan(e),s=c.chan(e),o=0;o<e;o++)c.go(u["default"].mark(function f(e,t,n){var r;return u["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=3,c.take(t);case 3:if(r=i.sent,e(r)){i.next=7;break}return n.close(),i.abrupt("break",9);case 7:i.next=0;break;case 9:case"end":return i.stop()}},f,this)}),[i,a,s]);return c.go(u["default"].mark(function l(e,t,n){var r,i;return u["default"].wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=3,c.take(t);case 3:if(r=a.sent,r!==c.CLOSED){a.next=9;break}return e.close(),a.abrupt("break",16);case 9:return i=c.chan(1),a.next=12,c.put(e,[r,i]);case 12:return a.next=14,c.put(n,i);case 14:a.next=0;break;case 16:case"end":return a.stop()}},l,this)}),[a,n,s]),c.go(u["default"].mark(function d(e,t,n){var r,i,a;return u["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=3,c.take(e);case 3:if(r=s.sent,r!==c.CLOSED){s.next=9;break}return t&&n.close(),s.abrupt("break",26);case 9:return s.next=11,c.take(r);case 11:i=s.sent;case 12:return s.next=15,c.take(i);case 15:if(a=s.sent,a===c.CLOSED){s.next=21;break}return s.next=19,c.put(n,a);case 19:s.next=22;break;case 21:return s.abrupt("break",24);case 22:s.next=12;break;case 24:s.next=0;break;case 26:case"end":return s.stop()}},d,this)}),[s,r,t]),t}function a(e,t,n,r,a){function s(e){if(e===c.CLOSED)return null;var n=e[0],r=e[1],i=c.chan(1,t,a);return c.go(u["default"].mark(function s(e,t){return u["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,c.put(e,t);case 2:e.close();case 3:case"end":return n.stop()}},s,this)}),[i,n]),c.putAsync(r,i),!0}return i(1,e,n,!r,s)}function s(e,t,n,r,a){function s(e){if(e===c.CLOSED)return null;var t=e[0],r=e[1],i=c.chan(1);return n(t,i),c.putAsync(r,i),!0}return i(e,t,r,!a,s)}var o=n(7),u=r(o),c=n(34);e.exports={pipeline:a,pipelineAsync:s}},function(e,t,n){"use strict";function r(e,t,n){var r=e._put(t,new m((!0),n));r&&n&&n(r.value)}function i(e,t){var n=e._take(new m((!0),t));n&&t(n.value)}function a(e){return new y(g,e)}function s(e,t){return new y(_,{channel:e,value:t})}function o(e){if(e.closed)return p;var t=e._take(new m((!1)));return t?t.value:p}function u(e,t){if(e.closed)return!1;var n=e._put(t,new m((!1)));return!!n}function c(e){return new y(b,e)}function f(e,t){return new y(T,{operations:e,options:t})}var l=n(36),d=n(57),h=n(21).Channel,p={},m=function(e,t){this.f=t,this.blockable=e};m.prototype.is_active=function(){return!0},m.prototype.is_blockable=function(){return this.blockable},m.prototype.commit=function(){return this.f};var v=function(e,t,n){this.gen=e,this.creatorFunc=n,this.finished=!1,this.onFinish=t},y=function(e,t){this.op=e,this.data=t},g="take",_="put",b="sleep",T="alts";v.prototype._continue=function(e){var t=this;l.run(function(){t.run(e)})},v.prototype._done=function(e){if(!this.finished){this.finished=!0;var t=this.onFinish;"function"==typeof t&&l.run(function(){t(e)})}},v.prototype.run=function(e){if(!this.finished){var t=this.gen.next(e);if(t.done)return void this._done(t.value);var n=t.value,a=this;if(n instanceof y)switch(n.op){case _:var s=n.data;r(s.channel,s.value,function(e){a._continue(e)});break;case g:var o=n.data;i(o,function(e){a._continue(e)});break;case b:var u=n.data;l.queue_delay(function(){a.run(null)},u);break;case T:d.do_alts(n.data.operations,function(e){a._continue(e)},n.data.options)}else if(n instanceof h){var o=n;i(o,function(e){a._continue(e)})}else this._continue(n)}},t.put_then_callback=r,t.take_then_callback=i,t.put=s,t.take=a,t.offer=u,t.poll=o,t.sleep=c,t.alts=f,t.Instruction=y,t.Process=v,t.NO_VALUE=p},function(e,t,n){"use strict";var r=n(36),i=n(21);t.timeout=function(e){var t=i.chan();return r.queue_delay(function(){t.close()},e),t}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.QualityArbiter=void 0;var a=n(60),s=n(30),o=r(s),u=n(3),c=(n(4),n(1)),f=t.QualityArbiter=void 0;!function(e){e[e.MANUAL=0]="MANUAL",e[e.ADAPTIVE=1]="ADAPTIVE"}(f||(t.QualityArbiter=f={}));var l=4,d=30,h=2,p=15,m=45,v=1e4,y=3e3,g=2,_=1.5,b=1.5,T=.001,x=function(){function e(){i(this,e),this.adaptiveQualityLevel=-1,this.manualQualityLevel=-1,this.lastAdaptiveSwitchTime_=0,this.isChangingQuality=!1,this.streamType="LIVE",this.safetyFactor=g,this.streamInitialQuality=0,c.extend(this,u)}return e.prototype.whatToDownload=function(e,t){var n=this;return this.getState_(e,t).then(function(e){return e?"LIVE"===n.streamType?n.liveAdaptive_(e):n.whatToDownload_(e):null}).then(function(e){return{segment:e,opts:{reason:"none"}}}.bind(this))},e.prototype.setQuality=function(e,t){this.lastAdaptiveSwitchTime_=c.now()-(v-y),this.lastTimeWithEnoughBuffer=c.now();var n=e-1;0===e&&t.length>1?(this.adaptiveQualityLevel=this.manualQualityLevel,this.desiredQualityArbiter=f.ADAPTIVE,this.trigger("quality-change",this.adaptiveQualityLevel)):(this.desiredQualityArbiter=f.MANUAL,this.manualQualityLevel=n,this.trigger("quality-change",n))},e.prototype.updateLevels=function(e){this.manualQualityLevel=e,this.adaptiveQualityLevel=e},e.prototype.getQuality=function(){return this.desiredQualityArbiter===f.ADAPTIVE?this.adaptiveQualityLevel:this.manualQualityLevel},e.prototype.changeAdaptiveQuality_=function(e,t,n){return!!this.allowQualitySwitching_(e,t,this.adaptiveQualityLevel,n)&&(this.lastAdaptiveSwitchTime_=e.now,"VOD"===this.streamType&&(this.isChangingQuality=!0),e.action.push("Quality switch from "+this.adaptiveQualityLevel+" to "+n),this.adaptiveQualityLevel=n,this.trigger("quality-change",n),!0)},e.prototype.allowQualitySwitching_=function(e,t,n,r){if("VOD"===this.streamType)return e.videoEnd-t>d;e.manifestRepresentation.requestTS(t,t,n),e.manifestRepresentation.requestTS(t,t,r);var i=e.sortedLevels[n],a=e.sortedLevels[r];if(!(i&&a&&i.segments.length&&a.segments.length))return!1;var s=o["default"].getRanges(i.segments,t,t)[0];return a.containsSegment(s)},e.prototype.adaptationEnabled=function(){return this.desiredQualityArbiter===f.ADAPTIVE},e.timeToDownload_=function(e,t){var n=t.segmentFileSize.average()/1e3,r=t.streamInfo.bandwidth/8/1e3*t.streamInfo.targetDuration;n=n?Math.max(n,r):1.5*r;var i=T*n;return n/e.bandwidth+i},e.minForwardBuffer_=function(t,n,r){var i=e.timeToDownload_(t,n);return Math.min(r,Math.max(p,i,n.streamInfo.targetDuration))},e.prototype.earliestSegmentToLoad=function(t){var n=this;return t.manifestRepresentation.requestTS(t.currentTime,t.maxBufferEnd,this.getQuality()).then(function(r){var i=c.last(r);if(r.length)for(var a=t.runway-t.minForwardBuffer,s=e.timeToDownload_(t,t.sortedLevels[n.getQuality()]),o=0,u=r.length-1;u>=1;u--){if(o+=2*s,o>a)return n.isChangingQuality=!1,i;i=r[u]}return n.isChangingQuality=!1,i})},e.maxForwardBuffer_=function(t,n){var r=e.timeToDownload_(t,n);return Math.min(m,Math.max(p,r*h,n.streamInfo.targetDuration*h))},e.prototype.getState_=function(t,n){return t.manifestRepresentation.doneLoading().then(function(r){if(!t.mediaDataModel)return null;var i=t.mediaDataModel.offsetTime!==-1?t.mediaDataModel.offsetTime:t.video.currentTime;t.mediaDataModel.offsetTime=-1;var s=r.end;if(n){this.streamType=t.manifestRepresentation.streamType;var o=r.levels[0];this.streamInitialQuality=c.indexOf(r.sortedLevels,o),this.manualQualityLevel===-1&&(this.adaptiveQualityLevel=this.streamInitialQuality,this.manualQualityLevel=this.streamInitialQuality)}"VOD"!==r.streamType&&0===i&&(i=a.StreamingEngine.getLiveStartTime(r));var u=r.sortedLevels[this.getQuality()],f=t.network.status(),h=Math.max(1,f.bandwidth),p=f.latency,m=u.streamInfo.targetDuration,v=i+t.maxForwardBuffer(),y=Math.max(v-i-p,0),g=l,_=d;if("VOD"!==this.streamType){g=Math.min(g,m);var b=u.segments.length?c.last(u.segments).end:s;_=Math.min(_,b-i-u.streamInfo.targetDuration)}var T=i+_+m,x=e.minForwardBuffer_(f,u,_),k=e.maxForwardBuffer_(f,u),w=i+k,S=t.mediaDataModel.currentTransmuxSession,E=S&&S.tmsArray.length?S:void 0,I=!E||E.forwardBufferCleared,A=E?E.tmsArray[0].ts:void 0;return n&&(y=0,v=i,x=1,f.requestsOutstanding=0),{requestsOutstanding:f.requestsOutstanding,bandwidth:h,runway:y,currentTime:i,videoEnd:s,currentBufferEnd:v,targetDuration:m,firstSessionTSSegment:A,sessionBufferCleared:I,minForwardBuffer:x,maxBufferEnd:w,upswitchBufferTime:T,minBufferForDownswitch:g,minBufferForUpswitch:_,action:[],now:c.now(),manifestRepresentation:t.manifestRepresentation,sortedLevels:r.sortedLevels,mdm:t.mediaDataModel,videoBuffers:t.video.buffered}}.bind(this))},e.prototype.liveAdaptive_=function(e){if(this.desiredQualityArbiter===f.MANUAL)return this.firstSegmentToBuffer_(e,e.currentBufferEnd,e.maxBufferEnd);var t=e.now-this.lastAdaptiveSwitchTime_,n=e.sortedLevels.length-1;if(e.now-this.lastTimeWithEnoughBuffer>5e3&&t>v&&this.adaptiveQualityLevel<n&&(this.changeAdaptiveQuality_(e,e.currentBufferEnd,n),this.safetyFactor*=_),e.runway<e.minBufferForDownswitch&&e.sessionBufferCleared)return this.firstSegmentToBuffer_(e,e.currentTime,e.currentTime+e.minBufferForDownswitch+e.targetDuration);if(this.lastTimeWithEnoughBuffer=e.now,!e.sessionBufferCleared)return this.firstSegmentToBuffer_(e,e.firstSessionTSSegment.end,e.firstSessionTSSegment.end);if(!(e.requestsOutstanding>1)){var r=this.targetUpswitchLevel_(e);return t>v&&r<this.adaptiveQualityLevel&&this.changeAdaptiveQuality_(e,e.currentBufferEnd,r),this.firstSegmentToBuffer_(e,e.currentBufferEnd,e.videoEnd)}},e.prototype.whatToDownload_=function(e){if(!this.isChangingQuality){if(this.desiredQualityArbiter===f.MANUAL)return this.firstSegmentToBuffer_(e,e.currentBufferEnd,e.maxBufferEnd);var t=e.now-this.lastAdaptiveSwitchTime_,n=e.sortedLevels.length-1;if(e.now-this.lastTimeWithEnoughBuffer>5e3&&t>v&&this.adaptiveQualityLevel!==n&&(this.changeAdaptiveQuality_(e,e.currentBufferEnd,n),this.isChangingQuality=!1),e.runway<e.minBufferForDownswitch&&e.sessionBufferCleared)return this.firstSegmentToBuffer_(e,e.currentBufferEnd,e.currentTime+e.minBufferForDownswitch+e.targetDuration);if(this.lastTimeWithEnoughBuffer=e.now,!e.sessionBufferCleared)return this.firstSegmentToBuffer_(e,e.firstSessionTSSegment.end,e.firstSessionTSSegment.end);if(!(e.requestsOutstanding>1)){if(e.runway<e.minBufferForUpswitch||t<v)return this.firstSegmentToBuffer_(e,e.currentBufferEnd,e.upswitchBufferTime);var r=this.targetUpswitchLevel_(e);return r<this.adaptiveQualityLevel&&this.changeAdaptiveQuality_(e,e.currentBufferEnd,r)?this.earliestSegmentToLoad(e):void 0}}},e.prototype.firstSegmentToBuffer_=function(e,t,n){var r=.01,i=this.getQuality(),a=function s(t){if(0!==t.length&&t[0]){if(e.mdm.currentTransmuxSession&&e.mdm.currentTransmuxSession.isSendingData&&1===e.mdm.currentTransmuxSession.tmsArray.length)return Promise.resolve(void 0);for(var n=0;n<t.length;n++){var a=t[n];if(!a.errorInSegment&&!e.mdm.getTransmuxSegment(a)&&(!o["default"].isSegmentTimesBuffered(e.videoBuffers,a)||a.mediaSequenceId>e.firstSessionTSSegment.mediaSequenceId))return Promise.resolve(a)}var u=c.last(t);return u.errorInSegment&&!u.isLast?e.manifestRepresentation.requestTS(u.end+r,u.end+2*r,i).then(s):Promise.resolve(void 0)}};return e.manifestRepresentation.requestTS(t+r,n,i).then(a)},e.prototype.targetUpswitchLevel_=function(t){for(var n=8*t.bandwidth,r=t.sortedLevels.length-1,i=0;i<r;i++){var a=t.sortedLevels[i],s=e.timeToDownload_(t,a)*this.safetyFactor,o=a.streamInfo.bandwidth/1e3,u=o*b;if(u<n&&t.runway-s>t.minForwardBuffer)return i}return r},e}();t["default"]=x},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.Caterpillar=void 0;var i=n(60),a=n(39),s=n(58),o=(n(26),n(3)),u=n(1);n(5),t.Caterpillar=function(){function e(t){r(this,e),u.extend(this,o),this.edition=t}return e.prototype.inited=function(){return!!this.streamingEngine},e.prototype.setup=function(e,t,n,r,o){this.mediaSource=t,this.videoTag=e,this.preload_=n||"metadata",this.network=new a.Network({withCredentials:r}),this.manifestRepresentation=new s.Manifest(this.network,this.videoTag,o),this.streamingEngine=new i.StreamingEngine(this.manifestRepresentation,this.network,this.videoTag,this.mediaSource,this.edition)},e.prototype.destroy=function(){this.videoTag&&this.videoTag.pause(),this.streamingEngine&&this.streamingEngine.destroy(),this.manifestRepresentation&&this.manifestRepresentation.destroy(),this.network&&this.network.destroy(),this.streamingEngine=void 0,this.manifestRepresentation=void 0,this.mediaSource=void 0,this.videoTag=void 0},e.prototype.play=function(){this.videoTag&&(this.videoTag.play(),this.streamingEngine.resumeOnTick())},e.prototype.pause=function(){this.videoTag&&(this.videoTag.pause(),this.manifestRepresentation.suspendManifestReloading(),this.streamingEngine.suspendOnTick())},e.prototype.seek=function(e){this.videoTag&&this.streamingEngine.seek(e)},e.prototype.getDuration=function(){if(!this.manifestRepresentation)return 0;var e=this.manifestRepresentation.currentManifestMetadata;return e?"VOD"===e.streamType?e.end:"DVR"===e.streamType?e.start-e.end:1/0:0},e.prototype.getExternalTime=function(e){if(!this.manifestRepresentation)return 0;var t=this.manifestRepresentation.currentManifestMetadata;return t?("DVR"===t.streamType&&(e-=t.end),e):0},e.prototype.getCurrentTime=function(){return this.getExternalTime(this.videoTag.currentTime)},e.prototype.getPtsOffset=function(){return this.streamingEngine.mediaDataModel.getPtsOffset()},e.prototype.getConfiguration=function(){var t=this.manifestRepresentation,n=this.streamingEngine,r=t.currentManifestMetadata;if(!r)return null;var i=n.mediaDataModel.getSegmentAtTime(this.videoTag.currentTime),a=n.adaptive.getQuality();if(i){var s=t.levels_[i.level];a=r.sortedLevels.indexOf(s)}var o=!1;return r.sortedLevels.length>1&&(a++,o=n.adaptive.adaptationEnabled()),{currentLevel:a,levels:e.parseLevels_(r.sortedLevels),enableAdaptation:o}},e.prototype.getAudioCodec=function(){return this.streamingEngine.audioCodec},e.parseLevels_=function(e){var t=u.map(e,function(e){var t=e.streamInfo.bandwidth||0,n={label:e.streamInfo.label,bitrate:t,height:1,width:1};return e.streamInfo.resolution&&e.streamInfo.resolution.height&&(n.height=e.streamInfo.resolution.height,n.width=e.streamInfo.resolution.width),n});return t.length>1&&t.unshift({label:"Auto",bitrate:1,height:1,width:1}),t},e.prototype.setCurrentQuality=function(e){this.streamingEngine.setCurrentQuality(e)},e.prototype.beginStream=function(){var e=this;this.readyToPlayPromise_.then(function(){e.streamingEngine.beginStream()})},e.prototype.initStream=function(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";this.streamingEngine.on("meta",function(e){this.trigger("meta",e)},this).on("bufferFull",function(e){this.trigger("bufferFull",e)},this).on("trackTypes",function(e){this.trigger("trackTypes",e)},this).on("cueAdded",function(e){this.trigger("cueAdded",e)},this).on("error",function(e){this.trigger("error",e)},this).on("levels",function(t){var n=e.parseLevels_(t.levels);this.trigger("levels",{levels:n,currentQuality:t.currentQuality})},this),this.readyToPlayPromise_=new Promise(function(e,t){n.streamReadyToStart_=e,n.streamStarted_=t})["catch"](function(){}),this.streamingEngine.setManifest(t,r).then(function(){var e="auto"===n.preload_;e?(n.streamingEngine.beginStream(),n.streamStarted_()):n.streamReadyToStart_()})["catch"](function(e){return n.trigger("error",e)})},e}()},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.LiveManifestLoader=t.RefreshRequest=void 0;var i,a=n(40),s=400,o=500,u=2,c=n(1);!function(e){e[e.IDLE=0]="IDLE",e[e.PENDING=1]="PENDING",e[e.COMPLETE=2]="COMPLETE"}(i||(i={}));var f=t.RefreshRequest=function l(){r(this,l),this.status=i.IDLE};t.LiveManifestLoader=function(){function e(t,n,i){r(this,e),this.refreshRequests=[],this.cancelRefreshRequest=function(e){e.cycles=0},this.manifest=t,this.network=n,this.video=i}return e.prototype.pollManifest=function(e){return this.refreshRequests[e]||(this.refreshRequests[e]=new f),this.reload_(e),this.refreshRequests[e].cycles=u,this.refreshRequests[e].promise},e.prototype.reload_=function(e){var t=this.refreshRequests[e];t&&t.status!==i.PENDING&&(t.promise=this.manifest.refreshManifest(e),t.status=i.PENDING,t.promise.then(this.checkManifestRefresh_.bind(this,e)))},e.prototype.checkManifestRefresh_=function(e){var t=this,n=this.refreshRequests[e];this.manifest&&n&&(n.cycles>0?this.getManifestLoadRunway_(e,this.video.currentTime).then(a.wait).then(function(){t.manifest&&n.cycles>0&&(n.cycles--,n.status=i.COMPLETE,t.reload_(e))}):n.status=i.IDLE)},e.prototype.getManifestLoadRunway_=function(e,t){var n=this;return this.manifest.doneLoading().then(function(){var r=3*n.network.maxLatency(),i=n.manifest.levels_[e],a=i.segments[i.segments.length-1],u=1e3*(a.end-t),c=1e3*n.manifest.levels_[e].streamInfo.targetDuration;if(c=Math.min(c,1e3*a.duration),c+s>u)return Math.max((u-r)/2,s);if(1===n.manifest.refreshesWithoutChanges)c/=2;else if(n.manifest.refreshesWithoutChanges>5)return o;return Math.max(c,s)})},e.prototype.cancelAllManifestReloads=function(){c.each(this.refreshRequests,this.cancelRefreshRequest)},e.prototype.destroy=function(){this.cancelAllManifestReloads(),this.video=null,this.refreshRequests=[],this.network=null,this.manifest=void 0},e}()},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.ManifestLoader=void 0;var a=n(7),s=r(a),o=n(39),u=n(3),c=n(1),f=n(5);t.ManifestLoader=function(){function e(t){i(this,e),this.totalManifest="",this.leftoverContent="",this.network=t,c.extend(this,u)}return e.prototype.load=function(e){var t={requester:"manifest",requestType:o.RequestType.Text,uris:e,retryParameters:{maxAttempts:2}},n=this.network.request(t).requestChannels,r=this;return n.then(function(e){var t=e.chan;return r.trigger("baseUrlFound",e.url),new Promise(function(e){f.go(s["default"].mark(function n(){var i;return s["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=3,f.take(t);case 3:if(i=n.sent,i!==f.CLOSED){n.next=10;break}return r.parseManifestChunk("",!0),e(),n.abrupt("break",13);case 10:r.parseManifestChunk(i,!1);case 11:n.next=0;break;case 13:case"end":return n.stop()}},n,this)}))})})},e.prototype.parseManifestChunk=function(e,t){var n=this.leftoverContent+e;if(this.totalManifest+=e,n=n.replace(/\r\n/g,"\n").replace(/\r/g,""),t&&n||n.indexOf("#EXT-X-ENDLIST")>=n.length-16)this.leftoverContent="",this.trigger("manifestChunk",n);else{var r=n.lastIndexOf("\n");this.leftoverContent=n.substring(r+1),this.trigger("manifestChunk",n.substring(0,r))}},e}()},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.ManifestParser=t.Playlist=void 0;var i=n(59),a=n(58),s=n(3),o=n(2),u=n(1),c=t.Playlist=function l(){r(this,l)},f=t.ManifestParser=function(){function e(t){r(this,e),this.KEY_VALUE=/^(?:,|, | )?([A-Z\-]+)=((?:"[^"]*")|(?:[^,]*))(.*)$/,this.EXTM3U={exp:/^#EXTM3U\s*$/,action:this.assignPlaylist.bind(this)},this.EXT_X_TARGETDURATION={exp:/^#EXT-X-TARGETDURATION:\s*(\d+(?:\.\d+)?)\s*$/,action:this.assignFloatStreamInfo.bind(this),prop:"targetDuration",checkType:this.setMediaPlaylist.bind(this)},this.EXT_X_MEDIA_SEQUENCE={exp:/^#EXT-X-MEDIA-SEQUENCE:\s*(\d+)\s*$/,action:this.assignMediaSequence.bind(this),checkType:this.setMediaPlaylist.bind(this)},this.EXT_X_VERSION={exp:/^#EXT-X-VERSION:\s*(\d+)\s*$/,action:this.assignInt.bind(this),prop:"version"},this.EXT_X_ALLOW_CACHE={exp:/^#EXT-X-ALLOW-CACHE:.*$/},this.EXT_X_KEY={exp:/^#EXT-X-KEY:(.*)$/,action:this.assignAesKey.bind(this)},this.EXT_X_STREAM_INF={exp:/^#EXT-X-STREAM-INF:(.*)$/,action:this.assignMediaPlaylist.bind(this),checkType:this.setMasterPlaylist.bind(this)},this.EXT_X_I_FRAME_STREAM_INF={exp:/^#EXT-X-I-FRAME-STREAM-INF:(.*)$/,action:this.assignIFrameMediaPlaylist.bind(this)},this.EXT_X_I_FRAMES_ONLY={exp:/^#EXT-X-I-FRAMES-ONLY\s*$/,action:this.setIFrameOnly.bind(this)},this.EXT_X_BYTERANGE={exp:/^#EXT-X-BYTERANGE:\s*(\d+(?:\.\d+)?)(?:@(\d+(?:\.\d+)?))?$/,action:this.setByteRange.bind(this)},this.EXTINF={exp:/^#EXTINF:\s*(-?\d+(?:\.\d+)?)(?:,(.*))?$/,action:this.createSegment.bind(this),checkType:this.setMediaPlaylist.bind(this)},this.EXT_X_MEDIA={exp:/^#EXT-X-MEDIA:(.*)$/,action:this.parseMedia.bind(this),checkType:this.setMasterPlaylist.bind(this)},this.EXT_X_DISCONTINUITY_SEQUENCE={exp:/^#EXT-X-DISCONTINUITY-SEQUENCE:\s*(\d+)\s*$/,action:this.setDiscontinuitySequence.bind(this),checkType:this.setMediaPlaylist.bind(this)},this.EXT_X_DISCONTINUITY={exp:/^#EXT-X-DISCONTINUITY\s*$/,action:this.incrementDiscontinuity.bind(this),checkType:this.setMediaPlaylist.bind(this)},this.EXT_X_ENDLIST={exp:/^#EXT-X-ENDLIST\s*$/,action:this.setEndlist.bind(this)},this.EXT_X_PLAYLIST_TYPE={exp:/^#EXT-X-PLAYLIST-TYPE:\s*(EVENT|VOD)\s*$/},this.EXT_X_INDEPENDENT_SEGMENTS={exp:/^#EXT-X-INDEPENDENT-SEGMENTS\s*$/},this.EXT_X_PROGRAM_DATE_TIME={exp:/^#EXT-X-PROGRAM-DATE-TIME:\s*([\d\-T:\.\+A-Z]+)\s*$/},this.DECIMAL_FLOATING_POINT={exp:/^(?:\+|-)?\d+(?:\.\d+)?$/},this.COMMENT={exp:/^#[^EXT](.*)$/},this.WHITESPACE={exp:/^\s*$/},this.URI={exp:/^([^#].+)$/,action:this.assignURL.bind(this)},this.props=[this.EXTM3U,this.EXT_X_VERSION,this.EXT_X_ALLOW_CACHE,this.EXTINF,this.EXT_X_BYTERANGE,this.EXT_X_DISCONTINUITY,this.EXT_X_KEY,this.EXT_X_PROGRAM_DATE_TIME,this.EXT_X_TARGETDURATION,this.EXT_X_MEDIA_SEQUENCE,this.EXT_X_DISCONTINUITY_SEQUENCE,this.EXT_X_ENDLIST,this.EXT_X_PLAYLIST_TYPE,this.EXT_X_I_FRAMES_ONLY,this.EXT_X_MEDIA,this.EXT_X_STREAM_INF,this.EXT_X_I_FRAME_STREAM_INF,this.EXT_X_INDEPENDENT_SEGMENTS,this.DECIMAL_FLOATING_POINT,this.URI,this.WHITESPACE,this.COMMENT],this.playlistObject=new c,this.manifestData="",this.baseURL="",this.discontinuityCount=0,u.extend(this,s),this.streamInfo=t||new i.StreamInfo}return e.prototype.assignPlaylist=function(t){u.extend(t,e.makePlaylistProperties())},e.prototype.assignFloatStreamInfo=function(e,t,n){e.streamInfo[t]=parseFloat(n[1])},e.prototype.assignInt=function(e,t,n){e[t]=parseInt(n[1])},e.prototype.assignURL=function(t,n,r){var i=r[0];if("media"===t.type){if(t.mediaElements.length>0){var a=t.mediaElements.length-1,s=t.mediaElements[a];if(s.url=e.getURI(i,this.baseURL),t.byteRangeLength){s.byteRange.length=t.byteRangeLength;var u=t.byteRangeStart;if(!u&&a>0){var c=t.mediaElements[a-1],f=c.byteRange.length,l=c.byteRange.byteRangeStart;u=l+f||0}s.byteRange.start=u}"AES-128"===t.key&&(s.aesKeyUri=o.getAbsolutePath(t.keyUri,this.baseURL),s.aesIV=this._getSegmentIV(t.aesIV,s.mediaSequenceId)),this.trigger("tsSegmentFound",s)}}else if("master"===t.type&&t.streamInfos.length>0){var d=t.streamInfos[t.streamInfos.length-1];d.urls.length||(d.urls.push(e.getURI(i,this.baseURL)),this.trigger("mediaPlaylistFound",d))}},e.prototype.assignMediaSequence=function(e,t,n){e.mediaSequence=e.currentMediaSequenceId=parseInt(n[1])},e.prototype.assignMediaPlaylist=function(t,n,r){var i=r[1];t.streamInfos.push(e.makeStreamInfo(i))},e.prototype.assignIFrameMediaPlaylist=function(t,n,r){var i=r[1];t.iframeStreamInfos.push(e.makeStreamInfo(i))},e.prototype.setIFrameOnly=function(e){e.iframeOnly=!0},e.prototype.assignAesKey=function(e,t,n){var r=this.parseAttributeValuePair(n[1]);e.key=r.method,e.keyUri=r.uri,e.aesIV=r.iv},e.prototype.setMediaPlaylist=function(t){return t.type||(u.extend(t,e.makeMediaPlaylistProperties()),t.streamInfo=this.streamInfo),"media"===t.type},e.prototype.setMasterPlaylist=function(t){return t.type||u.extend(t,e.makeMasterPlaylistProperties()),"master"===t.type},e.prototype.createIVArrayBuffer_=function(e){var t="00000000000000000000000000000000";return e=t.substr(e.length)+e,this.hexStringToArrayBuffer_(e)},e.prototype.hexStringToArrayBuffer_=function(e){var t=void 0,n=new Uint8Array(16);for(t=0;t<e.length;t+=2)n[t/2]=parseInt(e.substr(t,2),16);return n.buffer},e.prototype._getSegmentIV=function(e,t){if(e){var n=e.substring(0,2);return"0x"===n||"0X"===n?this.createIVArrayBuffer_(e.substring(2)):this.createIVArrayBuffer_(e)}return this.createIVArrayBuffer_(t.toString(16));
},e.prototype.parseMedia=function(t,n,r){var i=e.parseKeyValuesPairs(r[1]),s=new a.MediaData(i);if(s.type&&s.groupId){var o=s.type.toLowerCase(),u=s.groupId,c=t.renditions[o],f=void 0;t.renditions[o]=c?c:{},f=t.renditions[o][u],t.renditions[o][u]=f?f:[],t.renditions[o][u].push(s)}},e.prototype.setByteRange=function(e,t,n){e.byteRangeLength=parseInt(n[1],10),e.byteRangeStart=parseInt(n[2],10)},e.prototype.createSegment=function(e,t,n){var r=new i.Segment,a=parseFloat(n[1]),s=n[2];if(r.title=s?s:r.title,r.originalData.start=r.start=this.streamInfo.end,r.originalData.end=r.end=this.streamInfo.end+a,r.originalData.duration=r.duration=a||this.streamInfo.targetDuration,r.discontinuity=this.discontinuityCount,!(e.mediaSequence>=0))throw"expected a media sequence value before a segment, but did not find one.";r.mediaSequenceId=e.currentMediaSequenceId,e.currentMediaSequenceId++,r.title&&!/^(?:\{\}|no desc)$/.test(r.title.trim())&&(r.extInfTitle=r.title),e.streamInfo.end+=r.duration,e.mediaElements.push(r)},e.prototype.setDiscontinuitySequence=function(e,t,n){this.discontinuityCount=e.discontinuitySequence=parseFloat(n[1])},e.prototype.incrementDiscontinuity=function(e){e.containsDiscontinuities=!0,this.discontinuityCount++},e.prototype.setEndlist=function(e){"master"!==e.type&&(e.mediaElements[e.mediaElements.length-1].isLast=!0,this.streamInfo.hasEndlist=!0)},e.prototype.parseAttributeValuePair=function(e){for(var t={},n=e;;){var r=n.match(this.KEY_VALUE);if(null==r)break;var i=r[1].toLowerCase(),a="";a=0===r[2].indexOf('"')&&r[2].lastIndexOf('"')===r[2].length-1?r[2].slice(1,r[2].length-1):r[2],t[i]=a,n=r[3]}return t},e.makePlaylistProperties=function(){return{version:1,independentSegments:!1,start:{timeOffset:0,precise:!1}}},e.makeMasterPlaylistProperties=function(){return{type:"master",media:[],streamInfos:[],iframeStreamInfos:[],renditions:{}}},e.makeMediaPlaylistProperties=function(){return{type:"media",streamInfo:null,mediaSequence:0,currentMediaSequenceId:0,playlistType:null,discontinuitySequence:null,containsDiscontinuities:!1,iFramesOnly:!1,mediaElements:[]}},e.makeStreamInfo=function(t){var n=e.parseKeyValuesPairs(t),r=new i.StreamInfo,a=n.url||n.uri;if(a&&r.urls.push(a),r.bandwidth=parseFloat(n.bandwidth),n.codecs&&(r.codecs=n.codecs.split(/[, ] */g)),n.resolution){var s=n.resolution.split("x");r.resolution={width:parseInt(s[0]),height:parseInt(s[1])}}return r.trackTypes=e.getLevelTypes(r),r.video=n.video,r.audio=n.audio,r.programId=parseInt(n.programid),r.label=n.name||e.makeLabel(r),r},e.makeLabel=function(e){return e.resolution&&e.resolution&&e.resolution.height?e.resolution.height+"p":Math.floor(e.bandwidth/1e3)+" kbps"},e.parseKeyValuesPairs=function(t){for(var n={},r=void 0,i=void 0,a=void 0,s=t;;){if(r=e.KEY_VALUE.exec(s),null===r)break;i=r[1].replace("-","").toLowerCase(),a="",a=0===r[2].indexOf('"')&&r[2].lastIndexOf('"')===r[2].length-1?r[2].slice(1,r[2].length-1):r[2],n[i]=a,s=r[3]}return n},e.getLevelTypes=function(e){var t=e.resolution.width*e.resolution.height,n=e.codecs,r=!1,i=u.compact(u.map(n,function(e){return e.indexOf("avc1")>=0?(r=!0,{type:"video",codec:"default",codecString:e}):e.indexOf("mp4a.40.34")>=0?{type:"audio",codec:"mp3",codecString:e}:e.indexOf("mp4a")>=0?{type:"audio",codec:"aac",codecString:e}:void 0}));return t&&!r&&i.push({type:"video",codec:"default",codecString:"avc1.4d4015"}),i},e.getURI=function(e,t){var n=/^(https?:\/\/.*)$/i,r=/^(\/.*)$/,i=/^(.+)$/;if(n.test(e))return e;if(r.test(e)){var a=new URL(t);return a.protocol+"//"+a.host+e}return i.test(e)?t+e:e},e.prototype.parse=function(e){var t=this,n=e.split("\n");this.manifestData+=e,n.forEach(function(e){var n=void 0,r=!1;t.props.forEach(function(i){n=i.exp.exec(e),n&&(i.checkType&&!i.checkType(t.playlistObject)||(i.action&&i.action(t.playlistObject,i.prop,n),r=!0))})})},e.prototype.getPlaylist=function(){return this.playlistObject},e.prototype.setBaseUrl=function(e){var t=e.split(/[?#]/)[0];this.baseURL=t.substring(0,t.lastIndexOf("/")+1)},e}();f.KEY_VALUE=/^(?:,|, | )?([A-Z\-]+)=((?:"[^"]*")|(?:[^,]*))(.*)$/},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.MDM=void 0;var a=n(7),s=r(a),o=n(39),u=n(40),c=n(107),f=n(1),l=n(3),d=n(5),h=4,p=8;t.MDM=function(){function e(t,n){var r=this;i(this,e),this.loadedAesKeys_={},this.sessionHistory_=[],this.segmentPtsOffset_=-1,this.offsetTime=-1,this.network=n,this.manifestRepresentation=t,f.extend(this,l),this.streamTypesKnown_=new Promise(function(e,t){r.streamTypesKnownResolve_=e,r.streamTypesKnownReject_=t})}return e.prototype.getTrackTypes=function(){return this.streamTypesKnown_},e.prototype.removeTmsInRange_=function(e,t,n){f.each(this.sessionHistory_,function(r){(n!==!1||!r.active&&r.transmuxer.isBoxedChannelClosed())&&(r.tmsArray=f.filter(r.tmsArray,function(n){return!!n.ts.errorInSegment||!(n.ts.start<t&&n.ts.end>e)}))}),this.sessionHistory_=f.filter(this.sessionHistory_,function(e){return!!e.tmsArray.length})},e.prototype.removeInactiveTmsInRange=function(e,t){return this.removeTmsInRange_(e,t,!1)},e.prototype.removeAllTmsInRange=function(e,t){return this.removeTmsInRange_(e,t,!0)},e.prototype.getSegmentAtTime=function(e){for(var t=this.sessionHistory_.length;t--;){var n=f.find(this.sessionHistory_[t].tmsArray,function(t){return t.ts.start<=e&&t.ts.end>=e});if(n)return n.ts}},e.prototype.getSegmentByFileId=function(e){for(var t=e.split("_"),n=parseInt(t[0]),r=parseInt(t[1]),i=this.sessionHistory_.length;i--;){var a=f.find(this.sessionHistory_[i].tmsArray,function(e){return e.ts.level===n&&e.ts.mediaSequenceId===r});if(a)return a.ts}return null},e.prototype.getTransmuxSegment=function(e){return this.currentTransmuxSession&&!this.currentTransmuxSession.closed?this.currentTransmuxSession.getTransmuxSegment(e):null},e.prototype.get=function(e){var t=this,n=this.getTransmuxSegment(e);return n||(n=new c.TransmuxSegment(e),this.transmux_(n),this.beginDownloadingSegment(n)),e.aesKeyUri?this.loadedAesKeys_[e.aesKeyUri].then(function(){return Promise.resolve(t.currentTransmuxSession)})["catch"](function(e){return Promise.reject("Cannot download AES key: "+e)}):Promise.resolve(this.currentTransmuxSession)},e.prototype.getPtsOffset=function(){if(this.currentTransmuxSession){var e=this.currentTransmuxSession.tmsArray[0];e&&e.ts.pts&&(this.segmentPtsOffset_=e.ts.pts/9e4-e.ts.start)}return this.segmentPtsOffset_},e.prototype.complete=function(e){e.transmuxStatus="complete"},e.prototype.endTransmuxSession=function(e,t){if(e&&e.tmsArray&&e.tmsArray.length)return e.tmsArray[0].ts.level<=t?void e.closeTransmuxerWhenFinished():void e.cancelTransmuxSession()},e.prototype.transmux_=function(e){var t=this,n=e.ts;if(this.isSegmentContiguous_(e))this.currentTransmuxSession.tmsArray.push(e);else{this.endTransmuxSession(this.currentTransmuxSession,e.ts.level),this.currentTransmuxSession=new c.TransmuxSession,this.currentTransmuxSession.network=this.network,this.currentTransmuxSession.tmsArray.push(e),this.segmentPtsOffset_=-1,this.sessionHistory_.push(this.currentTransmuxSession);var r=["boxed","metadata","captions"],i=f.map(r,function(e){return this.spawnTransmuxTaker_(this.currentTransmuxSession,e).offsetsKnown},this);f.pluck(i,"offsetsKnown");this.currentTransmuxSession.offsetsKnown=Promise.all(i).then(function(e){return f.indexBy(e,"type")},function(){})}n.aesKeyUri&&(this.loadedAesKeys_[n.aesKeyUri]||(this.loadedAesKeys_[n.aesKeyUri]=this.loadAesKey_(n).then(function(e){return(0,u.channelToPromise)(e.chan)}))),this.currentTransmuxSession.transmuxer.trackTypesKnown().then(function(e){0!==e.length&&t.streamTypesKnownResolve_(e)})},e.prototype.spawnTransmuxTaker_=function(e,t){var n=e.transmuxer[t],r=e.mediaChannels[t],i=void 0,a=void 0,o=new Promise(function(e,t){i=e,a=t});return d.go(s["default"].mark(function u(){var e,o,c,l,m,v,y;return s["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:e=f.now()+h,"metadata"!==t&&"captions"!==t||i({type:t}),o=void 0;case 3:return s.t0=d.CLOSED,s.next=6,d.take(n);case 6:if(s.t1=o=s.sent,s.t0===s.t1){s.next=24;break}if("duration-update"!==o.type){s.next=11;break}return o.sourceFile&&(c=o.sourceFile.split("_"),l=c[0],m=c[1],this.manifestRepresentation.updateTS(l,m,{type:t,duration:o.duration,start:o.start})),s.abrupt("continue",3);case 11:return"metadata"!==o.type||f.isNumber(o.start)||f.isNumber(o.pts)||(v=this.manifestRepresentation.getSegmentFromIndex(o.sourceFile),o.start=v.start),s.next=14,d.put(r,o);case 14:if("error"!==o.type){s.next=16;break}return s.abrupt("return",a(o.message));case 16:if("init"===o.type&&i({type:t,start:o.start}),y=f.now(),!(y>e)){s.next=22;break}return e=y+h,s.next=22,d.timeout(p);case 22:s.next=3;break;case 24:i({type:t}),r.close();case 26:case"end":return s.stop()}},u,this)}).bind(this)),{offsetsKnown:o}},e.prototype.downloadComplete_=function(e,t){return e.size=t.size,this.manifestRepresentation.updateLevel(e),t},e.prototype.loadAesKey_=function(e){var t={requestType:o.RequestType.ByteArray,requester:"aes-key",uris:[e.aesKeyUri],retryParameters:{maxAttempts:2}};return this.network.request(t).requestChannels},e.prototype.isSegmentContiguous_=function(e){if(!e||e.ts.errorInSegment||!this.currentTransmuxSession||this.currentTransmuxSession.transmuxer.isBoxedChannelClosed()||!this.currentTransmuxSession.tmsArray.length)return!1;var t=this.currentTransmuxSession.tmsArray,n=t[t.length-1];return e.ts.discontinuity===n.ts.discontinuity&&(e.ts.level===n.ts.level&&e.ts.mediaSequenceId===n.ts.mediaSequenceId+1&&!n.ts.errorInSegment)},e.prototype.beginDownloadingSegment=function(e){var t=this,n=e.ts;e.transmuxStatus="downloading";var r={requestType:o.RequestType.ByteArray,requester:"media-file",uris:[n.url],retryParameters:{maxAttempts:2}},i=this.network.request(r),a=i.requestChannels;this.currentTransmuxSession.enqueueNetworkData({segment:e.ts,requestId:i.requestId,netRequestPromise:a,aesKeyPromise:this.loadedAesKeys_[e.ts.aesKeyUri]}),a.then(function(e){(0,u.channelToPromise)(e.metaChan).then(t.downloadComplete_.bind(t,n))})["catch"](function(){n.errorInSegment=!0,t.trigger("segment-download-error",n)})},e}()},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t,n,r){return new Promise(function(i){B.go(o["default"].mark(function a(){var s,u;return o["default"].wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t.closed){a.next=17;break}return a.next=3,B.take(e);case 3:if(s=a.sent,s!==B.CLOSED){a.next=10;break}return a.next=7,B.put(t,{type:"info",data:{endOfFile:!0,sourceFile:n}});case 7:return r&&t.close(),i(),a.abrupt("break",17);case 10:return u={sourceFile:n,payload:s},a.next=13,B.put(t,u);case 13:if(a.sent){a.next=15;break}return a.abrupt("break",17);case 15:a.next=0;break;case 17:case"end":return a.stop()}},a,this)}))})}t.__esModule=!0;var s=n(7),o=r(s),u=n(124),c=r(u),f=n(126),l=r(f),d=n(62),h=r(d),p=n(121),m=r(p),v=n(123),y=r(v),g=n(122),_=r(g),b=n(120),T=r(b),x=n(118),k=r(x),w=n(128),S=r(w),E=n(115),I=r(E),A=n(125),M=r(A),C=n(117),L=r(C),P=n(114),D=r(P),O=n(129),R=(n(26),n(1)),B=n(5),U=function(){function e(t){var n=this;i(this,e),this.id=t,this.trackTypesKnownPromise=new Promise(function(e){n.trackTypesKnownResolve=e}),this.trackTypesKnownPromise.then(function(e){return 0===e.length&&(o.close(),s.close(),f.close(),v.close()),e}),this.inputToMpegTSChannel=B.chan(1e4);var r=B.chan(),a=B.chan(),s=B.chan(),o=B.chan(),u=B.chan(),f=B.chan(),d=B.chan(),p=B.chan(),v=B.chan(),g=B.chan(),b=B.chan(),x=B.chan(),w=B.chan(),E=B.chan(),A=B.chan(),C=B.chan(),L=B.chan();this.boxed=B.chan(),this.captions=B.chan(),this.metadata=B.chan(),this.allChannels_=[this.boxed,this.captions,this.metadata,L,C,A,d,E,x,w,b,g,v,p,d,f,u,o,s,a,r,this.inputToMpegTSChannel],B.go((0,l["default"])(r,a)),B.go((0,m["default"])(f,v,p,d));var P=B.operations.merge([v,s]);B.go((0,h["default"])(P,w)),B.go((0,k["default"])(E,A)),B.go((0,y["default"])(d,L)),B.go((0,_["default"])(u,p,this.metadata)),B.go((0,M["default"])(o,g,b,this.metadata)),B.go((0,T["default"])(g,x)),B.go((0,S["default"])(x,w,C,E)),B.go((0,I["default"])(C,A,L)),B.go((0,D["default"])(b,this.captions)),B.go((0,O.PesPacketSplitter)(a,s,o,u)),B.go((0,c["default"])(this.inputToMpegTSChannel,r,a,f)),B.go(this.processOutputState(L,this.trackTypesKnownResolve,this.boxed))}return e.prototype.nextFile=function(e,t){if(!this.inputToMpegTSChannel.closed){if(t.aesKeyPromise){var n=B.chan(1e4);return B.go((0,L["default"])(e,n,this.inputToMpegTSChannel,t.id,t.aesIV,t.aesKeyPromise)),a(n,this.inputToMpegTSChannel,t.id,t.isLast)}return a(e,this.inputToMpegTSChannel,t.id,t.isLast)}},e.prototype.processOutputState=function(e,t,n){return o["default"].mark(function r(){var i;return o["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:i=void 0;case 1:return r.t0=B.CLOSED,r.next=4,B.take(e);case 4:if(r.t1=i=r.sent,r.t0===r.t1){r.next=13;break}if("info"!==i.type){r.next=9;break}return i.data.trackTypes&&t(i.data.trackTypes),r.abrupt("continue",1);case 9:return r.next=11,B.put(n,i);case 11:r.next=1;break;case 13:n.close();case 14:case"end":return r.stop()}},r,this)})},e.prototype.closeAllChannels=function(){R.each(this.allChannels_,function(e){e.close()})},e.prototype.endOfInput=function(){this.inputToMpegTSChannel.close()},e.prototype.isBoxedChannelClosed=function(){return this.boxed.closed},e.prototype.trackTypesKnown=function(){return this.trackTypesKnownPromise},e}();t["default"]=U},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.TransmuxSession=t.TransmuxSegment=void 0;var a=n(106),s=r(a),o=n(1),u=n(5);t.TransmuxSegment=function c(e){i(this,c),this.ts=e,this.transmuxStatus="in-progress"},t.TransmuxSession=function(){function e(){i(this,e),this.dataQueue_=[],this.isSendingData=!1,this.forwardBufferCleared=!1,this.closeWhenFinished=!1,this.closed=!1,this.active=!0,this.isBuffered=!1;var t=o.uniqueId("transmuxer-");this.tmsArray=[],this.transmuxer=new s["default"](t),this.mediaChannels={boxed:new u.chan(1e4),metadata:new u.chan(1e4),captions:new u.chan(1e4)}}return e.prototype.getTransmuxSegment=function(e){return o.find(this.tmsArray,function(t){return e===t.ts})},e.prototype.closeTransmuxerWhenFinished=function(){this.closed||(!this.transmuxer||this.isSendingData||this.dataQueue_.length?this.closeWhenFinished=!0:(this.transmuxer.endOfInput(),this.closeWhenFinished=!1)),this.active=!1},e.prototype.cancelTransmuxSession=function(){var e=this;this.transmuxer.closeAllChannels(),this.currentDownload&&(this.network.cancelRequest(this.currentDownload.requestId),this.currentDownload=void 0),o.each(this.dataQueue_,function(t){e.network.cancelRequest(t.requestId)}),this.destroy()},e.prototype.destroy=function(){var e=this;this.transmuxer.isBoxedChannelClosed()||this.cancelTransmuxSession(),o.each(this.dataQueue_,function(t){e.tmsArray=o.without(e.tmsArray,t.segment)}),this.active=!1,this.closeWhenFinished=!1,this.closed=!0,this.dataQueue_=null},e.prototype.enqueueNetworkData=function(e){this.dataQueue_.push(e),this.runQueue_()},e.prototype.runQueue_=function(){var e=this;if(!this.dataQueue_)return void this.destroy();if(!this.isSendingData&&this.dataQueue_.length){this.isSendingData=!0;var t=this.dataQueue_.shift();if(this.currentDownload)throw new Error("transmux-session handling multiple EnquableDownloads");this.currentDownload=t,t.netRequestPromise.then(function(n){e.dataQueue_||e.destroy();var r=t.segment,i=r.level+"_"+r.mediaSequenceId,a={id:i,isLast:r.isLast,aesIV:r.aesIV,aesKeyPromise:t.aesKeyPromise};e.transmuxer.nextFile(n.chan,a).then(function(){e.currentDownload=void 0,e.isSendingData=!1,e.closeWhenFinished&&e.closeTransmuxerWhenFinished()}).then(e.runQueue_.bind(e))})["catch"](function(){e.currentDownload=void 0,e.isSendingData=!1,e.closeTransmuxerWhenFinished()})}},e}()},function(e,t){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var r=function(){function e(t){n(this,e),this.aesIV=t}return e.prototype.decrypt=function(e,t){return window.crypto.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},t,e)},e}();t["default"]=r},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var i=function(){function e(t){r(this,e);var n=this.uint8ArrayToUint32Array_(t.buffer),i=this.keySize=n.length,a=i+6;this.ksRows=4*(a+1),this.keyWords=n,this.subMix=[],this.invSubMix=[],this.initTable(),this.expandKey()}return e.prototype.uint8ArrayToUint32Array_=function(e){for(var t=new DataView(e),n=new Uint32Array(4),r=0;r<n.length;r++)n[r]=t.getUint32(4*r);return n},e.prototype.initTable=function(){var e=this.sBox=new Uint32Array(256),t=this.invSBox=new Uint32Array(256),n=this.subMix[0]=new Uint32Array(256),r=this.subMix[1]=new Uint32Array(256),i=this.subMix[2]=new Uint32Array(256),a=this.subMix[3]=new Uint32Array(256),s=this.invSubMix[0]=new Uint32Array(256),o=this.invSubMix[1]=new Uint32Array(256),u=this.invSubMix[2]=new Uint32Array(256),c=this.invSubMix[3]=new Uint32Array(256);this.rcon=[0,1,2,4,8,16,32,64,128,27,54];for(var f=new Uint32Array(256),l=0,d=0,h=0;h<256;h++)h<128?f[h]=h<<1:f[h]=h<<1^283;for(var h=0;h<256;h++){var p=d^d<<1^d<<2^d<<3^d<<4;p=p>>>8^255&p^99,e[l]=p,t[p]=l;var m=f[l],v=f[m],y=f[v],g=257*f[p]^16843008*p;n[l]=g<<24|g>>>8,r[l]=g<<16|g>>>16,i[l]=g<<8|g>>>24,a[l]=g,g=16843009*y^65537*v^257*m^16843008*l,s[p]=g<<24|g>>>8,o[p]=g<<16|g>>>16,u[p]=g<<8|g>>>24,c[p]=g,l?(l=m^f[f[f[y^m]]],d^=f[f[d]]):l=d=1}},e.prototype.expandKey=function(){for(var e,t=this.keySchedule=new Uint32Array(this.ksRows).fill(0),n=this.rcon,r=this.invKeySchedule=new Uint32Array(this.ksRows).fill(0),i=this.keySize,a=this.keyWords,s=this.ksRows,o=this.sBox,u=this.invSubMix[0],c=this.invSubMix[1],f=this.invSubMix[2],l=this.invSubMix[3],d=0;d<s;d++)if(d<i)e=t[d]=a[d];else{var h=e;d%i?i>6&&d%i===4&&(h=o[h>>>24]<<24|o[h>>>16&255]<<16|o[h>>>8&255]<<8|o[255&h]):(h=h<<8|h>>>24,h=o[h>>>24]<<24|o[h>>>16&255]<<16|o[h>>>8&255]<<8|o[255&h],h^=n[d/i|0]<<24),t[d]=e=(t[d-i]^h)>>>0}for(var p=0;p<s;p++)d=s-p,h=3&p?t[d]:t[d-4],p<4||d<=4?r[p]=h:r[p]=u[o[h>>>24]]^c[o[h>>>16&255]]^f[o[h>>>8&255]]^l[o[255&h]],r[p]=r[p]>>>0},e.prototype.decrypt=function(e,t,n){for(var r=this.invKeySchedule,i=r[0],a=r[1],s=r[2],o=r[3],u=this.keySize+6,c=this.invSubMix[0],f=this.invSubMix[1],l=this.invSubMix[2],d=this.invSubMix[3],h=this.invSBox,p=new Uint8Array(e.length),m=this.uint8ArrayToUint32Array_(n),v=m[0],y=m[1],g=m[2],_=m[3],b=new DataView(e.buffer);t<e.length;){var T=b.getUint32(t),x=b.getUint32(t+4),k=b.getUint32(t+8),w=b.getUint32(t+12),S=new Uint32Array(4),E=new Uint32Array(4),I=new Uint32Array(4);S[0]=T^i,S[1]=w^a,S[2]=k^s,S[3]=x^o;for(var A=4,M=1;M<u;M++)E[0]=c[S[0]>>>24]^f[S[1]>>>16&255]^l[S[2]>>>8&255]^d[255&S[3]]^r[A++],E[1]=c[S[1]>>>24]^f[S[2]>>>16&255]^l[S[3]>>>8&255]^d[255&S[0]]^r[A++],E[2]=c[S[2]>>>24]^f[S[3]>>>16&255]^l[S[0]>>>8&255]^d[255&S[1]]^r[A++],E[3]=c[S[3]>>>24]^f[S[0]>>>16&255]^l[S[1]>>>8&255]^d[255&S[2]]^r[A++],S[0]=E[0],S[1]=E[1],S[2]=E[2],S[3]=E[3];E[0]=(h[S[0]>>>24]<<24|h[S[1]>>>16&255]<<16|h[S[2]>>>8&255]<<8|h[255&S[3]])^r[A++],E[1]=(h[S[1]>>>24]<<24|h[S[2]>>>16&255]<<16|h[S[3]>>>8&255]<<8|h[255&S[0]])^r[A++],E[2]=(h[S[2]>>>24]<<24|h[S[3]>>>16&255]<<16|h[S[0]>>>8&255]<<8|h[255&S[1]])^r[A++],E[3]=(h[S[3]>>>24]<<24|h[S[0]>>>16&255]<<16|h[S[1]>>>8&255]<<8|h[255&S[2]])^r[A],I[3]=E[0]^v,I[2]=E[3]^y,I[1]=E[2]^g,I[0]=E[1]^_;for(var C=new DataView(I.buffer),M=0;M<16;M++)p[M+t]=C.getUint8(15-M);v=T,y=x,g=k,_=w,t+=16}return this.unpad_(p)},e.prototype.unpad_=function(e){var t=e.length,n=e[t-1];return e.subarray(0,e.length-n)},e}();t["default"]=i},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var a=n(108),s=r(a),o=n(113),u=r(o),c=n(109),f=r(c),l=function(){function e(t,n){i(this,e),this.key=t,this.iv=n,this.fastAesKey=new u["default"](t)}return e.prototype.supportsWebCrypto_=function(){return window.crypto&&"https:"===window.location.protocol},e.prototype.decrypt=function(e){var t=this;if(this.supportsWebCrypto_())return this.fastAesKey.expandKey().then(function(n){var r=new s["default"](t.iv);return r.decrypt(e,n)});var n=new f["default"](this.key);return Promise.resolve(n.decrypt(e,0,this.iv))},e}();t["default"]=l},function(e,t){"use strict";t.__esModule=!0;var n=function(e){var t=e.byteLength,n=0,r=0;this.length=function(){return 8*t},this.bitsAvailable=function(){return 8*t+r},this.loadWord=function(){var i=e.byteLength-t,a=new Uint8Array(4),s=Math.min(4,t);if(0===s)throw new Error("no bytes available");a.set(e.subarray(i,i+s)),n=new DataView(a.buffer).getUint32(0),r=8*s,t-=s},this.skipBits=function(e){var i;r>e?(n<<=e,r-=e):(e-=r,i=Math.floor(e/8),e-=8*i,t-=i,this.loadWord(),n<<=e,r-=e)},this.readBits=function(e){var i=Math.min(r,e),a=n>>>32-i;return console.assert(e<=32,"Cannot read more than 32 bits at a time"),r-=i,r>0?n<<=i:t>0&&this.loadWord(),i=e-i,i>0?a<<i|this.readBits(i):a},this.skipLeadingZeros=function(){var e;for(e=0;e<r;++e)if(0!==(n&2147483648>>>e))return n<<=e,r-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()};t["default"]=n},function(e,t){"use strict";t.__esModule=!0;t.SpecialASCII={42:"á",92:"é",94:"í",95:"ó",96:"ú",123:"ç",124:"÷",125:"Ñ",126:"ñ",127:"♫"},t.US={48:"®",49:"°",50:"½",51:"¿",52:"™",53:"¢",54:"£",55:"♪",56:"à",57:" ",58:"è",59:"â",60:"ê",61:"î",62:"ô",63:"û"}},function(e,t){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var r=function(){function e(t){n(this,e),this.key=t}return e.prototype.expandKey=function(){return window.crypto.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},e}();t["default"]=r},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){return d["default"].mark(function n(){var r,i;return d["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r=void 0,i=new T;case 2:return n.t0=p.CLOSED,n.next=5,p.take(e);case 5:if(n.t1=r=n.sent,n.t0===n.t1){n.next=10;break}return n.delegateYield(o(t,r,i),"t2",8);case 8:n.next=2;break;case 10:t.close();case 11:case"end":return n.stop()}},n,this)})}function o(e,t,n){var r,i,a,s,o,l;return d["default"].wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(r=new x,r.payload=t.payload,i=r.payload.length,a=new _.CaptionsParser,!(r.position<i)){d.next=14;break}for(r.position++;r.position<i-10;)s=c(r),o=c(r),s===g["default"].SEI_PAYLOAD_ITU_T_35&&f(r)&&(o-=8,r.position+=8,o-=a.getCCDataPackets(r,n,t.pts)),u(r,o);a.parseCaptionsData(n,t.dts);case 8:if(!n.captions.length){d.next=14;break}return l=n.captions.shift(),d.next=12,p.put(e,{payload:l});case 12:d.next=8;break;case 14:case"end":return d.stop()}},b[0],this)}function u(e,t){for(var n=e.payload,r=e.payload.length,i=e.position,a=0;a<t;a++)i+3<r&&0===n[i]&&0===n[i+1]&&3===n[i+2]&&(i+=2,a++),i<r&&i++;e.position=i}function c(e){for(var t=void 0,n=0,r=e.payload.length;e.position<r&&(t=e.payload[e.position++],n+=t,255===t););return n}function f(e){var t=e.position,n=e.payload[t],r=v.readUint16(e.payload,t+1),i=v.readUint32(e.payload,t+3),a=e.payload[t+7];return n===g["default"].ITU_T_T35_COUNTRY_CODE&&r===g["default"].ITU_T_T35_PROVIDER_CODE&&i===g["default"].USER_IDENTIFIER_ATSC1_DATA&&a===g["default"].USER_DATA_TYPE_CC_DATA}t.__esModule=!0;var l=n(7),d=i(l);t["default"]=s;var h=n(5),p=r(h),m=n(12),v=r(m),y=n(63),g=i(y),_=n(116),b=[o].map(d["default"].mark),T=function k(){a(this,k),this.captions=[],this.sorted=[],this.buffer=["",""],this.bufferIndex=0,this.captionStart=NaN,this.captionEnd=NaN,this.lastCommand=0,this.mode=0,this.line=[NaN,NaN],this.position=[NaN,NaN],this.backgroundColor="black",this.textColor="white"},x=function w(){a(this,w),this.position=0}},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n,r){return c["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,y.put(e,{type:"init",start:t.minSegmentPts,length:0,payload:l["default"].initSegment(n),sourceFile:r});case 2:case"end":return i.stop()}},v[0],this)}function s(e,t,n,r,i){var a;return c["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return a={type:"mediaSegment",start:t.minSegmentPts,end:t.maxSegmentPts,length:(t.maxSegmentPts-t.minSegmentPts)/9e4,payload:n,sourceFile:i},r!==1/0&&(a.appendWindowEnd=r),s.next=4,y.put(e,a);case 4:case"end":return s.stop()}},v[1],this)}function o(e,t,n){return c["default"].mark(function r(){var i,o,u,f,l,d,p,v,g;return c["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:i={},o=void 0,u=-1,f=!1,l=void 0,d=c["default"].mark(function _(e){var t,r,d,p,v,g;return c["default"].wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(m.size(i)){c.next=2;break}return c.abrupt("return");case 2:t=i.video&&i.video.length?"video":"audio",r=m.min(i,function(e){return e.length}).length;case 4:if(!(r>0)){c.next=23;break}if(d=i[t][0],u<0&&(u=d.minSegmentPts),p=m.map(i,function(e){return e.shift()}),v=h.combineUint8Arrays(m.reduce(p,function(e,t){return e.concat(t.moof,t.mdat)},[])),o&&o.sourceFile===d.sourceFile){c.next=14;break}if(!o){c.next=13;break}return c.next=13,y.put(n,o);case 13:o={type:"duration-update",sourceFile:d.sourceFile,start:(d.minSegmentPts-u)/9e4,duration:0};case 14:if(g=d.maxSegmentPts+m.last(d.samples).duration,o.duration=(g-u)/9e4-o.start,f){c.next=19;break}return c.delegateYield(a(n,d,p,d.sourceFile),"t0",18);case 18:f=!0;case 19:return c.delegateYield(s(n,d,v,e?g:1/0,d.sourceFile),"t1",20);case 20:r--,c.next=4;break;case 23:if(!e||!o||o.sourceFile!==l){c.next=28;break}return c.next=26,y.put(n,o);case 26:return o=void 0,c.abrupt("return");case 28:case"end":return c.stop()}},_,this)}),p=void 0,v=y.operations.merge([e,t]);case 8:return r.t0=y.CLOSED,r.next=11,y.take(v);case 11:if(r.t1=p=r.sent,r.t0===r.t1){r.next=28;break}if("error"!==p.type){r.next=17;break}return r.next=16,y.put(n,p);case 16:return r.abrupt("continue",8);case 17:if("info"!==p.type){r.next=23;break}return p.data.trackTypes&&0===m.size(i)&&m.each(p.data.trackTypes,function(e){i[e.type]=[]}),p.data.endOfFile&&(l=p.data.sourceFile),r.next=22,y.put(n,p);case 22:return r.abrupt("continue",8);case 23:return g=p,i[g.track.type].push(g.track),r.delegateYield(d(!1),"t2",26);case 26:r.next=8;break;case 28:if(!n.closed){r.next=30;break}return r.abrupt("return");case 30:return r.delegateYield(d(!0),"t3",31);case 31:n.close();case 32:case"end":return r.stop()}},r,this)})}t.__esModule=!0;var u=n(7),c=i(u);t["default"]=o;var f=n(37),l=i(f),d=n(12),h=r(d),p=n(87),m=r(p),v=[a,s].map(c["default"].mark),y=n(5)},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0,t.CaptionsParser=t.CCPacket=t.CaptionStyle=t.CaptionPosition=t.Caption=void 0;var s=n(112),o=i(s),u=n(63),c=r(u),f=t.Caption=function N(){a(this,N),this.type="caption"},l=t.CaptionPosition=function F(){a(this,F)},d=t.CaptionStyle=function q(){a(this,q),this.textColor="white",this.backgroundColor="black"},h=t.CCPacket=function X(){a(this,X)},p=5152,m=5153,v=5154,y=5155,g=5156,_=5157,b=5158,T=5159,x=5160,k=5161,w=5162,S=5163,E=5164,I=5165,A=5166,M=5167,C=5921,L=5922,P=5923,D=["white","green","blue","cyan","red","yellow","magenta","italics",0,4,8,12,16,20,24,28],O=0,R=1,B=[11,1,3,12,14,5,7,9],U=["white","green","blue","cyan","red","yellow","magenta","black"];t.CaptionsParser=function(){function e(){a(this,e)}return e.prototype.parseCaptionsData=function(e,t){for(var n=e.sorted,r=e.sorted.length,i=0;i<r;i++){if(!(n[i].pts<=t))return void n.splice(0,i);this.processCCPacket_(e,n[i])}n.length=0},e.prototype.processCCPacket_=function(e,t){e.lastCommandText="";var n=t.ccByte1,r=t.ccByte2,i=(t.ccType,t.ccValid,"");32&n||64&n?(i=this.getCharacter_(n)+this.getCharacter_(r),this.handleCharacters_(e,t,i)):(17===n||25===n)&&r>=48&&r<=63&&(i=o.US[r],this.handleCharacters_(e,t,i)),(17===n||25===n)&&r>=32&&r<=47&&this.handleMidRowChanges_(e,t),(20===n||28===n||21===n||29===r)&&r>=32&&r<=47&&this.handleCommand_(e,t),(23===n||31===n)&&r>=33&&r<=35&&this.handleCommand_(e,t),n>=16&&n<=23&&r>=64&&r<=127&&this.handlePAC_(e,t)},e.prototype.getCharacter_=function(e){return o.SpecialASCII[e]?o.SpecialASCII[e]:0===e?"":String.fromCharCode(e)},e.prototype.handleCharacters_=function(e,t,n){e.mode===k?(e.buffer[1-e.bufferIndex]+=n,e.captionEnd=t.pts,this.finishCaption_(e,1-e.bufferIndex,t),e.captionStart=t.pts):e.buffer[e.bufferIndex]+=n},e.prototype.handleMidRowChanges_=function(e,t){var n=t.ccByte1,r=t.ccByte2,i=!!(1&n);if(i){var a=(14&r)>>1;U[a]}else{var s=(14&r)>>1;U[s]}},e.prototype.handleCommand_=function(e,t){var n=t.ccByte1,r=t.ccByte2;n=23&n;var i=n<<8|r;if(i===e.lastCommand)return void(e.lastCommand=0);switch(e.lastCommand=i,i){case p:e.mode=i;break;case m:break;case v:break;case y:break;case g:break;case _:case b:case T:e.mode=i,this.rollup_(e,t),e.captionEnd=t.pts,this.finishCaption_(e,e.bufferIndex,t),e.captionStart=t.pts;break;case x:e.mode=i;break;case k:e.mode=i,e.captionStart=t.pts;break;case w:e.nonCaptionText="";break;case S:break;case E:e.captionEnd=t.pts,this.finishCaption_(e,1-e.bufferIndex,t);break;case I:e.buffer[e.bufferIndex]+="\n",this.rollup_(e,t);break;case A:e.buffer[e.bufferIndex]="";break;case M:e.mode=p,!isNaN(e.captionStart)&&(isNaN(e.captionEnd)||e.captionEnd<=e.captionStart)&&(e.captionEnd=t.pts,this.finishCaption_(e,1-e.bufferIndex,t)),e.bufferIndex=1-e.bufferIndex,e.captionStart=t.pts;break;case C:e.position[e.bufferIndex]+=2.5;break;case L:e.position[e.bufferIndex]+=5;break;case P:e.position[e.bufferIndex]+=7.5}},e.prototype.finishCaption_=function(e,t,n){if(e.captionStart>-1){var r=e.buffer[t];if(e.lastCommand===E&&(e.buffer[t]=""),!r)return;var i=new f;i.text=r.replace(/\n+/g,"\n").replace(/^\n+/,"").replace(/\n+$/,""),i.start=e.captionStart,i.end=e.captionEnd;var a=new d;a.backgroundColor=e.backgroundColor||a.backgroundColor,a.backgroundTransparency=e.backgroundTransparency||a.backgroundTransparency,a.textColor=e.textColor||a.textColor,a.textStyle=e.textStyle||a.textStyle,i.style=a;var s=new l;s.position=e.position[t],s.line=e.line[t],i.positioning=s,e.captions.push(i),e.captionEnd=NaN}},e.prototype.rollup_=function(e,t){var n=1;switch(e.mode){case _:n=2;break;case b:n=3;break;case T:n=4}var r=e.buffer[e.bufferIndex];r=r.replace(/\n+/g,"\n");var i=r.split("\n");e.buffer[e.bufferIndex]=i.slice(Math.max(0,i.length-n)).join("\n")},
e.prototype.handlePAC_=function(e,t){var n=(30&t.ccByte2)>>1,r=(16&t.ccByte2)>>4,i=(!!(1&t.ccByte2),7&t.ccByte1),a=((32&t.ccByte2)>>5,e.mode===k?1-e.bufferIndex:e.bufferIndex);switch(r){case R:e.position[a]=10+10*D[n]/4;break;case O:}e.line[a]=B[i],16===t.ccByte1&&t.ccByte2>95||(e.buffer[a]+="\n")},e.prototype.getCCDataPackets=function(e,t,n){var r=0,i=e.payload,a=i[e.position++];e.position++,r+=2;var s=31&a;return this.getPackets_(e,t,s,n),r+=3*s},e.prototype.getPackets_=function(e,t,n,r){for(var i=e.payload,a=e.position,s=(e.payload.length,0);s<n;s++){var o=i[a++],u=127&i[a++],f=127&i[a++],l=0!==(4&o),d=3&o;if(d===c["default"].NTSC_CC_FIELD_1){if(0===u&&0===f)continue;var p=!1,m=t.sorted,v=new h;v.pts=r,v.ccByte1=u,v.ccByte2=f,v.ccType=d,v.ccValid=l;for(var y=m.length-1;y>=0;y--)if(m[y].pts<=r){m.splice(y+1,0,v),p=!0;break}p||m.splice(0,0,v)}}e.position=a},e}()},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n,r,i,a){function s(e,s){var o=c.combineUint8Arrays(e,s);return new Promise(function(e){a.then(function(a){a=a instanceof Uint8Array?a:new Uint8Array(a);var s=new h["default"](a,i);return n.closed?(t.close(),void e()):void s.decrypt(o).then(function(n){var r=new Uint8Array(n);l.putAsync(t,r,e)})["catch"](function(n){l.putAsync(t,{type:"error",file:r,message:"Error decrypting AES stream"}),t.close(),e()})})})}var u=[],f=0;return o["default"].mark(function d(){var r;return o["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.t0=l.CLOSED,i.next=3,l.take(e);case 3:if(i.t1=r=i.sent,i.t0===i.t1){i.next=9;break}f+=r.byteLength,u.push(new Uint8Array(r)),i.next=0;break;case 9:if(!n.closed){i.next=12;break}return t.close(),i.abrupt("return");case 12:f>0?s(u,f).then(function(){return t.close()}):t.close();case 13:case"end":return i.stop()}},d,this)})}t.__esModule=!0;var s=n(7),o=i(s);t["default"]=a;var u=n(12),c=r(u),f=n(5),l=r(f),d=n(110),h=i(d)},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){return c["default"].mark(function n(){var r,i,a,u,f,d,p,v,g,_;return c["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r=0,i=1,a=void 0,u=void 0,f=void 0,d=void 0;case 6:return n.t0=m.CLOSED,n.next=9,m.take(e);case 9:if(n.t1=a=n.sent,n.t0===n.t1){n.next=29;break}if("info"!==a.type){n.next=15;break}return n.next=14,m.put(t,a);case 14:return n.abrupt("continue",6);case 15:return u=a,d=u.frames,p=d[0],v=y.last(d),g=y.pluck(d,"data"),f={audioobjecttype:p.audioobjecttype,baseMediaDecodeTime:r,channelcount:p.channelcount,codec:"adts",duration:v.pts+v.frameDuration-p.pts,id:1,maxSegmentDts:v.dts,maxSegmentPts:v.pts,minSegmentDts:p.dts,minSegmentPts:p.pts,samplerate:p.samplerate,samples:s(d),samplesize:p.samplesize,samplingfrequencyindex:p.samplingfrequencyindex,sourceFile:u.sourceFile,timelineStartInfo:{baseMediaDecodeTime:r,dts:p.dts,pts:p.pts},moof:void 0,mdat:void 0,type:"audio"},_=f.samplerate/9e4,r+=f.duration*_,f.moof=l["default"].moof(i,[f]),f.mdat=l["default"].mdat(h.combineUint8Arrays(g)),i++,n.delegateYield(o(u.sourceFile,f,t),"t2",27);case 27:n.next=6;break;case 29:t.close();case 30:case"end":return n.stop()}},n,this)})}function s(e){return y.map(e,function(e){return{size:e.data.byteLength,duration:1024}})}function o(e,t,n){var r;return c["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return r={type:"mediaSegment",track:t,sourceFile:e},i.next=3,m.put(n,r);case 3:case"end":return i.stop()}},v[0],this)}t.__esModule=!0;var u=n(7),c=i(u);t["default"]=a;var f=n(37),l=i(f),d=n(12),h=r(d),p=n(5),m=r(p),v=[o].map(c["default"].mark),y=n(1)},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e){var t=new u["default"](e),n=t.readUnsignedByte(),r=t.readBits(6);t.skipBits(2);var i=t.readUnsignedByte();if(t.skipUnsignedExpGolomb(),100===n||110===n||122===n||244===n||44===n||83===n||86===n||118===n||128===n||138===n||139===n||134===n){var a=t.readUnsignedExpGolomb();if(3===a&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()){var o=3!==a?8:12;for(l=0;l<o;l++)t.readBoolean()&&(l<6?s(16,t):s(64,t))}}t.skipUnsignedExpGolomb();var c=t.readUnsignedExpGolomb();if(0===c)t.readUnsignedExpGolomb();else if(1===c){t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb();for(var f=t.readUnsignedExpGolomb(),l=0;l<f;l++)t.skipExpGolomb()}t.skipUnsignedExpGolomb(),t.skipBits(1);var d=t.readUnsignedExpGolomb(),h=t.readUnsignedExpGolomb(),p=t.readBits(1);0===p&&t.skipBits(1);var m=0,v=0,y=0,g=0;return t.skipBits(1),t.readBoolean()&&(m=t.readUnsignedExpGolomb(),v=t.readUnsignedExpGolomb(),y=t.readUnsignedExpGolomb(),g=t.readUnsignedExpGolomb()),{profileIdc:n,levelIdc:i,profileCompatibility:r,width:16*(d+1)-2*m-2*v,height:(2-p)*(h+1)*16-2*y-2*g}}function a(e){for(var t,n,r=e.byteLength,i=[],a=1;a<r-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(i.push(a+2),a+=2):a++;if(0===i.length)return e;t=r-i.length,n=new Uint8Array(t);var s=0;for(a=0;a<t;s++,a++)s===i[0]&&(s++,i.shift()),n[a]=e[s];return n}function s(e,t){var n,r,i=8,a=8;for(n=0;n<e;n++)0!==a&&(r=t.readExpGolomb(),a=(i+r+256)%256),i=0===a?i:a}t.__esModule=!0;var o=n(111),u=r(o);t["default"]={parseSPS:i,discardEmulationPreventionBytes:a}},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){return g["default"].mark(function n(){var r,i,a,s,o,u,c,f,h,m;return g["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r=new M,i=[],a=void 0,s=0,o=0,u=0,c=void 0,f=void 0;case 8:return n.t0=E.CLOSED,n.next=11,E.take(e);case 11:if(n.t1=c=n.sent,n.t0===n.t1){n.next=30;break}if("info"!==c.type){n.next=17;break}return n.next=16,E.put(t,c);case 16:return n.abrupt("continue",8);case 17:if(f=c,a?f.sourceFile!==a?(h=f.dts-s,m=f.dts-o,m<0||m>18e4?h=o-s+u:u=m,s=f.dts):u=f.dts-o:s=f.dts,o=f.dts,!(r.sps&&r.pps&&i.length>0&&f.flags.hasKeyframe)){n.next=25;break}return n.delegateYield(d(t,r,i,u),"t2",23);case 23:i=[],p(r);case 25:a=f.sourceFile,l(r,f),i.push(f),n.next=8;break;case 30:if(!i.length||r.sps&&r.pps||console.warn("TS File missing Keyframe info"),!t.closed){n.next=33;break}return n.abrupt("return");case 33:return n.delegateYield(d(t,r,i,void 0),"t3",34);case 34:t.close();case 35:case"end":return n.stop()}},n,this)})}function o(e){return 31&e.payload[0]}function u(e){return 9===o(e)}function c(e){return 5===o(e)}function f(e,t){A.isNumber(t.pts)&&(e.minSegmentPts=Math.min(t.pts,e.minSegmentPts),e.maxSegmentPts=Math.max(t.pts,e.maxSegmentPts)),A.isNumber(t.dts)&&(e.minSegmentDts=Math.min(t.dts,e.minSegmentDts),e.maxSegmentDts=Math.max(t.dts,e.maxSegmentDts))}function l(e,t){var n=t.nalus,r=n.length;e.naluCount+=r;for(var i=0;i<r;++i){var a=n[i],s=a.payload,u=o(a);switch(e.nalUnitsByteLength+=s.byteLength,u){case 7:e.sps=s,e.spsInfoRBSP=w["default"].discardEmulationPreventionBytes(s.subarray(1)),e.spsInfo=w["default"].parseSPS(e.spsInfoRBSP);break;case 8:e.pps=s}}}function d(e,t,n,r){var i,a,s,o,u,c,l,d,h;return g["default"].wrap(function(y){for(;;)switch(y.prev=y.next){case 0:if(!(t.naluCount<=0)){y.next=2;break}return y.abrupt("return");case 2:for(;!t.hasSentTrack&&n.length&&!n[0].flags.hasKeyframe;)n.shift();if(n.length){y.next=5;break}return y.abrupt("return");case 5:i=t.nalUnitsByteLength+4*t.naluCount,a=new Uint8Array(i),s=0,o=[],u=0,c=NaN,l=null,d=0;case 13:if(!(d<n.length)){y.next=27;break}if(l=n[d],f(t,l),1!==n.length||r){y.next=21;break}return y.abrupt("continue",24);case 21:c=d===n.length-1?r||l.dts-n[d-1].dts:n[d+1].dts-l.dts;case 22:s=m(t,l,o,c,a,s,u);case 24:d++,y.next=13;break;case 27:if(t.sample&&(o.push(t.sample),t.sample=null),!(o.length>0)){y.next=33;break}return h=l.sourceFile,t.hasSentTrack=!0,y.delegateYield(v(h,t,o,a,e,c),"t0",32);case 32:p(t);case 33:t.naluCount=0,t.nalUnitsByteLength=0;case 35:case"end":return y.stop()}},I[0],this)}function h(e,t,n){return e.lastTrackStartDts!==1/0&&(e.baseMediaDecodeTime+=e.minSegmentDts-e.lastTrackStartDts),e.lastTrackStartDts=e.minSegmentDts,{id:2,samples:t,type:"video",codec:"avc",pps:[e.pps],sps:[e.sps],height:e.spsInfo.height,width:e.spsInfo.width,profileIdc:e.spsInfo.profileIdc,levelIdc:e.spsInfo.levelIdc,profileCompatibility:e.spsInfo.profileCompatibility,baseMediaDecodeTime:e.baseMediaDecodeTime,minSegmentPts:e.minSegmentPts,maxSegmentPts:e.maxSegmentPts,minSegmentDts:e.minSegmentDts,maxSegmentDts:e.maxSegmentDts,duration:e.maxSegmentDts+t[t.length-1].duration-e.minSegmentDts,sourceFile:n,moof:void 0,mdat:void 0}}function p(e){e.minSegmentPts=Number.MAX_VALUE,e.maxSegmentPts=0,e.minSegmentDts=Number.MAX_VALUE,e.maxSegmentDts=0}function m(e,t,n,r,i,a,s){for(var o=t.nalus,f=e.sample,l=0;l<o.length;++l){var d=o[l];u(d)&&(f&&n.push(f),f={size:0,duration:r,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0},dataOffset:a+s,compositionTimeOffset:d.pts-d.dts}),f&&(c(d)&&(f.flags.dependsOn=2),f.size+=d.payload.byteLength+4,x.writeUint32(i,a,d.payload.byteLength),a+=4,i.set(d.payload,a),a+=d.payload.byteLength)}return e.sample=f,a}function v(e,t,n,r,i,a){var s,o;return g["default"].wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return s=h(t,n,e),s.moof=b["default"].moof(t.sequenceNumber,[s]),s.mdat=b["default"].mdat(r),o={type:"mediaSegment",track:s,sourceFile:e},a.next=7,E.put(i,o);case 7:t.sequenceNumber++;case 8:case"end":return a.stop()}},I[1],this)}t.__esModule=!0;var y=n(7),g=i(y);t["default"]=s;var _=n(37),b=i(_),T=n(12),x=r(T),k=n(119),w=i(k),S=n(5),E=r(S),I=[d,v].map(g["default"].mark),A=n(1),M=function C(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;a(this,C),this.nalUnitsByteLength=0,this.naluCount=0,this.sequenceNumber=1,this.minSegmentPts=Number.MAX_VALUE,this.maxSegmentPts=0,this.minSegmentDts=Number.MAX_VALUE,this.maxSegmentDts=0,this.hasSentTrack=!1,this.lastTrackStartDts=1/0,this.baseMediaDecodeTime=e}},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n,r,i,a,s){var o,c,l,h;return u["default"].wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(o=d.combineUint8Arrays(n,r),!(o.length>0)){u.next=29;break}if(c=(0,g["default"])(o),l=c.position,h=o.subarray(l),e.type||!p["default"].probeMP3(h)){u.next=12;break}return e.type=v["default"].ISO_IEC_11172_3_AUDIO,e.targetChannel=s,u.next=10,f.put(e.targetChannel,{type:"info",data:{codecType:"audio",codec:"mp3",codecString:"mp4a.40.34"}});case 10:u.next=24;break;case 12:if(e.type||!p["default"].probeAAC(h)){u.next=19;break}return e.type=v["default"].ISO_IEC_13818_1_RESERVED,e.targetChannel=i,u.next=17,f.put(e.targetChannel,{type:"info",data:{codecType:"audio",codec:"aac",codecString:"mp4a.40.2"}});case 17:u.next=24;break;case 19:if(e.type){u.next=24;break}return u.next=22,f.put(i,{type:"error",message:"ID3 Data found, but audio type could not be determined"});case 22:i.close(),a.close();case 24:if(!c.cues.length){u.next=27;break}return u.next=27,f.put(a,{sourceFile:t,cues:c.cues});case 27:return u.next=29,f.put(e.targetChannel,{sourceFile:t,type:e.type,payload:h});case 29:case"end":return u.stop()}},_[0],this)}function s(e,t,n,r){return u["default"].mark(function i(){var s,o,c,l,d,h;return u["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:s=void 0,o=void 0,c=null,l={},d=[],h=0;case 6:return i.t0=f.CLOSED,i.next=9,f.take(e);case 9:if(i.t1=s=i.sent,i.t0===i.t1){i.next=29;break}if("info"!==s.type||!l.targetChannel){i.next=15;break}return i.next=14,f.put(l.targetChannel,s);case 14:return i.abrupt("continue",6);case 15:if(o=s,!(h>0&&c&&c!==s.sourceFile)){i.next=20;break}return i.delegateYield(a(l,c,d,h,t,n,r),"t2",18);case 18:d=[],h=0;case 20:if(c=s.sourceFile,d.push(s.payload),h+=s.payload.length,!(h>2e4)){i.next=27;break}return i.delegateYield(a(l,c,d,h,t,n,r),"t3",25);case 25:d=[],h=0;case 27:i.next=6;break;case 29:if(!t.closed||!n.closed){i.next=31;break}return i.abrupt("return");case 31:if(!h){i.next=33;break}return i.delegateYield(a(l,c,d,h,t,n,r),"t4",33);case 33:t.close(),n.close();case 35:case"end":return i.stop()}},i,this)})}t.__esModule=!0;var o=n(7),u=i(o);t["default"]=s;var c=n(5),f=r(c),l=n(12),d=r(l),h=n(64),p=i(h),m=n(38),v=i(m),y=n(61),g=i(y),_=[a].map(u["default"].mark)},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n){return o["default"].mark(function r(){var i,a,s,u,f,h;return o["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:i=void 0,a=d.operations.merge([e,t]);case 2:return r.t0=d.CLOSED,r.next=5,d.take(a);case 5:if(r.t1=i=r.sent,r.t0===r.t1){r.next=19;break}if("error"!==i.type){r.next=11;break}return r.next=10,d.put(n,i);case 10:return r.abrupt("continue",2);case 11:if(s=void 0,u=void 0,i.payload?(f=i,h=new Uint8Array(f.payload),s=(0,l["default"])(h).cues,u=f.dts):s=i.cues,0===c.size(s)){r.next=17;break}return r.next=17,d.put(n,{type:"metadata",pts:u,sourceFile:i.sourceFile,payload:s});case 17:r.next=2;break;case 19:n.close();case 20:case"end":return r.stop()}},r,this)})}t.__esModule=!0;var s=n(7),o=i(s);t["default"]=a;var u=n(87),c=r(u),f=n(61),l=i(f),d=n(5)},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){return o["default"].mark(function n(){var r,i,a;return o["default"].wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r=!1,i=void 0,a=void 0;case 3:return n.t0=c.CLOSED,n.next=6,c.take(e);case 6:if(n.t1=a=n.sent,n.t0===n.t1){n.next=26;break}if("info"!==a.type){n.next=10;break}return n.abrupt("continue",3);case 10:if("error"!==a.type){n.next=15;break}return n.next=13,c.put(t,i);case 13:return t.close(),n.abrupt("return");case 15:if(i=a,r){n.next=22;break}return r=!0,n.next=20,c.put(t,{type:"info",data:{trackTypes:[{type:"audio",codecString:"mp4a.40.34"}]}});case 20:return n.next=22,c.put(t,{type:"init",codec:"mp4a.40.34",sourceFile:i.sourceFile,start:0});case 22:return n.next=24,c.put(t,{type:"mediaSegment",codec:"mp4a.40.34",sourceFile:i.sourceFile,payload:i.payload});case 24:n.next=3;break;case 26:t.close();case 27:case"end":return n.stop()}},n,this)})}t.__esModule=!0;var s=n(7),o=i(s);t["default"]=a;var u=n(5),c=r(u)},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n,r){function i(e,t,n){var r,i,a,s,u,c;return o["default"].wrap(function(o){for(;;)switch(o.prev=o.next){case 0:r=Math.floor(t.length/d["default"].TS_PACKET_SIZE),i=0;case 2:if(!(i<r)){o.next=22;break}if(a=i*d["default"].TS_PACKET_SIZE,s=(i+1)*d["default"].TS_PACKET_SIZE,u=t.subarray(a,s),u[0]!==d["default"].TS_SYNCBYTE){o.next=18;break}return o.prev=7,c=(0,h.TsPacket)(u),c.sourceFile=e,o.next=12,y.put(n,c);case 12:o.next=16;break;case 14:o.prev=14,o.t0=o["catch"](7);case 16:o.next=19;break;case 18:console.warn("TS Packet missing SYNC_BYTE");case 19:i++,o.next=2;break;case 22:case"end":return o.stop()}},l[0],this,[[7,14]])}function a(e,t,n){return o["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,y.put(n,{sourceFile:e,payload:t});case 2:case"end":return r.stop()}},l[1],this)}function s(e,t,n){return o["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,y.put(n,{sourceFile:e,type:f["default"].ISO_IEC_11172_3_AUDIO,payload:t});case 2:case"end":return r.stop()}},l[2],this)}function c(e,t,n){return o["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,y.put(n,{sourceFile:e,type:f["default"].ISO_IEC_13818_1_RESERVED,payload:t});case 2:case"end":return r.stop()}},l[3],this)}var l=[i,a,s,c].map(o["default"].mark),p=[];return o["default"].mark(function v(){var f,l,h,g;return o["default"].wrap(function(o){for(;;)switch(o.prev=o.next){case 0:f=void 0,l=0,h=void 0;case 3:return o.t0=y.CLOSED,o.next=6,y.take(e);case 6:if(o.t1=f=o.sent,o.t0===o.t1){o.next=59;break}if("info"!==f.type){o.next=21;break}o.t2=h,o.next=o.t2===u.MPEGTS?12:o.t2===u.ID3?15:o.t2===u.AAC?18:o.t2===u.MP3?18:20;break;case 12:return o.next=14,y.put(t,f);case 14:return o.abrupt("break",20);case 15:return o.next=17,y.put(r,f);case 17:return o.abrupt("break",20);case 18:return o.next=20,y.put(n,f);case 20:return o.abrupt("continue",3);case 21:if("error"!==f.payload.type){o.next=25;break}return o.next=24,y.put(n,f.payload);case 24:return o.abrupt("continue",3);case 25:if(l+=f.payload.length||f.payload.byteLength,p.push(new Uint8Array(f.payload)),g=m.combineUint8Arrays(p,l),h!==u.MPEGTS&&!d["default"].probeMPEGTS(g)){o.next=34;break}return h=u.MPEGTS,o.delegateYield(i(f.sourceFile,g,t),"t3",31);case 31:l=g.length%d["default"].TS_PACKET_SIZE,o.next=56;break;case 34:if(h!==u.ID3&&!d["default"].probeID3(g)){o.next=40;break}return h=u.ID3,o.delegateYield(a(f.sourceFile,g,r),"t4",37);case 37:l=0,o.next=56;break;case 40:if(h!==u.MP3&&!d["default"].probeMP3(g)){o.next=49;break}if(h){o.next=44;break}return o.next=44,y.put(n,{type:"info",data:{codecType:"audio",codec:"mp3",codecString:"mp4a.40.34"}});case 44:return h=u.MP3,o.delegateYield(s(f.sourceFile,g,n),"t5",46);case 46:l=0,o.next=56;break;case 49:if(h!==u.AAC&&!d["default"].probeAAC(g)){o.next=56;break}if(h){o.next=53;break}return o.next=53,y.put(n,{type:"info",data:{codecType:"audio",codec:"aac",codecString:"mp4a.40.2"}});case 53:return h=u.AAC,o.delegateYield(c(f.sourceFile,g,n),"t6",55);case 55:l=0;case 56:p=[g.subarray(g.length-l)],o.next=3;break;case 59:r.close(),t.close();case 62:case"end":return o.stop()}},v,this)})}t.__esModule=!0;var s=n(7),o=i(s);t["default"]=a;var u,c=n(38),f=i(c),l=n(64),d=i(l),h=n(130),p=n(12),m=r(p),v=n(5),y=r(v);!function(e){e[e.MPEGTS=0]="MPEGTS",e[e.AAC=1]="AAC",e[e.MP3=2]="MP3",e[e.ID3=3]="ID3"}(u||(u={}))},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n,r){return y=r,f["default"].mark(function i(){var a,s,c,l,h,m,y,g,_,b,T;return f["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:a={state:"zeroes-0",startCodePrefix:-1,maybeEndOfNalu:-1,naluBuffer:[],chunkFile:"",flags:{},currentPes:null,queueToSend:[],seiQueueToSend:[]},s=void 0,c=void 0,l=void 0,h=!0,m=0;case 6:return i.t0=p.CLOSED,i.next=9,p.take(e);case 9:if(i.t1=s=i.sent,i.t0===i.t1){i.next=52;break}if("info"!==s.type){i.next=15;break}return i.next=14,p.put(t,s);case 14:return i.abrupt("continue",6);case 15:if(c=s,a.currentPes=c,!h){i.next=23;break}h=!1,a.chunkFile=c.sourceFile,l=c.payload,i.next=35;break;case 23:if(1!==c.data_alignment_indicator){i.next=34;break}if(!(a.startCodePrefix>-1)){i.next=27;break}return u(a,l.subarray(a.startCodePrefix)),i.delegateYield(o(t,a),"t2",27);case 27:m=0,l=c.payload,a.state="zeroes-0",a.startCodePrefix=-1,a.chunkFile=c.sourceFile,i.next=35;break;case 34:l=d.concatUint8Array(l,c.payload);case 35:for(;m<l.byteLength;m++)y=l[m],g=v[a.state][y]||v[a.state]["default"]||{},g.newstate&&(a.state=g.newstate),g.method&&g.method.call(a,m,l);_=a.startCodePrefix-(l.byteLength-c.payload.byteLength),_>0&&(l=c.payload,m=l.byteLength,a.maybeEndOfNalu-=a.startCodePrefix-_,a.startCodePrefix=_);case 38:if(!a.queueToSend.length){i.next=44;break}return b=a.queueToSend.shift(),i.next=42,p.put(t,b);case 42:i.next=38;break;case 44:if(!a.seiQueueToSend.length){i.next=50;break}return T=a.seiQueueToSend.shift(),i.next=48,p.put(n,T);case 48:i.next=44;break;case 50:i.next=6;break;case 52:if(!(n.closed&&t.closed&&r.closed)){i.next=54;break}return i.abrupt("return");case 54:if(n.close(),h){i.next=58;break}return u(a,l.subarray(a.startCodePrefix)),i.delegateYield(o(t,a),"t3",58);case 58:t.close(),r.close();case 60:case"end":return i.stop()}},i,this)})}function s(e){var t={sourceFile:e.chunkFile,pts:e.naluBuffer[0].pts,dts:e.naluBuffer[0].dts,flags:e.flags,nalus:e.naluBuffer};return e.flags={},e.naluBuffer=[],e.chunkFile=e.currentPes.sourceFile,t}function o(e,t){var n;return f["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return n=s(t),r.next=3,p.put(e,n);case 3:case"end":return r.stop()}},m[0],this)}function u(e,t){if(t.length){var n=128&t[0];if(!n){var r=31&t[0],i={pts:e.currentPes.pts,dts:e.currentPes.dts,payload:t};switch(r){case 5:e.flags.hasKeyframe=!0;break;case 6:e.seiQueueToSend.push(i);break;case 7:e.flags.hasSPS=!0;break;case 8:e.flags.hasPPS=!0}var a=9===r;if(a&&e.naluBuffer.length){var o=s(e);e.queueToSend.push(o)}e.naluBuffer.push(i)}}}t.__esModule=!0;var c=n(7),f=i(c);t["default"]=a;var l=n(12),d=r(l),h=n(5),p=r(h),m=[o].map(f["default"].mark),v={"zeroes-0":{0:{newstate:"zeroes-1"}},"zeroes-1":{0:{newstate:"zeroes-2"},"default":{newstate:"zeroes-0"}},"zeroes-2":{0:{newstate:"zeroes-3plus",method:function(e){this.maybeEndOfNalu=e}},1:{newstate:"sync",method:function(e,t){if(this.startCodePrefix>=0){var n=t.subarray(this.startCodePrefix,e-2);u(this,n)}}},"default":{newstate:"zeroes-0"}},"zeroes-3plus":{0:{newstate:"zeroes-3plus"},1:{newstate:"sync",method:function(e,t){if(this.startCodePrefix>=0){var n=t.subarray(this.startCodePrefix,this.maybeEndOfNalu-2);u(this,n)}}},"default":{newstate:"zeroes-0"}},sync:{0:{newstate:"zeroes-1",method:function(e){this.startCodePrefix=e}},"default":{newstate:"zeroes-0",method:function(e){this.startCodePrefix=e}}}},y=void 0},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){var n={},r=[],i=-1,a=void 0,u=d["default"].mark(function l(){var e,r,i;return d["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:e=g.keys(a),r=0;case 2:if(!(r<e.length)){s.next=8;break}return i=parseInt(e[r]),s.delegateYield(f(t,a,n,i),"t0",5);case 5:r++,s.next=2;break;case 8:case"end":return s.stop()}},l,this)});return d["default"].mark(function h(){var f,l,m;return d["default"].wrap(function(d){for(;;)switch(d.prev=d.next){case 0:f=void 0,l=void 0;case 2:return d.next=4,p.take(e);case 4:if(d.t0=l=d.sent,d.t1=p.CLOSED,d.t0===d.t1){d.next=35;break}if("info"!==l.type){d.next=13;break}if(!l.data.endOfFile){d.next=10;break}return d.delegateYield(u(),"t2",10);case 10:return d.next=12,p.put(t,l);case 12:return d.abrupt("continue",2);case 13:if(f=l,m=f.header.pid,0!==m){d.next=19;break}i=s(f),d.next=33;break;case 19:if(m!==i){d.next=28;break}a=o(f);case 21:if(!r.length){d.next=26;break}return f=r.shift(),d.delegateYield(c(t,a,n,f),"t3",24);case 24:d.next=21;break;case 26:d.next=33;break;case 28:if(!a){d.next=32;break}return d.delegateYield(c(t,a,n,f),"t4",30);case 30:d.next=33;break;case 32:r.push(f);case 33:d.next=2;break;case 35:if(!e.closed||!t.closed){d.next=37;break}return d.abrupt("return");case 37:return d.delegateYield(u(),"t5",38);case 38:t.close();case 39:case"end":return d.stop()}},h,this)})}function s(e){u(e);var t=0;if(t|=(15&e.payload[1])<<4,t|=(240&e.payload[2])>>4,t>13)throw new Error("Multiple PMT/NIT entries not supported");var n=0;return n|=(31&e.payload[10])<<8|e.payload[11]}function o(e){var t={};u(e);for(var n=e.payload,r=(15&n[1])<<8|n[2],i=r+3,a=(15&n[10])<<8|n[11],s=12+a;s<i;){var o=(31&n[s+1])<<8|n[s+2];t[o]=n[s],s+=((15&n[s+3])<<8|n[s+4])+5}return t}function u(e){var t=1;t+=e.payload[0],e.payload=e.payload.subarray(t)}function c(e,t,n,r){var i;return d["default"].wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(i=r.header.pid,t[i]){a.next=4;break}return a.abrupt("return");case 4:if(n[i]||(n[i]=[]),!r.header.payloadStart){a.next=8;break}return a.delegateYield(f(e,t,n,i),"t0",7);case 7:n[i]=[];case 8:n[i].push(r);case 9:case"end":return a.stop()}},y[0],this)}function f(e,t,n,r){var i,a,s,o,u,c,f,l;return d["default"].wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(i=n[r],g.isArray(i)&&i.length){d.next=3;break}return d.abrupt("return");case 3:for(a=t[r],s=g.reduce(i,function(e,t){return t.header.containsPayload?e+t.payload.length:e},0),o=new Uint8Array(s),u=i[0].sourceFile,c=0;i.length;)f=i.shift(),f.header.containsPayload&&(o.set(f.payload,c),c+=f.payload.length);if(l=v["default"].parsePES(a,o)){d.next=12;break}return d.abrupt("return");case 12:return l.sourceFile=u,console.assert(e.closed===!1,"PES Packet Channel should not be closed"),d.next=16,p.put(e,l);case 16:case"end":return d.stop()}},y[1],this)}t.__esModule=!0;var l=n(7),d=i(l);t["default"]=a;var h=n(5),p=r(h),m=n(127),v=i(m),y=[c,f].map(d["default"].mark),g=n(1)},function(e,t,n){"use strict";t.__esModule=!0;var r={PRIVATE_STREAM_1:189,PADDING_STREAM:190,PRIVATE_STREAM_2:191},i={readTS:function(e){var t=(14&e[0])*(1<<29),n=(255&e[1])<<22|(254&e[2])<<14|(255&e[3])<<7|(255&e[4])>>>1;return t+n},parsePES:function(e,t){var n=t[0]<<16|t[1]<<8|t[2];if(1===n){var i=t[3];t[4]<<8|t[5];if(i!==r.PADDING_STREAM&&i!==r.PRIVATE_STREAM_2){var a=t[7]>>>6,s=(4&t[6])>>>2,o=t[8]+9,u=void 0,c=void 0;if(2===a)u=this.readTS(t.subarray(9)),c=u;else{if(3!==a)return;u=this.readTS(t.subarray(9)),c=this.readTS(t.subarray(14))}var f={id:i,type:e,pts:u,dts:c,data_alignment_indicator:s,payload:t.subarray(o)};return f}}}};t["default"]=i},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function i(e,t,n){return u["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,d.put(e,{type:"segmentData",start:t.minSegmentPts,end:t.maxSegmentPts,length:t.duration,track:t,moof:t.moof,mdat:t.mdat,sourceFile:n});case 2:case"end":return r.stop()}},l[0],this)}function a(e,t,n){return u["default"].wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,d.put(e,{type:"segmentData",frames:t,sourceFile:n});case 2:case"end":return r.stop()}},l[1],this)}function s(e,t,n,r){return u["default"].mark(function s(){var o,c,l,p,m,v,y,g,_,b,T,x,k;return u["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:o={},c=Number.MAX_VALUE,l=[],p=[],m=void 0,v=!1,y=u["default"].mark(function w(e){var t,s,o,c,d,h,m,y,g,_,b,T,x,k;return u["default"].wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(!l.length||!p.length){u.next=42;break}if(t=l[0],s=t.maxSegmentPts+t.samples[t.samples.length-1].duration,o=p.length-1,c=p[o],d=c.pts+c.frameDuration,f["default"].doRangesOverlap({start:p[0].pts,end:d},{start:t.minSegmentPts,end:s},!0)){u.next=8;break}return u.abrupt("return");case 8:if(!(e&&d-s<0)){u.next=10;break}return u.abrupt("return");case 10:h=Math.floor((d-s)/c.frameDuration),m=Math.min(o,Math.max(0,o-h)),y=!1,g=void 0,_=void 0,b=void 0,T=1/0,x=void 0;case 19:if(y){u.next=34;break}if(g=p[m]){u.next=29;break}if(e||!_||p.indexOf(_)!==p.length-1){u.next=26;break}y=!0,u.next=27;break;case 26:return u.abrupt("return");case 27:u.next=32;break;case 29:b=g.pts+g.frameDuration,x=b-s,x>=0&&x<=g.frameDuration?(y=!0,_=g):Math.abs(x)>Math.abs(T)?(y=!0,m=p.indexOf(_)):(_=g,T=x,m+=x<0?1:-1);case 32:u.next=19;break;case 34:if(k=p.splice(0,m+1),!v)for(;k[0]&&k[0].pts<t.minSegmentPts;)k.shift();return v=!0,u.delegateYield(i(n,l.shift(),t.sourceFile),"t0",38);case 38:if(!k.length){u.next=40;break}return u.delegateYield(a(r,k,t.sourceFile),"t1",40);case 40:u.next=0;break;case 42:case"end":return u.stop()}},w,this)}),g=u["default"].mark(function S(e){var t,s,o=this;return u["default"].wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if("video"!==e){c.next=8;break}case 1:if(!l.length){c.next=6;break}return t=l.shift(),c.delegateYield(i(n,t,t.sourceFile),"t0",4);case 4:c.next=1;break;case 6:c.next=13;break;case 8:s=u["default"].mark(function f(){var e,t,n;return u["default"].wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return e=p[0].sourceFile,t=h.find(p,function(t){return t.sourceFile!==e}),n=t?p.indexOf(t):p.length,i.delegateYield(a(r,p.splice(0,n),e),"t0",4);case 4:case"end":return i.stop()}},f,o)});case 9:if(!p.length){c.next=13;break}return c.delegateYield(s(),"t1",11);case 11:c.next=9;break;case 13:case"end":return c.stop()}},S,this)}),_=u["default"].mark(function E(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return u["default"].wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(1!==c){t.next=4;break}return t.delegateYield(g(h.keys(o)[0]),"t0",2);case 2:t.next=6;break;case 4:if(2!==c){t.next=6;break}return t.delegateYield(y(e),"t1",6);case 6:case"end":return t.stop()}},E,this)}),b=void 0,T=d.operations.merge([e,t]);case 11:return s.t0=d.CLOSED,s.next=14,d.take(T);case 14:if(s.t1=m=s.sent,s.t0===s.t1){s.next=47;break}if("error"!==m.type){s.next=20;break}return s.next=19,d.put(n,m);case 19:return s.abrupt("continue",11);case 20:if("info"!==m.type){s.next=42;break}if(b=m.data,c!==Number.MAX_VALUE){s.next=33;break}if(b.codecType&&(o[b.codecType]={type:b.codecType,codecString:b.codecString}),x=h.size(o),2!==x&&!b.endOfFile){s.next=33;break}if(c=h.size(o),!c){s.next=32;break}return s.next=30,d.put(n,{type:"info",data:{trackTypes:h.map(o,function(e){return e})}});case 30:s.next=33;break;case 32:throw new Error("No tracks found after end of file.");case 33:if(!b.endOfFile){s.next=35;break}return s.delegateYield(_(),"t2",35);case 35:if(!b.codecType){s.next=37;break}return s.abrupt("continue",11);case 37:return s.next=39,d.put(n,m);case 39:return s.next=41,d.put(r,m);case 41:return s.abrupt("continue",11);case 42:return k=m.track?"video":"audio","video"===k?l.push(m.track):"audio"===k&&(p=p.concat(m.frames)),s.delegateYield(_(),"t3",45);case 45:s.next=11;break;case 47:if(!n.closed||!r.closed){s.next=49;break}return s.abrupt("return");case 49:return s.delegateYield(_(!1),"t4",50);case 50:n.close(),r.close();case 52:case"end":return s.stop()}},s,this)})}t.__esModule=!0;var o=n(7),u=r(o);t["default"]=s;var c=n(30),f=r(c),l=[i,a].map(u["default"].mark),d=n(5),h=n(1)},function(e,t,n){"use strict";function r(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t["default"]=e,t}function i(e){return e&&e.__esModule?e:{"default":e}}function a(e,t,n,r){var i=!1,a=!1;return o["default"].mark(function s(){var u,f,d;return o["default"].wrap(function(s){for(;;)switch(s.prev=s.next){case 0:u=void 0,f=void 0;case 2:return s.t0=l.CLOSED,s.next=5,l.take(e);case 5:if(s.t1=f=s.sent,s.t0===s.t1){s.next=51;break}if(!(n.closed&&r.closed&&t.closed)){s.next=9;break}return s.abrupt("return");case 9:if(d=f.type,"error"!==d){s.next=14;break}return s.next=13,l.put(t,f);case 13:return s.abrupt("break",51);case 14:if("info"!==d){s.next=20;break}return s.next=17,l.put(t,f);case 17:return s.next=19,l.put(n,f);case 19:return s.abrupt("continue",2);case 20:if(u=f,d!==c["default"].ISO_IEC_13818_1_RESERVED){s.next=30;break}if(i){s.next=26;break}return i=!0,s.next=26,l.put(t,{type:"info",data:{codecType:"audio",codec:"aac",codecString:"mp4a.40.2"}});case 26:return s.next=28,l.put(t,u);case 28:s.next=49;break;case 30:if(d!==c["default"].ITU_T_H264){s.next=39;break;
}if(a){s.next=35;break}return a=!0,s.next=35,l.put(n,{type:"info",data:{codecType:"video",codec:"default",codecString:"avc1.4d4015"}});case 35:return s.next=37,l.put(n,u);case 37:s.next=49;break;case 39:if(d!==c["default"].METADATA_IN_PES){s.next=44;break}return s.next=42,l.put(r,u);case 42:s.next=49;break;case 44:if("error"!==d){s.next=49;break}return s.next=47,l.put(r,u);case 47:s.next=49;break;case 49:s.next=2;break;case 51:n.close(),r.close(),t.close();case 54:case"end":return s.stop()}},s,this)})}t.__esModule=!0;var s=n(7),o=i(s);t.PesPacketSplitter=a;var u=n(38),c=i(u),f=n(5),l=r(f)},function(e,t){"use strict";function n(e){var t={syncByte:e[0],payloadStart:0!==(64&e[1]),priority:0!==(32&e[1]),pid:(31&e[1])<<8|e[2],adaptationField:0!==(32&e[3]),containsPayload:0!==(16&e[3]),continuityCounter:15&e[3]},n=4;if(t.adaptationField){n+=1;var r=e[4];n+=r}var i;i=t.containsPayload?e.subarray(n):null;var a={header:t,payload:i};return a}t.__esModule=!0,t.TsPacket=n},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){(function(t,n){!function(t){"use strict";function r(e,t,n,r){var i=Object.create((t||a).prototype),s=new p(r||[]);return i._invoke=l(e,n,s),i}function i(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}function a(){}function s(){}function o(){}function u(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function c(e){this.arg=e}function f(e){function t(n,r,a,s){var o=i(e[n],e,r);if("throw"!==o.type){var u=o.arg,f=u.value;return f instanceof c?Promise.resolve(f.arg).then(function(e){t("next",e,a,s)},function(e){t("throw",e,a,s)}):Promise.resolve(f).then(function(e){u.value=e,a(u)},s)}s(o.arg)}function r(e,n){function r(){return new Promise(function(r,i){t(e,n,r,i)})}return a=a?a.then(r,r):r()}"object"==typeof n&&n.domain&&(t=n.domain.bind(t));var a;this._invoke=r}function l(e,t,n){var r=w;return function(a,s){if(r===E)throw new Error("Generator is already running");if(r===I){if("throw"===a)throw s;return v()}for(;;){var o=n.delegate;if(o){if("return"===a||"throw"===a&&o.iterator[a]===y){n.delegate=null;var u=o.iterator["return"];if(u){var c=i(u,o.iterator,s);if("throw"===c.type){a="throw",s=c.arg;continue}}if("return"===a)continue}var c=i(o.iterator[a],o.iterator,s);if("throw"===c.type){n.delegate=null,a="throw",s=c.arg;continue}a="next",s=y;var f=c.arg;if(!f.done)return r=S,f;n[o.resultName]=f.value,n.next=o.nextLoc,n.delegate=null}if("next"===a)n.sent=n._sent=s;else if("throw"===a){if(r===w)throw r=I,s;n.dispatchException(s)&&(a="next",s=y)}else"return"===a&&n.abrupt("return",s);r=E;var c=i(e,t,n);if("normal"===c.type){r=n.done?I:S;var f={value:c.arg,done:n.done};if(c.arg!==A)return f;n.delegate&&"next"===a&&(s=y)}else"throw"===c.type&&(r=I,a="throw",s=c.arg)}}}function d(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function h(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function p(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(d,this),this.reset(!0)}function m(e){if(e){var t=e[b];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function i(){for(;++n<e.length;)if(g.call(e,n))return i.value=e[n],i.done=!1,i;return i.value=y,i.done=!0,i};return r.next=r}}return{next:v}}function v(){return{value:y,done:!0}}var y,g=Object.prototype.hasOwnProperty,_="function"==typeof Symbol?Symbol:{},b=_.iterator||"@@iterator",T=_.toStringTag||"@@toStringTag",x="object"==typeof e,k=t.regeneratorRuntime;if(k)return void(x&&(e.exports=k));k=t.regeneratorRuntime=x?e.exports:{},k.wrap=r;var w="suspendedStart",S="suspendedYield",E="executing",I="completed",A={},M=o.prototype=a.prototype;s.prototype=M.constructor=o,o.constructor=s,o[T]=s.displayName="GeneratorFunction",k.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===s||"GeneratorFunction"===(t.displayName||t.name))},k.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,o):(e.__proto__=o,T in e||(e[T]="GeneratorFunction")),e.prototype=Object.create(M),e},k.awrap=function(e){return new c(e)},u(f.prototype),k.async=function(e,t,n,i){var a=new f(r(e,t,n,i));return k.isGeneratorFunction(t)?a:a.next().then(function(e){return e.done?e.value:a.next()})},u(M),M[b]=function(){return this},M[T]="Generator",M.toString=function(){return"[object Generator]"},k.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},k.values=m,p.prototype={constructor:p,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=y,this.done=!1,this.delegate=null,this.tryEntries.forEach(h),!e)for(var t in this)"t"===t.charAt(0)&&g.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=y)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){function t(t,r){return a.type="throw",a.arg=e,n.next=t,!!r}if(this.done)throw e;for(var n=this,r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return t("end");if(i.tryLoc<=this.prev){var s=g.call(i,"catchLoc"),o=g.call(i,"finallyLoc");if(s&&o){if(this.prev<i.catchLoc)return t(i.catchLoc,!0);if(this.prev<i.finallyLoc)return t(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return t(i.catchLoc,!0)}else{if(!o)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return t(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&g.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?this.next=i.finallyLoc:this.complete(a),A},complete:function(e,t){if("throw"===e.type)throw e.arg;"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=e.arg,this.next="end"):"normal"===e.type&&t&&(this.next=t)},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),h(n),A}},"catch":function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;h(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:m(e),resultName:t,nextLoc:n},A}}}("object"==typeof t?t:"object"==typeof window?window:"object"==typeof self?self:this)}).call(t,function(){return this}(),n(85))},,,,,,,function(e,t,n){var r,i;r=[],i=function(){var e=!0,t={media:{TextSourceBuffer:{isTypeSupported:function(){return!1}}},asserts:{assert:function(){}},util:{EventManager:function(){this.destroy=function(){},this.listen=function(e,t,n){e.addEventListener(t,n,!1)}},PublicPromise:function(){var e,t,n=new Promise(function(n,r){e=n,t=r});return n.resolve=e,n.reject=t,n}}};return t.media.MediaSourceEngine=function(e,n){t.asserts.assert("open"==e.readyState,"The MediaSource should be in the 'open' state."),this.mediaSource_=e,this.textTrack_=n,this.sourceBuffers_={},this.queues_={},this.eventManager_=new t.util.EventManager,this.destroyed_=!1},t.media.MediaSourceEngine.Operation,t.media.MediaSourceEngine.isTypeSupported=function(e){return t.media.TextSourceBuffer.isTypeSupported(e)||MediaSource.isTypeSupported(e)},t.media.MediaSourceEngine.support=function(){var e={basic:!!window.MediaSource};if(e.basic){var n=['video/mp4; codecs="avc1.42E01E"','audio/mp4; codecs="mp4a.40.2"','video/webm; codecs="vp8"','video/webm; codecs="vp9"','audio/webm; codecs="vorbis"','audio/webm; codecs="opus"','video/mp2t; codecs="avc1.42E01E"','video/mp2t; codecs="mp4a.40.2"',"text/vtt",'application/mp4; codecs="wvtt"',"application/ttml+xml",'application/mp4; codecs="stpp"'];n.forEach(function(n){e[n]=t.media.MediaSourceEngine.isTypeSupported(n);var r=n.split(";")[0];e[r]=e[r]||e[n]})}return e},t.media.MediaSourceEngine.prototype.destroy=function(){this.destroyed_=!0;var n=[];for(var r in this.queues_){var i=this.queues_[r],a=i[0];this.queues_[r]=i.slice(0,1),a&&n.push(a.p["catch"](function(){}));for(var s=1;s<i.length;++s)i[s].p["catch"](function(){}),i[s].p.reject()}return Promise.all(n).then(function(){if(this.eventManager_.destroy(),this.eventManager_=null,this.mediaSource_=null,this.textTrack_=null,this.sourceBuffers_={},!e)for(var n in this.queues_)t.asserts.assert(0==this.queues_[n].length,n+" queue should be empty after destroy!");this.queues_={}}.bind(this))},t.media.MediaSourceEngine.prototype.init=function(e){for(var n in e){var r=e[n];t.asserts.assert(t.media.MediaSourceEngine.isTypeSupported(r),"Type negotation should happen before MediaSourceEngine.init!");var i;if("text"==n){var a=new t.media.TextSourceBuffer(this.textTrack_,r);i=a}else i=this.mediaSource_.addSourceBuffer(r);this.eventManager_.listen(i,"error",this.onError_.bind(this,n)),this.eventManager_.listen(i,"updateend",this.onUpdateEnd_.bind(this,n)),this.sourceBuffers_[n]=i,this.queues_[n]=[]}},t.media.MediaSourceEngine.prototype.bufferedAheadOf=function(e,t){for(var n=this.sourceBuffers_[e].buffered,r=1e-6,i=0;i<n.length;++i)if(t+r>=n.start(i)&&t<n.end(i))return n.end(i)-t;return 0},t.media.MediaSourceEngine.prototype.appendBuffer=function(e,t){return this.enqueueOperation_(e,this.append_.bind(this,e,t))},t.media.MediaSourceEngine.prototype.remove=function(e,t,n){return this.enqueueOperation_(e,this.remove_.bind(this,e,t,n))},t.media.MediaSourceEngine.prototype.endOfStream=function(e){return this.enqueueBlockingOperation_(function(){e?this.mediaSource_.endOfStream(e):this.mediaSource_.endOfStream()}.bind(this))},t.media.MediaSourceEngine.prototype.setDuration=function(e){return isNaN(this.mediaSource_.duration)||t.asserts.assert(this.mediaSource_.duration<=e,"duration cannot decrease: "+this.mediaSource_.duration+" -> "+e),this.enqueueBlockingOperation_(function(){this.mediaSource_.duration=e}.bind(this))},t.media.MediaSourceEngine.prototype.append_=function(e,t){this.sourceBuffers_[e].appendBuffer(t)},t.media.MediaSourceEngine.prototype.remove_=function(e,t,n){this.sourceBuffers_[e].remove(t,n)},t.media.MediaSourceEngine.prototype.onError_=function(e,n){var r=this.queues_[e][0];t.asserts.assert(r,"Spurious error event!"),t.asserts.assert(!this.sourceBuffers_[e].updating,"SourceBuffer should not be updating on error!"),r.p.reject(n)},t.media.MediaSourceEngine.prototype.onUpdateEnd_=function(e,n){var r=this.queues_[e][0];t.asserts.assert(r,"Spurious updateend event!"),t.asserts.assert(!this.sourceBuffers_[e].updating,"SourceBuffer should not be updating on updateend!"),r.p.resolve(),this.popFromQueue_(e)},t.media.MediaSourceEngine.prototype.enqueueOperation_=function(e,n){if(this.destroyed_)return Promise.reject();var r={start:n,p:new t.util.PublicPromise};if(this.queues_[e].push(r),1==this.queues_[e].length)try{r.start()}catch(i){r.p.reject(i),this.popFromQueue_(e)}return r.p},t.media.MediaSourceEngine.prototype.enqueueBlockingOperation_=function(n){if(this.destroyed_)return Promise.reject();var r=[];for(var i in this.sourceBuffers_){var a=new t.util.PublicPromise,s={start:function(e){e.resolve()}.bind(null,a),p:a};this.queues_[i].push(s),r.push(a),1==this.queues_[i].length&&s.start()}return Promise.all(r).then(function(){if(!e)for(var r in this.sourceBuffers_)t.asserts.assert(0==this.sourceBuffers_[r].updating,"SourceBuffers should not be updating after a blocking op!");var i;try{n()}catch(a){i=Promise.reject(a)}for(var r in this.sourceBuffers_)this.popFromQueue_(r);return i}.bind(this),function(){if(t.asserts.assert(this.destroyed_,"Should be destroyed by now"),!e)for(var n in this.sourceBuffers_)this.queues_[n].length&&(t.asserts.assert(1==this.queues_[n].length,"Should be at most one item in queue!"),t.asserts.assert(r.indexOf(this.queues_[n][0].p)!=-1,"The item in queue should be one of our waiters!"),this.queues_[n].shift());return Promise.reject()}.bind(this))},t.media.MediaSourceEngine.prototype.popFromQueue_=function(e){this.queues_[e].shift();var t=this.queues_[e][0];if(t)try{t.start()}catch(n){t.p.reject(n),this.popFromQueue_(e)}},t.media.MediaSourceEngine.prototype.setTimestampOffset=function(e,t){return this.enqueueBlockingOperation_(function(){this.sourceBuffers_[e].timestampOffset=t}.bind(this))},t.media.MediaSourceEngine}.apply(t,r),!(void 0!==i&&(e.exports=i))}]);