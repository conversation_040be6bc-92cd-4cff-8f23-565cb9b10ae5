<?php
/*
* ----------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
* @since 2.5.5
*/

# Theme options
define('DOO_THEME_DOWNLOAD_MOD', true);
define('DOO_THEME_PLAYER_MOD',   true);
define('DOO_THEME_DBMOVIES',     true);
define('DOO_THEME_USER_MOD',     true);
define('DOO_THEME_VIEWS_COUNT',  true);
define('DOO_THEME_RELATED',      true);
define('DOO_THEME_SOCIAL_SHARE', true);
define('DOO_THEME_CACHE',        true);
define('DOO_THEME_PLAYERSERNAM', true);
define('DOO_THEME_JSCOMPRESS',   true);
define('DOO_THEME_TOTAL_POSTC',  true);
define('DOO_THEME_LAZYLOAD',     false);
# Repository data
define('DOO_COM','Doothemes');
define('DOO_VERSION','2.5.5');
define('Bes_VERSION','2.5.5.1'); // Bescraper version for wp_update only 23/06/2021
define('DOO_VERSION_DB','2.8');
define('DOO_ITEM_ID','154');
define('DOO_PHP_REQUIRE','7.1');
define('DOO_THEME','Dooplay');
define('DOO_THEME_SLUG','dooplay');
define('DOO_SERVER','https://cdn.bescraper.cf/api');
define('DOO_GICO','https://s2.googleusercontent.com/s2/favicons?domain=');

# Configure Here date format #
define('DOO_TIME','M. d, Y');  // More Info >>> https://www.php.net/manual/function.date.php
##############################

# Define Rating data
define('DOO_MAIN_RATING','_starstruck_avg');
define('DOO_MAIN_VOTOS','_starstruck_total');
# Define Options key
define('DOO_OPTIONS','_dooplay_options');
define('DOO_CUSTOMIZE', '_dooplay_customize');
# Define template directory
define('DOO_URI',get_template_directory_uri());
define('DOO_DIR',get_template_directory());

# Translations
load_theme_textdomain('dooplay', DOO_DIR.'/lang/');

# Load Application
require get_parent_theme_file_path('/inc/doo_init.php');

/* Custom functions
========================================================
*/

// Function to get episode download links
function get_episode_download_links($episode_id) {
    global $wpdb;

    $links = array();

    // Get all links for this episode
    $sql = "SELECT ID FROM {$wpdb->posts} WHERE post_parent = %d AND post_type = 'dt_links' AND post_status = 'publish' ORDER BY ID DESC";
    $link_posts = $wpdb->get_results($wpdb->prepare($sql, $episode_id));

    if($link_posts) {
        foreach($link_posts as $link_post) {
            $link_id = $link_post->ID;

            // Get link metadata
            $type = get_post_meta($link_id, '_dool_type', true);
            $url = get_post_meta($link_id, '_dool_url', true);
            $quality = get_post_meta($link_id, '_dool_quality', true);
            $language = get_post_meta($link_id, '_dool_language', true);
            $size = get_post_meta($link_id, '_dool_size', true);

            // Only include download and streaming links
            if($type == __d('Download') || $type == __d('Watch online')) {
                $links[] = array(
                    'id' => $link_id,
                    'type' => $type,
                    'quality' => $quality ?: 'HD',
                    'language' => $language ?: 'EN',
                    'size' => $size ?: '----',
                    'download_url' => get_permalink($link_id),
                    'stream_url' => $url,
                    'direct_url' => $url
                );
            }
        }
    }

    return $links;
}

// Enqueue episode links assets
function enqueue_episode_links_assets() {
    wp_enqueue_style('episode-links-css', get_template_directory_uri() . '/assets/css/custom-episodes.css', array(), '1.0.0');
    wp_enqueue_script('episode-links-js', get_template_directory_uri() . '/assets/js/custom-episodes.js', array('jquery'), '1.0.0', true);
}
add_action('wp_enqueue_scripts', 'enqueue_episode_links_assets');

	// Here your code
