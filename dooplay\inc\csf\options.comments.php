<?php
/**
 * @since 2.5.0
 * @version 2.0
 */
CSF::createSection(DOO_OPTIONS,
    array(
        'title'  => __d('Comments'),
        'parent' => 'settings',
        'icon'   => 'fa fa-minus',
        'fields' => array(
            array(
                'id'      => 'comments',
                'type'    => 'radio',
                'title'   => __d('Comments system'),
                'default' => 'wp',
                'options' => array(
                    'wp' => __d('WordPress'),
                    'fb' => __d('Facebook'),
                    'dq' => __d('Disqus'),
                    'nn' => __d('None')
                )
            ),
            array(
                'id'    => 'commentspage',
                'type'  => 'switcher',
                'title' => __d('Comments on pages'),
                'label' => __d('Allow comments on all pages?'),
                'dependency' => array('comments', '!=', 'nn')
            ),
            array(
                'type'    => 'subheading',
                'content' => __d('Facebook comments'),
                'dependency' => array('comments', '==', 'fb')
            ),
            array(
                'id'    => 'fbappid',
                'type'  => 'text',
                'title' => __d('App ID'),
                'subtitle'  => __d('Insert you Facebook app ID'),
                'after' => '<p><a href="https://developers.facebook.com/apps/" target="_blank">'.__d('Facebook developers').'</a></p>',
                'dependency' => array('comments', '==', 'fb')
            ),
            array(
                'id'    => 'fblang',
                'type'  => 'select',
                'title' => __d('APP language'),
                'subtitle'  => __d('Select language for the app of facebook'),
                'options' => array(
                    'af_ZA' => __d('Afrikaans'),
                    'ak_GH' => __d('Akan'),
                    'am_ET' => __d('Amharic'),
                    'ar_AR' => __d('Arabic'),
                    'as_IN' => __d('Assamese'),
                    'ay_BO' => __d('Aymara'),
                    'az_AZ' => __d('Azerbaijani'),
                    'be_BY' => __d('Belarusian'),
                    'bg_BG' => __d('Bulgarian'),
                    'bn_IN' => __d('Bengali'),
                    'br_FR' => __d('Breton'),
                    'bs_BA' => __d('Bosnian'),
                    'ca_ES' => __d('Catalan'),
                    'cb_IQ' => __d('Sorani Kurdish'),
                    'ck_US' => __d('Cherokee'),
                    'co_FR' => __d('Corsican'),
                    'cs_CZ' => __d('Czech'),
                    'cx_PH' => __d('Cebuano'),
                    'cy_GB' => __d('Welsh'),
                    'da_DK' => __d('Danish'),
                    'de_DE' => __d('German'),
                    'el_GR' => __d('Greek'),
                    'en_GB' => __d('English (UK)'),
                    'en_IN' => __d('English (India)'),
                    'en_PI' => __d('English (Pirate)'),
                    'en_UD' => __d('English (Upside Down)'),
                    'en_US' => __d('English (US)'),
                    'eo_EO' => __d('Esperanto'),
                    'es_CL' => __d('Spanish (Chile)'),
                    'es_CO' => __d('Spanish (Colombia)'),
                    'es_ES' => __d('Spanish (Spain)'),
                    'es_LA' => __d('Spanish (Latin America)'),
                    'es_MX' => __d('Spanish (Mexico)'),
                    'es_VE' => __d('Spanish (Venezuela)'),
                    'et_EE' => __d('Estonian'),
                    'eu_ES' => __d('Basque'),
                    'fa_IR' => __d('Persian'),
                    'fb_LT' => __d('Leet Speak'),
                    'ff_NG' => __d('Fulah'),
                    'fi_FI' => __d('Finnish'),
                    'fo_FO' => __d('Faroese'),
                    'fr_CA' => __d('French (Canada)'),
                    'fr_FR' => __d('French (France)'),
                    'fy_NL' => __d('Frisian'),
                    'ga_IE' => __d('Irish'),
                    'gl_ES' => __d('Galician'),
                    'gn_PY' => __d('Guarani'),
                    'gu_IN' => __d('Gujarati'),
                    'gx_GR' => __d('Classical Greek'),
                    'ha_NG' => __d('Hausa'),
                    'he_IL' => __d('Hebrew'),
                    'hi_IN' => __d('Hindi'),
                    'hr_HR' => __d('Croatian'),
                    'hu_HU' => __d('Hungarian'),
                    'hy_AM' => __d('Armenian'),
                    'id_ID' => __d('Indonesian'),
                    'ig_NG' => __d('Igbo'),
                    'is_IS' => __d('Icelandic'),
                    'it_IT' => __d('Italian'),
                    'ja_JP' => __d('Japanese'),
                    'ja_KS' => __d('Japanese (Kansai)'),
                    'jv_ID' => __d('Javanese'),
                    'ka_GE' => __d('Georgian'),
                    'kk_KZ' => __d('Kazakh'),
                    'km_KH' => __d('Khmer'),
                    'kn_IN' => __d('Kannada'),
                    'ko_KR' => __d('Korean'),
                    'ku_TR' => __d('Kurdish (Kurmanji)'),
                    'ky_KG' => __d('Kyrgyz'),
                    'la_VA' => __d('Latin'),
                    'lg_UG' => __d('Ganda'),
                    'li_NL' => __d('Limburgish'),
                    'ln_CD' => __d('Lingala'),
                    'lo_LA' => __d('Lao'),
                    'lt_LT' => __d('Lithuanian'),
                    'lv_LV' => __d('Latvian'),
                    'mg_MG' => __d('Malagasy'),
                    'mi_NZ' => __d('Maori'),
                    'mk_MK' => __d('Macedonian'),
                    'ml_IN' => __d('Malayalam'),
                    'mn_MN' => __d('Mongolian'),
                    'mr_IN' => __d('Marathi'),
                    'ms_MY' => __d('Malay'),
                    'mt_MT' => __d('Maltese'),
                    'my_MM' => __d('Burmese'),
                    'nb_NO' => __d('Norwegian (bokmal)'),
                    'nd_ZW' => __d('Ndebele'),
                    'ne_NP' => __d('Nepali'),
                    'nl_BE' => __d('Dutch (Belgie)'),
                    'nl_NL' => __d('Dutch'),
                    'nn_NO' => __d('Norwegian (nynorsk)'),
                    'ny_MW' => __d('Chewa'),
                    'or_IN' => __d('Oriya'),
                    'pa_IN' => __d('Punjabi'),
                    'pl_PL' => __d('Polish'),
                    'ps_AF' => __d('Pashto'),
                    'pt_BR' => __d('Portuguese (Brazil)'),
                    'pt_PT' => __d('Portuguese (Portugal)'),
                    'qu_PE' => __d('Quechua'),
                    'rm_CH' => __d('Romansh'),
                    'ro_RO' => __d('Romanian'),
                    'ru_RU' => __d('Russian'),
                    'rw_RW' => __d('Kinyarwanda'),
                    'sa_IN' => __d('Sanskrit'),
                    'sc_IT' => __d('Sardinian'),
                    'se_NO' => __d('Northern Sami'),
                    'si_LK' => __d('Sinhala'),
                    'sk_SK' => __d('Slovak'),
                    'sl_SI' => __d('Slovenian'),
                    'sn_ZW' => __d('Shona'),
                    'so_SO' => __d('Somali'),
                    'sq_AL' => __d('Albanian'),
                    'sr_RS' => __d('Serbian'),
                    'sv_SE' => __d('Swedish'),
                    'sw_KE' => __d('Swahili'),
                    'sy_SY' => __d('Syriac'),
                    'sz_PL' => __d('Silesian'),
                    'ta_IN' => __d('Tamil'),
                    'te_IN' => __d('Telugu'),
                    'tg_TJ' => __d('Tajik'),
                    'th_TH' => __d('Thai'),
                    'tk_TM' => __d('Turkmen'),
                    'tl_PH' => __d('Filipino'),
                    'tl_ST' => __d('Klingon'),
                    'tr_TR' => __d('Turkish'),
                    'tt_RU' => __d('Tatar'),
                    'tz_MA' => __d('Tamazight'),
                    'uk_UA' => __d('Ukrainian'),
                    'ur_PK' => __d('Urdu'),
                    'uz_UZ' => __d('Uzbek'),
                    'vi_VN' => __d('Vietnamese'),
                    'wo_SN' => __d('Wolof'),
                    'xh_ZA' => __d('Xhosa'),
                    'yi_DE' => __d('Yiddish'),
                    'yo_NG' => __d('Yoruba'),
                    'zh_CN' => __d('Simplified Chinese (China)'),
                    'zh_HK' => __d('Traditional Chinese (Hong Kong)'),
                    'zh_TW' => __d('Traditional Chinese (Taiwan)'),
                    'zu_ZA' => __d('Zulu'),
                    'zz_TR' => __d('Zazaki')
                ),
                'default' => 'en_US',
                'dependency' => array('comments', '==', 'fb')
            ),
            array(
                'id'    => 'fbadmin',
                'type'  => 'text',
                'title' => __d('Admin user'),
                'subtitle'  => __d('Add user or user ID to manage comment'),
                'dependency' => array('comments', '==', 'fb')
            ),
            array(
                'id'      => 'fbscheme',
                'type'    => 'radio',
                'title'   => __d('Color Scheme'),
                'default' => 'light',
                'options' => array(
                    'light' => __d('Light color'),
                    'dark' => __d('Dark color')
                ),
                'dependency' => array('comments', '==', 'fb')
            ),
            array(
                'id' => 'fbnumber',
                'type' => 'text',
                'title' => __d('Number of Posts'),
                'default' => '10',
                'attributes' => array(
                    'style' => 'width:100px',
                    'type' => 'number'
                ),
                'dependency' => array('comments', '==', 'fb')
            ),
            array(
                'type'    => 'subheading',
                'content' => __d('Disqus comments'),
                'dependency' => array('comments', '==', 'dq')
            ),
            array(
                'id'    => 'dqshortname',
                'type'  => 'text',
                'title' => __d('Shortname'),
                'subtitle'  => __d('This is used to uniquely identify your website on Disqus. It cannot be changed'),
                'after' => '<p><a href="https://help.disqus.com/installation/whats-a-shortname" target="_blank">'.__d('more info').'</a></p>',
                'dependency' => array('comments', '==', 'dq')
            ),
            array(
                'type'    => 'content',
                'content' => '<a href="'.admin_url('options-discussion.php').'"><strong>'.__d('Discussion Settings').'</strong></a>',
                'dependency' => array('comments', '==', 'wp')
            )
        )
    )
);
