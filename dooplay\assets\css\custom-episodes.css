/* Simple Episode List Improvements */

/* Better spacing and subtle improvements */
#seasons .se-c {
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

#seasons .se-c:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Season Header - Keep original style with minor improvements */
#seasons .se-c .se-q {
    transition: background-color 0.3s ease;
}

#seasons .se-c .se-q:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

#seasons .se-c .se-q span.se-t {
    border-radius: 6px;
    transition: transform 0.2s ease;
}

#seasons .se-c .se-q:hover span.se-t {
    transform: scale(1.05);
}

#seasons .se-c .se-q span.title {
    transition: color 0.3s ease;
}

/* Episodes Container - Keep original structure with improvements */
#seasons .se-c .se-a ul.episodios li {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin-bottom: 2px;
}

#seasons .se-c .se-a ul.episodios li:hover {
    background-color: rgba(0, 0, 0, 0.02);
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Episode image improvements */
#seasons .se-c .se-a ul.episodios li .imagen {
    border-radius: 6px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

#seasons .se-c .se-a ul.episodios li:hover .imagen {
    transform: scale(1.02);
}

#seasons .se-c .se-a ul.episodios li .imagen img {
    transition: all 0.3s ease;
}

#seasons .se-c .se-a ul.episodios li:hover .imagen img {
    filter: brightness(1.1);
}

/* Episode number styling improvements */
#seasons .se-c .se-a ul.episodios li .numerando {
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 600;
}

#seasons .se-c .se-a ul.episodios li:hover .numerando {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.02);
}

/* Episode title improvements */
#seasons .se-c .se-a ul.episodios li .episodiotitle a {
    transition: color 0.3s ease;
    font-weight: 500;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle a:hover {
    text-decoration: none;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle span.date {
    transition: color 0.3s ease;
    font-size: 12px;
}

/* Responsive improvements */
@media (max-width: 768px) {
    #seasons .se-c .se-a ul.episodios li {
        padding: 12px 15px;
    }

    #seasons .se-c .se-a ul.episodios li .imagen {
        width: 80px;
        margin-top: 2px;
    }

    #seasons .se-c .se-a ul.episodios li .episodiotitle {
        padding-left: 160px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    #seasons .se-c .se-q {
        padding: 12px 15px;
    }

    #seasons .se-c .se-a ul.episodios li .imagen {
        width: 70px;
    }

    #seasons .se-c .se-a ul.episodios li .numerando {
        width: 70px;
        font-size: 12px;
        padding: 6px;
    }

    #seasons .se-c .se-a ul.episodios li .episodiotitle {
        padding-left: 150px;
        font-size: 13px;
    }
}

/* Smooth season toggle animation */
#seasons .se-c .se-a {
    transition: all 0.4s ease-in-out;
}

/* Better focus states for accessibility */
#seasons .se-c .se-q:focus,
#seasons .se-c .se-a ul.episodios li:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition-duration: 0.01ms !important;
    }
}
