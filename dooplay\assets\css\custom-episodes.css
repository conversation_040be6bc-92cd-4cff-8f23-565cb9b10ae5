/* Episode List with Inline Links */

/* Better spacing and subtle improvements */
#seasons .se-c {
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

#seasons .se-c:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Episode with links container */
.episode-with-links {
    display: flex;
    flex-direction: column;
    padding: 15px !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.episode-main-info {
    display: flex;
    align-items: center;
    width: 100%;
}

/* Season Header - Keep original style with minor improvements */
#seasons .se-c .se-q {
    transition: background-color 0.3s ease;
}

#seasons .se-c .se-q:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

#seasons .se-c .se-q span.se-t {
    border-radius: 6px;
    transition: transform 0.2s ease;
}

#seasons .se-c .se-q:hover span.se-t {
    transform: scale(1.05);
}

#seasons .se-c .se-q span.title {
    transition: color 0.3s ease;
}

/* Episodes Container - Keep original structure with improvements */
#seasons .se-c .se-a ul.episodios li {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin-bottom: 2px;
}

#seasons .se-c .se-a ul.episodios li:hover {
    background-color: rgba(0, 0, 0, 0.02);
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Episode image improvements */
#seasons .se-c .se-a ul.episodios li .imagen {
    border-radius: 6px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

#seasons .se-c .se-a ul.episodios li:hover .imagen {
    transform: scale(1.02);
}

#seasons .se-c .se-a ul.episodios li .imagen img {
    transition: all 0.3s ease;
}

#seasons .se-c .se-a ul.episodios li:hover .imagen img {
    filter: brightness(1.1);
}

/* Episode number styling improvements */
#seasons .se-c .se-a ul.episodios li .numerando {
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 600;
}

#seasons .se-c .se-a ul.episodios li:hover .numerando {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.02);
}

/* Episode title improvements */
#seasons .se-c .se-a ul.episodios li .episodiotitle a {
    transition: color 0.3s ease;
    font-weight: 500;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle a:hover {
    text-decoration: none;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle span.date {
    transition: color 0.3s ease;
    font-size: 12px;
}

/* Episode Links Section */
.episode-links-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.links-toggle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    display: inline-block;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.links-toggle:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.episode-links-container {
    display: none;
    margin-top: 10px;
}

.episode-links-container.show {
    display: block;
}

.episode-link-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.episode-link-item:hover {
    background: #e9ecef;
    transform: translateX(3px);
}

.link-info {
    display: flex;
    gap: 10px;
    align-items: center;
}

.link-info span {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.link-info .quality {
    background: #28a745;
    color: white;
}

.link-info .language {
    background: #17a2b8;
    color: white;
}

.link-info .size {
    background: #6c757d;
    color: white;
}

.link-actions {
    display: flex;
    gap: 8px;
}

.download-btn, .play-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.3s ease;
}

.download-btn {
    background: #28a745;
    color: white;
}

.download-btn:hover {
    background: #218838;
    transform: scale(1.1);
}

.play-btn {
    background: #007bff;
    color: white;
}

.play-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

/* Episode Player Modal */
.episode-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.episode-modal .modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.episode-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.episode-modal .modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.episode-modal .close-modal {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    transition: opacity 0.3s ease;
}

.episode-modal .close-modal:hover {
    opacity: 0.7;
}

.episode-modal .modal-body {
    padding: 0;
}

.episode-modal iframe {
    width: 100%;
    height: 450px;
    border: none;
}

/* Responsive modal */
@media (max-width: 768px) {
    .episode-modal .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .episode-modal iframe {
        height: 300px;
    }

    .episode-modal .modal-header {
        padding: 15px 20px;
    }

    .episode-modal .modal-header h3 {
        font-size: 16px;
    }
}

/* Responsive improvements for episode with links */
@media (max-width: 768px) {
    .episode-with-links {
        padding: 12px !important;
    }

    .episode-main-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .episode-main-info .imagen {
        width: 100%;
        max-width: 120px;
    }

    .episode-main-info .numerando {
        align-self: flex-start;
    }

    .episode-main-info .episodiotitle {
        width: 100%;
        padding-left: 0;
    }

    .episode-links-section {
        margin-top: 10px;
        padding-top: 10px;
    }

    .episode-link-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .link-actions {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .episode-with-links {
        padding: 10px !important;
    }

    .links-toggle {
        font-size: 11px;
        padding: 6px 12px;
    }

    .link-info {
        flex-wrap: wrap;
        gap: 5px;
    }

    .link-info span {
        font-size: 10px;
        padding: 3px 6px;
    }

    .download-btn, .play-btn {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }
}

/* Smooth season toggle animation */
#seasons .se-c .se-a {
    transition: all 0.4s ease-in-out;
}

/* Better focus states for accessibility */
#seasons .se-c .se-q:focus,
#seasons .se-c .se-a ul.episodios li:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition-duration: 0.01ms !important;
    }
}
