/* Enhanced Episode List Design */

/* Season Container */
#seasons .se-c {
    margin-bottom: 30px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

#seasons .se-c:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Season Header */
#seasons .se-c .se-q {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: none;
    padding: 20px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#seasons .se-c .se-q:hover {
    background: rgba(255, 255, 255, 0.15);
}

#seasons .se-c .se-q::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

#seasons .se-c .se-q:hover::before {
    left: 100%;
}

#seasons .se-c .se-q span.se-t {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 700;
    position: absolute;
    left: 25px;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#seasons .se-c .se-q span.title {
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    margin-left: 90px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Episodes Container */
#seasons .se-c .se-a {
    background: #fff;
    padding: 0;
}

#seasons .se-c .se-a ul.episodios {
    padding: 0;
    margin: 0;
    list-style: none;
}

/* Enhanced Episode Cards */
#seasons .se-c .se-a ul.episodios li.episode-card {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    background: #fff;
}

#seasons .se-c .se-a ul.episodios li.episode-card:last-child {
    border-bottom: none;
}

#seasons .se-c .se-a ul.episodios li.episode-card:hover {
    background: #f8f9ff;
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Episode Thumbnail */
.episode-thumbnail {
    position: relative;
    width: 120px;
    height: 68px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;
}

.episode-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.episode-thumbnail:hover img {
    transform: scale(1.05);
}

/* Play Overlay */
.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.episode-thumbnail:hover .play-overlay {
    opacity: 1;
}

.play-overlay i {
    color: #fff;
    font-size: 24px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Episode Info */
.episode-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Episode Number */
.episode-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    min-width: 80px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.episode-number.special {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3);
}

/* Episode Details */
.episode-details {
    flex: 1;
}

.episode-title {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.episode-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.episode-title a:hover {
    color: #667eea;
}

.episode-date {
    color: #888;
    font-size: 13px;
    font-weight: 400;
}

/* Responsive Design */
@media (max-width: 768px) {
    #seasons .se-c .se-a ul.episodios li.episode-card {
        flex-direction: column;
        align-items: flex-start;
        padding: 15px;
    }
    
    .episode-thumbnail {
        width: 100%;
        height: 180px;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .episode-info {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .episode-number {
        align-self: flex-start;
    }
}

@media (max-width: 480px) {
    #seasons .se-c .se-q {
        padding: 15px 20px;
    }
    
    #seasons .se-c .se-q span.se-t {
        width: 50px;
        height: 50px;
        font-size: 20px;
        left: 20px;
    }
    
    #seasons .se-c .se-q span.title {
        margin-left: 80px;
        font-size: 18px;
    }
    
    .episode-thumbnail {
        height: 160px;
    }
}

/* Animation for season toggle */
#seasons .se-c .se-a {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}

#seasons .se-c .se-a[style*="display:block"] {
    max-height: 2000px;
}

/* Loading state */
.episode-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.episode-card.loading .episode-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Search functionality */
.episode-search {
    position: relative;
    margin: 20px 0;
    max-width: 400px;
}

.episode-search-input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.episode-search-input:focus {
    outline: none;
    border-color: #667eea;
}

.episode-search i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

/* Watched indicator */
.episode-card.watched {
    opacity: 0.7;
}

.watched-indicator {
    background: #4caf50;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: auto;
}

/* Keyboard focus */
.episode-card.keyboard-focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Scroll animations */
.episode-card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.episode-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    #seasons .se-c .se-a {
        background: #1a1a1a;
    }

    #seasons .se-c .se-a ul.episodios li.episode-card {
        background: #1a1a1a;
        border-bottom-color: #333;
    }

    #seasons .se-c .se-a ul.episodios li.episode-card:hover {
        background: #2a2a2a;
    }

    .episode-title a {
        color: #fff;
    }

    .episode-date {
        color: #aaa;
    }

    .episode-search-input {
        background: #2a2a2a;
        border-color: #444;
        color: #fff;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    #seasons .se-c .se-q span.se-t {
        background: #000;
        color: #fff;
    }

    .episode-number {
        background: #000 !important;
        color: #fff !important;
    }

    .play-overlay {
        background: rgba(0, 0, 0, 0.9);
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .episode-card {
        opacity: 1;
        transform: none;
    }
}
