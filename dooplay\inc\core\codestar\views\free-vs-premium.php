<?php if ( ! defined( 'ABSPATH' ) ) { die; } // Cannot access directly. ?>

<table class="csf--table-compare widefat fixed striped">
  <thead>
    <tr>
      <td><strong>Features</strong></td>
      <td><strong>Free Version</strong></td>
      <td><strong>Premium Version</strong></td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Admin Option Framework</td>
      <td><i class="fas fa-check-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Customize Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Metabox Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Nav Menu Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Taxonomy Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <th>Profile Option Framework</th>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Widget Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Comment Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Shortcode Option Framework</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>All Option Fields</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Developer Packages</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Unminfy Library</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>New Requests</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Autoremove Advertisements</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Life-time access/updates</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
    <tr>
      <td>Support Forum</td>
      <td><i class="fas fa-times-circle"></i></td>
      <td><i class="fas fa-check-circle"></i></td>
    </tr>
  </tbody>
  <tfoot>
    <tr>
      <td></td>
      <td><a href="https://github.com/Codestar/codestar-framework/" class="button" target="_blank" rel="nofollow">Download Free Version</a></td>
      <td><a href="http://codestarframework.com/" class="button button-primary" target="_blank" rel="nofollow">Upgrade Premium Version</a></td>
    </tr>
  </tfoot>
</table>
