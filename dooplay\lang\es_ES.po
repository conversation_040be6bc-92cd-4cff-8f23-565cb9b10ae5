msgid ""
msgstr ""
"Project-Id-Version: DooPlay\n"
"POT-Creation-Date: 2021-06-21 18:21-0500\n"
"PO-Revision-Date: 2021-06-21 18:57-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: style.css\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2;_d;__d\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: 404.php:15
msgid "Page not found"
msgstr "Pagina no se encuentra"

#: 404.php:18
msgid "ERROR"
msgstr "ERROR"

#: 404.php:19 pages/letter.php:7 pages/search.php:61
msgid "Suggestions"
msgstr "Sugerencias"

#: 404.php:21
msgid "Verify that the link is correct."
msgstr "Compruebe que el vínculo es correcto."

#: 404.php:22
msgid "Use the search box on the page."
msgstr "Utilice el cuadro de búsqueda de la página."

#: 404.php:23
msgid "Contact support page."
msgstr "Pónganse en contacto con el soporte de la pagina."

#: archive-episodes.php:17 inc/core/dbmvs/classes/epsemboxes.php:32
#: inc/core/dbmvs/classes/postypes.php:154
#: inc/core/dbmvs/classes/postypes.php:155
#: inc/core/dbmvs/classes/postypes.php:156
#: inc/core/dbmvs/classes/postypes.php:157
#: inc/core/dbmvs/classes/postypes.php:158
#: inc/core/dbmvs/classes/postypes.php:166
#: inc/core/dbmvs/tpl/form_setting_titles.php:35
#: inc/csf/options.module_episodes.php:16 inc/includes/slugs.php:29
#: inc/parts/single/series.php:84 inc/parts/single/series.php:158
#: inc/widgets/content_widget.php:79
msgid "Episodes"
msgstr "Episodios"

#: archive-episodes.php:18 archive-movies.php:21 archive-seasons.php:18
#: archive-tvshows.php:21 taxonomy.php:24
msgid "Recently added"
msgstr "Añadido recientemente"

#: archive-movies.php:18 archive-requests.php:22 archive.php:17
#: inc/core/dbmvs/classes/postypes.php:31
#: inc/core/dbmvs/classes/postypes.php:33
#: inc/core/dbmvs/classes/postypes.php:34
#: inc/core/dbmvs/classes/postypes.php:35
#: inc/core/dbmvs/classes/postypes.php:43 inc/core/dbmvs/tpl/admin_app.php:8
#: inc/core/dbmvs/tpl/form_setting_titles.php:8
#: inc/core/dbmvs/tpl/form_setting_updater.php:17
#: inc/csf/options.customize.php:213 inc/csf/options.module_movies.php:8
#: inc/csf/options.module_movies.php:16 inc/includes/slugs.php:26
#: inc/parts/modules/top-imdb-page.php:86
#: inc/parts/modules/top-imdb-page.php:108 inc/parts/single/peliculas.php:156
#: inc/widgets/content_widget.php:77 inc/widgets/content_widget_home.php:123
#: pages/rating.php:27 pages/trending.php:28
msgid "Movies"
msgstr "Películas"

#: archive-requests.php:18
msgid "Requests List"
msgstr "Lista de solicitudes"

#: archive-requests.php:19
msgid "+ Add new"
msgstr "+ Agregar nuevo"

#: archive-requests.php:20 pages/sections/account.php:95
msgid "Go back"
msgstr "Regresar"

#: archive-requests.php:23 inc/core/dbmvs/tpl/form_setting_titles.php:17
#: inc/includes/slugs.php:27 inc/parts/modules/top-imdb-page.php:95
#: inc/parts/modules/top-imdb-page.php:121
msgid "TVShows"
msgstr "Series"

#: archive-requests.php:26 inc/widgets/content_widget_home.php:122
msgid "All"
msgstr "Todo"

#: archive-requests.php:33
msgid "Search a title.."
msgstr "Buscar un titulo.."

#: archive-requests.php:43
msgid "Find a title you want to suggest"
msgstr "Encuentra un título que quieras sugerir"

#: archive-requests.php:56
msgid "Soon"
msgstr "Próximamente"

#: archive-seasons.php:17 inc/core/dbmvs/classes/epsemboxes.php:31
#: inc/core/dbmvs/classes/postypes.php:113
#: inc/core/dbmvs/classes/postypes.php:114
#: inc/core/dbmvs/classes/postypes.php:115
#: inc/core/dbmvs/classes/postypes.php:116
#: inc/core/dbmvs/classes/postypes.php:117
#: inc/core/dbmvs/classes/postypes.php:125
#: inc/core/dbmvs/classes/tables.php:128
#: inc/core/dbmvs/tpl/form_setting_titles.php:26
#: inc/csf/options.module_seasons.php:16 inc/includes/slugs.php:28
#: inc/parts/single/series.php:153
msgid "Seasons"
msgstr "Temporadas"

#: archive-tvshows.php:18 inc/core/dbmvs/classes/postypes.php:72
#: inc/core/dbmvs/classes/postypes.php:73
#: inc/core/dbmvs/classes/postypes.php:74
#: inc/core/dbmvs/classes/postypes.php:75
#: inc/core/dbmvs/classes/postypes.php:76
#: inc/core/dbmvs/tpl/form_setting_updater.php:18
#: inc/csf/options.customize.php:215 inc/csf/options.module_tvshows.php:8
#: inc/csf/options.module_tvshows.php:16 inc/parts/single/series.php:179
#: inc/widgets/content_widget.php:78 inc/widgets/content_widget_home.php:124
msgid "TV Shows"
msgstr "Series de TV"

#: author.php:35 pages/sections/account.php:32
msgid "You haven't written anything about yourself"
msgstr "No has escrito nada sobre ti"

#: author.php:40 author.php:60 pages/sections/account.php:37
#: pages/sections/account.php:57
msgid "Favorites"
msgstr "Favoritos"

#: author.php:44 author.php:86 inc/core/dbmvs/classes/tables.php:70
#: inc/core/dbmvs/classes/tables.php:129 inc/core/dbmvs/classes/tables.php:177
#: inc/core/dbmvs/classes/tables.php:215 inc/doo_scripts.php:198
#: inc/includes/metabox.php:37 inc/parts/single/post.php:26
#: pages/sections/account.php:41
msgid "Views"
msgstr "Vistas"

#: author.php:48 author.php:61 inc/doo_links.php:98 inc/doo_links.php:99
#: inc/doo_links.php:101 inc/doo_links.php:102 inc/doo_links.php:111
#: inc/doo_links.php:379 inc/doo_links.php:380 inc/doo_links.php:381
#: inc/includes/slugs.php:30 inc/parts/single/links.php:4
#: inc/parts/single/peliculas.php:95 pages/sections/account.php:45
#: pages/sections/account.php:59
msgid "Links"
msgstr "Enlaces"

#: author.php:52 inc/csf/options.comments.php:8 pages/sections/account.php:49
msgid "Comments"
msgstr "Comentarios"

#: author.php:63
msgid "Edit Profile"
msgstr "Editar perfil"

#: author.php:74 author.php:97 inc/core/dbmvs/tpl/dashboard_widget.php:24
#: inc/core/dbmvs/tpl/dashboard_widget.php:42 pages/sections/account.php:72
#: pages/sections/account.php:84 pages/sections/account.php:118
#: pages/sections/account.php:140
msgid "Load more"
msgstr "Cargar mas"

#: author.php:83 inc/core/dbmvs/classes/requests.php:118
#: inc/parts/links_editor.php:44 inc/parts/links_editor_type.php:13
#: inc/parts/player_editor.php:7 inc/parts/single/links.php:33
msgid "Type"
msgstr "Tipo"

#: author.php:84 inc/doo_links.php:709 inc/parts/links_editor.php:45
#: pages/sections/account.php:103 pages/sections/account.php:127
msgid "Server"
msgstr "Servidor"

#: author.php:85 inc/parts/player_editor.php:6
#: inc/widgets/content_widget_home.php:116 pages/sections/account.php:104
#: pages/sections/account.php:128
msgid "Title"
msgstr "Título"

#: author.php:87 inc/core/dbmvs/classes/taxonomies.php:57
#: inc/csf/options.links_module.php:64 inc/doo_links.php:564
#: inc/doo_links.php:711 inc/includes/slugs.php:38
#: inc/parts/links_editor.php:47 inc/parts/links_editor_type.php:49
#: inc/parts/single/links.php:35 pages/sections/account.php:107
msgid "Quality"
msgstr "Calidad"

#: author.php:88 inc/csf/options.links_module.php:65 inc/doo_links.php:565
#: inc/doo_links.php:710 inc/parts/links_editor.php:46
#: inc/parts/links_editor_type.php:39 inc/parts/single/links.php:36
#: pages/sections/account.php:106
msgid "Language"
msgstr "Idioma"

#: author.php:89 inc/csf/options.links_module.php:68 inc/doo_links.php:568
#: inc/parts/links_editor.php:51 pages/sections/account.php:108
msgid "Added"
msgstr "Añadido"

#: category.php:16
#, php-format
msgid "Category Archives: %s"
msgstr "Archivos de categoría: %s"

#: category.php:38 inc/parts/modules/blog.php:48 pages/blog.php:46
msgid "No posts to show"
msgstr "No hay entradas para mostrar"

#: comments.php:21
#, php-format
msgid "(1) comment"
msgid_plural "(%1$s) comments"
msgstr[0] "(1) comentario"
msgstr[1] "(%1$s) comentarios"

#: comments.php:32
msgid "&larr; Older Comments"
msgstr "&larr; Comentarios antiguos"

#: comments.php:33
msgid "Newer Comments &rarr;"
msgstr "Nuevos comentarios &rarr;"

#: comments.php:39
msgid "Comments are closed."
msgstr "Los comentarios están cerrados."

#: footer.php:23
#, php-format
msgid "%s %s by %s. All Rights Reserved. Powered by %s"
msgstr "%s %s por %s. Todos los derechos reservados. Potenciado por %s"

#: footer.php:68
msgid "&lsaquo;"
msgstr "&lsaquo;"

#: footer.php:69
msgid "&rsaquo;"
msgstr "&rsaquo;"

#: footer.php:70
msgid "&times;"
msgstr "&times;"

#: header.php:48
msgid "Generating data.."
msgstr "Generando datos.."

#: header.php:49
msgid "Please wait, not close this page to complete the upload"
msgstr "Por favor, espere, no cierre esta página para completar la carga"

#: header.php:66 header.php:104
#: inc/core/codestar/classes/admin-options.class.php:569
#: inc/core/codestar/fields/icon/icon.php:57
#: inc/core/codestar/fields/map/map.php:23 pages/search.php:7 searchform.php:2
msgid "Search..."
msgstr "Buscar..."

#: header.php:78 header.php:120
msgid "Sign out"
msgstr "Cerrar sesión"

#: header.php:116
msgid "My account"
msgstr "Mi cuenta"

#: header.php:125
msgid "Login"
msgstr "Ingresar"

#: header.php:126
msgid "Sign Up"
msgstr "Registrarse"

#: inc/core/codestar/classes/admin-options.class.php:226
msgid "Error while saving the changes."
msgstr "Error mientras se guardaban los cambios."

#: inc/core/codestar/classes/admin-options.class.php:286
msgid "Settings successfully imported."
msgstr "Ajustes importados con éxito."

#: inc/core/codestar/classes/admin-options.class.php:298
#: inc/core/codestar/classes/admin-options.class.php:314
msgid "Default settings restored."
msgstr "Se restauraron las opciones por defecto."

#: inc/core/codestar/classes/admin-options.class.php:385
#: inc/parts/admin/ads_tool.php:4
msgid "Settings saved."
msgstr "Ajustes guardados."

#: inc/core/codestar/classes/admin-options.class.php:565
msgid "You have unsaved changes, save your changes!"
msgstr "¡Tienes cambios sin guardar, guarda tus cambios!"

#: inc/core/codestar/classes/admin-options.class.php:567
msgid "show all settings"
msgstr "mostrar todos los ajustes"

#: inc/core/codestar/classes/admin-options.class.php:572
#: inc/core/codestar/classes/admin-options.class.php:695
#: inc/core/dbmvs/classes/enqueues.php:61
msgid "Save"
msgstr "Guardar"

#: inc/core/codestar/classes/admin-options.class.php:572
#: inc/core/codestar/classes/admin-options.class.php:695
msgid "Saving..."
msgstr "Guardando…"

#: inc/core/codestar/classes/admin-options.class.php:573
#: inc/core/codestar/classes/admin-options.class.php:696
msgid "Reset Section"
msgstr "Restablecer sección"

#: inc/core/codestar/classes/admin-options.class.php:573
#: inc/core/codestar/classes/admin-options.class.php:696
msgid "Are you sure to reset this section options?"
msgstr "¿Está seguro de restablecer las opciones de esta sección?"

#: inc/core/codestar/classes/admin-options.class.php:574
#: inc/core/codestar/classes/admin-options.class.php:697
msgid "Reset All"
msgstr "Restablecer todo"

#: inc/core/codestar/classes/admin-options.class.php:574
#: inc/core/codestar/classes/admin-options.class.php:697
#: inc/core/codestar/classes/comment-options.class.php:215
#: inc/core/codestar/classes/metabox-options.class.php:293
#: inc/core/codestar/fields/backup/backup.php:31
msgid "Reset"
msgstr "Reiniciar"

#: inc/core/codestar/classes/admin-options.class.php:574
#: inc/core/codestar/classes/admin-options.class.php:697
msgid "Are you sure you want to reset all settings to default values?"
msgstr ""
"¿Estás seguro de que quieres restablecer todos los ajustes a los valores por "
"defecto?"

#: inc/core/codestar/classes/admin-options.class.php:672
#: inc/core/codestar/classes/comment-options.class.php:198
#: inc/core/codestar/classes/metabox-options.class.php:276
#: inc/core/codestar/fields/button_set/button_set.php:56
#: inc/core/codestar/fields/checkbox/checkbox.php:76
#: inc/core/codestar/fields/radio/radio.php:75
#: inc/core/codestar/fields/select/select.php:113
#: inc/core/codestar/functions/actions.php:41
msgid "No data available."
msgstr "Datos no disponibles."

#: inc/core/codestar/classes/comment-options.class.php:216
#: inc/core/codestar/classes/metabox-options.class.php:294
msgid "update post"
msgstr "actualizar la publicación"

#: inc/core/codestar/classes/comment-options.class.php:216
#: inc/core/codestar/classes/metabox-options.class.php:294
#: inc/core/dbmvs/tpl/import_seaepis.php:16 inc/doo_links.php:275
#: inc/parts/links_editor.php:33
msgid "Cancel"
msgstr "Cancelar"

#: inc/core/codestar/classes/setup.class.php:592
msgid "Are you sure?"
msgstr "¿Estás seguro?"

#: inc/core/codestar/classes/setup.class.php:593
#, php-format
msgid "Please enter %s or more characters"
msgstr "Por favor, introduzca %s o más caracteres"

#: inc/core/codestar/classes/setup.class.php:594
msgid "Searching..."
msgstr "Buscando..."

#: inc/core/codestar/classes/setup.class.php:595
#: inc/includes/rating/init.php:598
msgid "No results found."
msgstr "No se encontraron resultados."

#: inc/core/codestar/classes/setup.class.php:696
msgid "Oops! Not allowed."
msgstr "¡Vaya! No está permitido."

#: inc/core/codestar/classes/setup.class.php:768
#: inc/core/codestar/classes/setup.class.php:772
msgid "Field not found!"
msgstr "¡Campo no encontrado!"

#: inc/core/codestar/classes/shortcode-options.class.php:251
#: inc/core/codestar/fields/group/group.php:23
msgid "Add New"
msgstr "Añadir nuevo"

#: inc/core/codestar/classes/shortcode-options.class.php:288
#: inc/core/codestar/functions/actions.php:16
#: inc/core/codestar/functions/actions.php:68
#: inc/core/codestar/functions/actions.php:106
#: inc/core/codestar/functions/actions.php:141
#: inc/core/codestar/functions/actions.php:170
msgid "Error: Invalid nonce verification."
msgstr "Error: Verificación de nones no válida."

#: inc/core/codestar/fields/background/background.php:36
#: inc/core/codestar/fields/media/media.php:57
msgid "Not selected"
msgstr "No seleccionado"

#: inc/core/codestar/fields/background/background.php:72
#: inc/core/codestar/fields/date/date.php:31
#: inc/core/dbmvs/classes/dashboard.php:194
#: inc/core/dbmvs/classes/inboxes.php:128
msgid "From"
msgstr "De"

#: inc/core/codestar/fields/background/background.php:90
#: inc/core/codestar/fields/date/date.php:32
msgid "To"
msgstr "A"

#: inc/core/codestar/fields/background/background.php:108
msgid "Direction"
msgstr "Dirección"

#: inc/core/codestar/fields/background/background.php:114
msgid "Gradient Direction"
msgstr "Dirección del gradiente"

#: inc/core/codestar/fields/background/background.php:115
msgid "&#8659; top to bottom"
msgstr "&#8659; de arriba hacia abajo"

#: inc/core/codestar/fields/background/background.php:116
msgid "&#8658; left to right"
msgstr "&#8658; de izquierda a derecha"

#: inc/core/codestar/fields/background/background.php:117
msgid "&#8664; corner top to right"
msgstr "&#8664; esquina de arriba a derecha"

#: inc/core/codestar/fields/background/background.php:118
msgid "&#8665; corner top to left"
msgstr "&#8665; esquina de arriba a izquierda"

#: inc/core/codestar/fields/background/background.php:161
msgid "Background Position"
msgstr "Posición de fondo"

#: inc/core/codestar/fields/background/background.php:162
msgid "Left Top"
msgstr "Izquierda superior"

#: inc/core/codestar/fields/background/background.php:163
msgid "Left Center"
msgstr "Izquierda centro"

#: inc/core/codestar/fields/background/background.php:164
msgid "Left Bottom"
msgstr "Izquierda abajo"

#: inc/core/codestar/fields/background/background.php:165
msgid "Center Top"
msgstr "Centro Arriba"

#: inc/core/codestar/fields/background/background.php:166
msgid "Center Center"
msgstr "Centro Centro"

#: inc/core/codestar/fields/background/background.php:167
msgid "Center Bottom"
msgstr "Centro Abajo"

#: inc/core/codestar/fields/background/background.php:168
msgid "Right Top"
msgstr "Derecha superior"

#: inc/core/codestar/fields/background/background.php:169
msgid "Right Center"
msgstr "Derecha centro"

#: inc/core/codestar/fields/background/background.php:170
msgid "Right Bottom"
msgstr "Derecha abajo"

#: inc/core/codestar/fields/background/background.php:184
msgid "Background Repeat"
msgstr "Repetir fondo"

#: inc/core/codestar/fields/background/background.php:185
msgid "Repeat"
msgstr "Repetir"

#: inc/core/codestar/fields/background/background.php:186
msgid "No Repeat"
msgstr "No repetir"

#: inc/core/codestar/fields/background/background.php:187
msgid "Repeat Horizontally"
msgstr "Repetir Horizontalmente"

#: inc/core/codestar/fields/background/background.php:188
msgid "Repeat Vertically"
msgstr "Repetir Verticalmente"

#: inc/core/codestar/fields/background/background.php:202
msgid "Background Attachment"
msgstr "Archivo adjunto de fondo"

#: inc/core/codestar/fields/background/background.php:203
msgid "Scroll"
msgstr "Scroll"

#: inc/core/codestar/fields/background/background.php:204
msgid "Fixed"
msgstr "Fixed"

#: inc/core/codestar/fields/background/background.php:218
msgid "Background Size"
msgstr "Tamaño de fondo"

#: inc/core/codestar/fields/background/background.php:219
msgid "Cover"
msgstr "Cover"

#: inc/core/codestar/fields/background/background.php:220
msgid "Contain"
msgstr "Contenido"

#: inc/core/codestar/fields/background/background.php:221
msgid "Auto"
msgstr "Auto"

#: inc/core/codestar/fields/background/background.php:235
msgid "Background Origin"
msgstr "Origen del Fondo"

#: inc/core/codestar/fields/background/background.php:236
#: inc/core/codestar/fields/background/background.php:254
msgid "Padding Box"
msgstr "Padding Box"

#: inc/core/codestar/fields/background/background.php:237
#: inc/core/codestar/fields/background/background.php:253
msgid "Border Box"
msgstr "Border Box"

#: inc/core/codestar/fields/background/background.php:238
#: inc/core/codestar/fields/background/background.php:255
msgid "Content Box"
msgstr "Content Box"

#: inc/core/codestar/fields/background/background.php:252
msgid "Background Clip"
msgstr "Recorte del fondo"

#: inc/core/codestar/fields/background/background.php:269
msgid "Background Blend Mode"
msgstr "Background Blend Mode"

#: inc/core/codestar/fields/background/background.php:270
#: inc/core/codestar/fields/link_color/link_color.php:36
#: inc/core/codestar/fields/typography/typography.php:186
msgid "Normal"
msgstr "Normal"

#: inc/core/codestar/fields/background/background.php:271
msgid "Multiply"
msgstr "Multiplicar"

#: inc/core/codestar/fields/background/background.php:272
msgid "Screen"
msgstr "Pantalla"

#: inc/core/codestar/fields/background/background.php:273
msgid "Overlay"
msgstr "Cubrir"

#: inc/core/codestar/fields/background/background.php:274
msgid "Darken"
msgstr "Oscurecer"

#: inc/core/codestar/fields/background/background.php:275
msgid "Lighten"
msgstr "Aclarar"

#: inc/core/codestar/fields/background/background.php:276
msgid "Color Dodge"
msgstr "Sobreexposición del color"

#: inc/core/codestar/fields/background/background.php:277
msgid "Saturation"
msgstr "Saturación"

#: inc/core/codestar/fields/background/background.php:278
msgid "Color"
msgstr "Color"

#: inc/core/codestar/fields/background/background.php:279
msgid "Luminosity"
msgstr "Luminosidad"

#: inc/core/codestar/fields/backup/backup.php:26
#: inc/core/dbmvs/classes/enqueues.php:60
#: inc/core/dbmvs/tpl/import_movies.php:7
#: inc/core/dbmvs/tpl/import_seaepis.php:15
#: inc/core/dbmvs/tpl/import_tvshows.php:7
msgid "Import"
msgstr "Importar"

#: inc/core/codestar/fields/backup/backup.php:29
msgid "Export & Download"
msgstr "Exportar & Descargar"

#: inc/core/codestar/fields/border/border.php:25
#: inc/core/codestar/fields/spacing/spacing.php:25
msgid "top"
msgstr "arriba"

#: inc/core/codestar/fields/border/border.php:26
#: inc/core/codestar/fields/spacing/spacing.php:26
msgid "right"
msgstr "derecha"

#: inc/core/codestar/fields/border/border.php:27
#: inc/core/codestar/fields/spacing/spacing.php:27
msgid "bottom"
msgstr "abajo"

#: inc/core/codestar/fields/border/border.php:28
#: inc/core/codestar/fields/spacing/spacing.php:28
msgid "left"
msgstr "izquierda"

#: inc/core/codestar/fields/border/border.php:29
#: inc/core/codestar/fields/spacing/spacing.php:29
msgid "all"
msgstr "todo"

#: inc/core/codestar/fields/border/border.php:51
#: inc/core/codestar/fields/typography/typography.php:214
msgid "Solid"
msgstr "Sólido"

#: inc/core/codestar/fields/border/border.php:52
#: inc/core/codestar/fields/typography/typography.php:217
msgid "Dashed"
msgstr "Discontinua"

#: inc/core/codestar/fields/border/border.php:53
#: inc/core/codestar/fields/typography/typography.php:216
msgid "Dotted"
msgstr "Punteado"

#: inc/core/codestar/fields/border/border.php:54
#: inc/core/codestar/fields/typography/typography.php:215
msgid "Double"
msgstr "Doble"

#: inc/core/codestar/fields/border/border.php:55
msgid "Inset"
msgstr "Recuadro"

#: inc/core/codestar/fields/border/border.php:56
msgid "Outset"
msgstr "Exterior"

#: inc/core/codestar/fields/border/border.php:57
msgid "Groove"
msgstr "Ranura"

#: inc/core/codestar/fields/border/border.php:58
msgid "ridge"
msgstr "rugoso"

#: inc/core/codestar/fields/border/border.php:59
#: inc/core/codestar/fields/typography/typography.php:199
#: inc/core/codestar/fields/typography/typography.php:213
#: inc/csf/options.comments.php:21
msgid "None"
msgstr "Ninguno"

#: inc/core/codestar/fields/dimensions/dimensions.php:22
msgid "width"
msgstr "ancho"

#: inc/core/codestar/fields/dimensions/dimensions.php:23
msgid "height"
msgstr "altura"

#: inc/core/codestar/fields/gallery/gallery.php:20
msgid "Add Gallery"
msgstr "Agregar galeria"

#: inc/core/codestar/fields/gallery/gallery.php:21
msgid "Edit Gallery"
msgstr "Editar galería"

#: inc/core/codestar/fields/gallery/gallery.php:22
msgid "Clear"
msgstr "Limpiar"

#: inc/core/codestar/fields/group/group.php:35
#: inc/core/codestar/fields/repeater/repeater.php:27
msgid "Error: Field ID conflict."
msgstr "Error: Conflicto de ID de campo."

#: inc/core/codestar/fields/group/group.php:46
#: inc/core/codestar/fields/group/group.php:87
#: inc/core/codestar/fields/repeater/repeater.php:48
#: inc/core/codestar/fields/repeater/repeater.php:76
msgid "Are you sure to delete this item?"
msgstr "¿Está seguro de eliminar este elemento?"

#: inc/core/codestar/fields/group/group.php:121
#: inc/core/codestar/fields/repeater/repeater.php:89
msgid "You cannot add more."
msgstr "No se puede agregar más."

#: inc/core/codestar/fields/group/group.php:122
#: inc/core/codestar/fields/repeater/repeater.php:90
msgid "You cannot remove more."
msgstr "No se puede quitar más."

#: inc/core/codestar/fields/icon/icon.php:20
#: inc/core/codestar/fields/icon/icon.php:53
msgid "Add Icon"
msgstr "Agregar icono"

#: inc/core/codestar/fields/icon/icon.php:21
msgid "Remove Icon"
msgstr "Eliminar icono"

#: inc/core/codestar/fields/link/link.php:20
msgid "Add Link"
msgstr "Añadir enlace"

#: inc/core/codestar/fields/link/link.php:21
#: inc/parts/links_editor_single.php:9
msgid "Edit Link"
msgstr "Editar enlace"

#: inc/core/codestar/fields/link/link.php:22
msgid "Remove Link"
msgstr "Eliminar enlace"

#: inc/core/codestar/fields/link_color/link_color.php:37
msgid "Hover"
msgstr "Hover"

#: inc/core/codestar/fields/link_color/link_color.php:38
msgid "Active"
msgstr "Activo"

#: inc/core/codestar/fields/link_color/link_color.php:39
msgid "Visited"
msgstr "Visitado"

#: inc/core/codestar/fields/link_color/link_color.php:40
msgid "Focus"
msgstr "Enfoque"

#: inc/core/codestar/fields/map/map.php:24
msgid "Latitude"
msgstr "Latitud"

#: inc/core/codestar/fields/map/map.php:25
msgid "Longitude"
msgstr "Longitud"

#: inc/core/codestar/fields/media/media.php:23
#: inc/core/codestar/fields/upload/upload.php:21 inc/doo_metafields.php:136
#: inc/doo_metafields.php:254
msgid "Upload"
msgstr "Cargar"

#: inc/core/codestar/fields/media/media.php:24
#: inc/core/codestar/fields/upload/upload.php:22
#: inc/core/dbmvs/classes/tables.php:58 inc/core/dbmvs/classes/tables.php:116
#: inc/doo_collection.php:178 inc/doo_scripts.php:199 inc/doo_scripts.php:255
#: inc/parts/player_editor.php:35 inc/parts/player_editor.php:58
#: inc/parts/player_editor.php:79 inc/parts/simple_item_favorites.php:6
#: inc/parts/simple_item_views.php:6
msgid "Remove"
msgstr "Eliminar"

#: inc/core/codestar/fields/sorter/sorter.php:21
msgid "Enabled"
msgstr "Habilitado"

#: inc/core/codestar/fields/sorter/sorter.php:22
msgid "Disabled"
msgstr "Desactivado"

#: inc/core/codestar/fields/switcher/switcher.php:20
msgid "On"
msgstr "On"

#: inc/core/codestar/fields/switcher/switcher.php:21
msgid "Off"
msgstr "Off"

#: inc/core/codestar/fields/typography/typography.php:96
msgid "Font Family"
msgstr "Font family"

#: inc/core/codestar/fields/typography/typography.php:97
msgid "Select a font"
msgstr "Seleccione una fuente"

#: inc/core/codestar/fields/typography/typography.php:105
msgid "Backup Font Family"
msgstr "Respaldar Font Family"

#: inc/core/codestar/fields/typography/typography.php:119
#: inc/core/codestar/fields/typography/typography.php:132
#: inc/core/codestar/fields/typography/typography.php:145
#: inc/core/codestar/fields/typography/typography.php:160
#: inc/core/codestar/fields/typography/typography.php:176
#: inc/core/codestar/fields/typography/typography.php:189
#: inc/core/codestar/fields/typography/typography.php:203
#: inc/core/codestar/fields/typography/typography.php:221
msgid "Default"
msgstr "Por defecto"

#: inc/core/codestar/fields/typography/typography.php:130
msgid "Font Style"
msgstr "Estilo de Fuente"

#: inc/core/codestar/fields/typography/typography.php:144
#: inc/core/codestar/fields/typography/typography.php:145
msgid "Load Extra Styles"
msgstr "Cargar estilos adicionales"

#: inc/core/codestar/fields/typography/typography.php:158
msgid "Subset"
msgstr "Subconjunto"

#: inc/core/codestar/fields/typography/typography.php:168
msgid "Text Align"
msgstr "Alineación del texto"

#: inc/core/codestar/fields/typography/typography.php:170
msgid "Inherit"
msgstr "Heredar"

#: inc/core/codestar/fields/typography/typography.php:171
#: inc/csf/options.customize.php:168 inc/csf/options.customize.php:178
#: inc/csf/options.customize.php:188
msgid "Left"
msgstr "Izquierda"

#: inc/core/codestar/fields/typography/typography.php:172
msgid "Center"
msgstr "Centro"

#: inc/core/codestar/fields/typography/typography.php:173
#: inc/csf/options.customize.php:167 inc/csf/options.customize.php:177
#: inc/csf/options.customize.php:187
msgid "Right"
msgstr "Derecha"

#: inc/core/codestar/fields/typography/typography.php:174
msgid "Justify"
msgstr "Justificar"

#: inc/core/codestar/fields/typography/typography.php:175
msgid "Initial"
msgstr "Inicial"

#: inc/core/codestar/fields/typography/typography.php:184
msgid "Font Variant"
msgstr "Variante de fuente"

#: inc/core/codestar/fields/typography/typography.php:187
msgid "Small Caps"
msgstr "Mayúsculas pequeñas"

#: inc/core/codestar/fields/typography/typography.php:188
msgid "All Small Caps"
msgstr "Todas las mayusculas pequeñas"

#: inc/core/codestar/fields/typography/typography.php:197
msgid "Text Transform"
msgstr "Transformación de texto"

#: inc/core/codestar/fields/typography/typography.php:200
msgid "Capitalize"
msgstr "Capitalizar"

#: inc/core/codestar/fields/typography/typography.php:201
msgid "Uppercase"
msgstr "Mayúsculas"

#: inc/core/codestar/fields/typography/typography.php:202
msgid "Lowercase"
msgstr "Minúsculas"

#: inc/core/codestar/fields/typography/typography.php:211
msgid "Text Decoration"
msgstr "Decoración del texto"

#: inc/core/codestar/fields/typography/typography.php:218
msgid "Wavy"
msgstr "Ondulado"

#: inc/core/codestar/fields/typography/typography.php:219
msgid "Overline"
msgstr "Sobrerayado"

#: inc/core/codestar/fields/typography/typography.php:220
msgid "Line-through"
msgstr "Tachada"

#: inc/core/codestar/fields/typography/typography.php:233
msgid "Font Size"
msgstr "Tamaño de fuente"

#: inc/core/codestar/fields/typography/typography.php:245
msgid "Line Height"
msgstr "Altura lineal"

#: inc/core/codestar/fields/typography/typography.php:257
msgid "Letter Spacing"
msgstr "Espacio entre letras"

#: inc/core/codestar/fields/typography/typography.php:269
msgid "Word Spacing"
msgstr "Espaciado entre palabras"

#: inc/core/codestar/fields/typography/typography.php:284
msgid "Font Color"
msgstr "Color de fuente"

#: inc/core/codestar/fields/typography/typography.php:295
msgid "Custom Style"
msgstr "Estilo personalizado"

#: inc/core/codestar/fields/typography/typography.php:362
msgid "Custom Web Fonts"
msgstr "Fuentes web personalizadas"

#: inc/core/codestar/fields/typography/typography.php:368
msgid "Safe Web Fonts"
msgstr "Fuentes Web Seguras"

#: inc/core/codestar/fields/typography/typography.php:388
msgid "Google Web Fonts"
msgstr "Fuentes web de Google"

#: inc/core/codestar/functions/actions.php:72
#: inc/core/codestar/functions/actions.php:110
msgid "Error: Invalid key."
msgstr "Error: clave no válida."

#: inc/core/codestar/functions/actions.php:114
msgid "Error: The response is not a valid JSON response."
msgstr "Error: la respuesta no es una respuesta JSON válida."

#: inc/core/codestar/functions/actions.php:174
msgid "Error: Invalid term ID."
msgstr "Error: ID de término no válido."

#: inc/core/codestar/functions/actions.php:180
msgid "Error: You do not have permission to do that."
msgstr "Error: No tiene permiso para hacerlo."

#: inc/core/codestar/functions/validate.php:14
#: inc/core/codestar/functions/validate.php:86
msgid "Please enter a valid email address."
msgstr "Por favor, introduce una dirección de correo electrónico válida."

#: inc/core/codestar/functions/validate.php:32
#: inc/core/codestar/functions/validate.php:106
msgid "Please enter a valid number."
msgstr "Por favor escribe un número válido."

#: inc/core/codestar/functions/validate.php:50
#: inc/core/codestar/functions/validate.php:126
msgid "This field is required."
msgstr "Este campo es obligatorio."

#: inc/core/codestar/functions/validate.php:68
#: inc/core/codestar/functions/validate.php:146
msgid "Please enter a valid URL."
msgstr "Por favor, introduzca una URL válido."

#: inc/core/dbmvs/classes/adminpage.php:49
#: inc/core/dbmvs/classes/importers.php:636 inc/parts/single/temporadas.php:29
msgid "{name}: Season {season}"
msgstr "{name}: Temporada {season}"

#: inc/core/dbmvs/classes/adminpage.php:100
msgid "Dbmovies"
msgstr "Dbmovies"

#: inc/core/dbmvs/classes/adminpage.php:101
msgid "Dbmovies - Settings"
msgstr "Dbmovies - Ajustes"

#: inc/core/dbmvs/classes/adminpage.php:101
#: inc/csf/options.main_settings.php:10 inc/includes/rating/init.php:63
#: pages/sections/account.php:60
msgid "Settings"
msgstr "Ajustes"

#: inc/core/dbmvs/classes/ajax.php:94 inc/core/dbmvs/classes/ajax.php:180
#: inc/core/dbmvs/classes/ajax.php:208 inc/core/dbmvs/classes/ajax.php:238
#: inc/core/dbmvs/classes/importers.php:707
#: inc/core/dbmvs/classes/importers.php:838
msgid "Complete required data"
msgstr "Completar los datos requeridos"

#: inc/core/dbmvs/classes/ajax.php:112
msgid "votes"
msgstr "votos"

#: inc/core/dbmvs/classes/ajax.php:147
msgid "Settings saved"
msgstr "Ajustes guardados"

#: inc/core/dbmvs/classes/ajax.php:153
msgid "No changes to save"
msgstr "No se registro ningún cambio"

#: inc/core/dbmvs/classes/ajax.php:160
msgid "Validation is not completed"
msgstr "La validación no se ha completado"

#: inc/core/dbmvs/classes/dashboard.php:31
msgid "Dooplay"
msgstr "Dooplay"

#: inc/core/dbmvs/classes/dashboard.php:98
#: inc/core/dbmvs/classes/dashboard.php:125
#: inc/core/dbmvs/classes/inboxes.php:269
msgid "Authentication error"
msgstr "Error de autenticación"

#: inc/core/dbmvs/classes/dashboard.php:145
msgid "Guest"
msgstr "Invitado"

#: inc/core/dbmvs/classes/dashboard.php:152
#: inc/core/dbmvs/classes/inboxes.php:224
msgid "Report details"
msgstr "Detalles del reporte"

#: inc/core/dbmvs/classes/dashboard.php:161
#: inc/core/dbmvs/classes/inboxes.php:236
msgid "Sender"
msgstr "Remitente"

#: inc/core/dbmvs/classes/dashboard.php:162
#: inc/core/dbmvs/classes/dashboard.php:199
#: inc/core/dbmvs/classes/inboxes.php:131
#: inc/core/dbmvs/classes/inboxes.php:240
msgid "IP Address"
msgstr "Dirección IP"

#: inc/core/dbmvs/classes/dashboard.php:164
#: inc/core/dbmvs/classes/enqueues.php:73
#: inc/core/dbmvs/classes/epsemboxes.php:160
#: inc/core/dbmvs/classes/epsemboxes.php:194 inc/doo_links.php:590
#: inc/doo_links.php:644 inc/doo_links.php:685 inc/parts/item_links.php:42
#: inc/parts/item_links_admin.php:38
msgid "Edit"
msgstr "Editar"

#: inc/core/dbmvs/classes/dashboard.php:165
msgid "View Post"
msgstr "Ver Post"

#: inc/core/dbmvs/classes/dashboard.php:166
#: inc/core/dbmvs/classes/dashboard.php:202
msgid "Close"
msgstr "Cerrar"

#: inc/core/dbmvs/classes/dashboard.php:186
msgid "Reply"
msgstr "Respuesta"

#: inc/core/dbmvs/classes/dashboard.php:195
#: inc/core/dbmvs/classes/inboxes.php:129
msgid "Email Address"
msgstr "Correo electrónico"

#: inc/core/dbmvs/classes/dashboard.php:198
#: inc/core/dbmvs/classes/inboxes.php:133
msgid "Reference"
msgstr "Referencia"

#: inc/core/dbmvs/classes/dashboard.php:201
msgid "Answer"
msgstr "Responder"

#: inc/core/dbmvs/classes/enqueues.php:53
msgid "Processing.."
msgstr "Procesando.."

#: inc/core/dbmvs/classes/enqueues.php:54
msgid "There is no Internet connection"
msgstr "No hay conexión a Internet"

#: inc/core/dbmvs/classes/enqueues.php:55
msgid "Our services are out of line, please try again later"
msgstr ""
"Nuestros servicios están fuera de línea, por favor inténtelo de nuevo más "
"tarde"

#: inc/core/dbmvs/classes/enqueues.php:56
msgid "The title does not exist or resources are not available at this time"
msgstr ""
"El título no existe o los recursos no están disponibles en este momento"

#: inc/core/dbmvs/classes/enqueues.php:57
msgid "You have not added an API key for Dbmovies"
msgstr "No ha agregado una clave de API para Dbmovies"

#: inc/core/dbmvs/classes/enqueues.php:58
#: inc/core/dbmvs/tpl/import_seaepis.php:17 inc/doo_scripts.php:195
#: inc/doo_scripts.php:226 inc/parts/modules/episodes.php:45
#: inc/parts/modules/featured-post-movies.php:44
#: inc/parts/modules/featured-post-tvshows.php:44
#: inc/parts/modules/featured-post.php:45 inc/parts/modules/movies.php:46
#: inc/parts/modules/seasons.php:45 inc/parts/modules/tvshows.php:46
#: inc/widgets/content_widget_home.php:52
msgid "Loading.."
msgstr "Cargando..."

#: inc/core/dbmvs/classes/enqueues.php:59 inc/core/dbmvs/tpl/admin_app.php:130
msgid "Load More"
msgstr "Cargar más"

#: inc/core/dbmvs/classes/enqueues.php:62
#: inc/core/dbmvs/tpl/admin_settings.php:51
msgid "Save Changes"
msgstr "Guardar Cambios"

#: inc/core/dbmvs/classes/enqueues.php:63
msgid "Saving.."
msgstr "Guardando.."

#: inc/core/dbmvs/classes/enqueues.php:64
#: inc/core/dbmvs/classes/updater.php:240 inc/doo_auth.php:105
#: pages/sections/dt_foot.php:6
msgid "Unknown error"
msgstr "Error desconocido"

#: inc/core/dbmvs/classes/enqueues.php:65
msgid "Connection error"
msgstr "Error de conección"

#: inc/core/dbmvs/classes/enqueues.php:66
msgid "Api key invalid or blocked"
msgstr "Clave de API inválida o bloqueada"

#: inc/core/dbmvs/classes/enqueues.php:67
msgid "There are not enough credits to continue"
msgstr "No hay suficientes créditos para continuar"

#: inc/core/dbmvs/classes/enqueues.php:68 inc/core/dbmvs/classes/filters.php:66
msgid "Process Completed"
msgstr "Proceso completado"

#: inc/core/dbmvs/classes/enqueues.php:69
msgid "Welcome, the service has started successfully"
msgstr "Bienvenido, el servicio ha iniciado con éxito"

#: inc/core/dbmvs/classes/enqueues.php:70
msgid "Log cleaned"
msgstr "Registro limpiado"

#: inc/core/dbmvs/classes/enqueues.php:71
#: inc/core/dbmvs/classes/importers.php:292
#: inc/core/dbmvs/classes/importers.php:560
msgid "Imported"
msgstr "Importado"

#: inc/core/dbmvs/classes/enqueues.php:72
#: inc/core/dbmvs/classes/updater.php:117
#: inc/core/dbmvs/classes/updater.php:263
msgid "Updated"
msgstr "Actualizado"

#: inc/core/dbmvs/classes/enqueues.php:74
msgid "No content available"
msgstr "No hay contenido disponible"

#: inc/core/dbmvs/classes/enqueues.php:76
msgid "Second"
msgstr "Segundo"

#: inc/core/dbmvs/classes/enqueues.php:77
msgid "Seconds"
msgstr "Segundos"

#: inc/core/dbmvs/classes/enqueues.php:78
msgid "Minute"
msgstr "Minuto"

#: inc/core/dbmvs/classes/enqueues.php:79
msgid "Minutes"
msgstr "Minutos"

#: inc/core/dbmvs/classes/enqueues.php:80
msgid "Hour"
msgstr "Hora"

#: inc/core/dbmvs/classes/enqueues.php:81
msgid "Hours"
msgstr "Horas"

#: inc/core/dbmvs/classes/enqueues.php:82
msgid "Day"
msgstr "Día"

#: inc/core/dbmvs/classes/enqueues.php:83
msgid "Days"
msgstr "Dias"

#: inc/core/dbmvs/classes/enqueues.php:84
msgid "Week"
msgstr "Semana"

#: inc/core/dbmvs/classes/enqueues.php:85
msgid "Weeks"
msgstr "Semanas"

#: inc/core/dbmvs/classes/enqueues.php:86
msgid "Month"
msgstr "Mes"

#: inc/core/dbmvs/classes/enqueues.php:87
msgid "Months"
msgstr "Meses"

#: inc/core/dbmvs/classes/enqueues.php:88
#: inc/core/dbmvs/classes/taxonomies.php:219
#: inc/core/dbmvs/classes/taxonomies.php:220
#: inc/core/dbmvs/classes/taxonomies.php:221
#: inc/core/dbmvs/tpl/admin_app.php:50
msgid "Year"
msgstr "Año"

#: inc/core/dbmvs/classes/enqueues.php:89
msgid "Years"
msgstr "Años"

#: inc/core/dbmvs/classes/epsemboxes.php:52
msgid "Generate new seasons"
msgstr "Genere nuevas temporadas"

#: inc/core/dbmvs/classes/epsemboxes.php:52
msgid "Generate Seasons"
msgstr "Generar temporadas"

#: inc/core/dbmvs/classes/epsemboxes.php:57
#: inc/core/dbmvs/classes/epsemboxes.php:76
msgid "There is not yet content to show"
msgstr "Todavía no hay contenido para mostrar"

#: inc/core/dbmvs/classes/epsemboxes.php:71
msgid "Generate new episodes"
msgstr "Generar nuevos episodios"

#: inc/core/dbmvs/classes/epsemboxes.php:71
msgid "Generate Episodes"
msgstr "Generar episodios"

#: inc/core/dbmvs/classes/epsemboxes.php:150
#: inc/core/dbmvs/classes/epsemboxes.php:187
msgid "date not defined"
msgstr "fecha no definida"

#: inc/core/dbmvs/classes/epsemboxes.php:154
#: inc/core/dbmvs/classes/tables.php:158
msgid "Get episodes"
msgstr "Obtener episodios"

#: inc/core/dbmvs/classes/epsemboxes.php:161
#: inc/core/dbmvs/classes/epsemboxes.php:195
#: inc/parts/simple_item_favorites.php:5 inc/parts/simple_item_views.php:5
msgid "View"
msgstr "Ver"

#: inc/core/dbmvs/classes/epsemboxes.php:162
#: inc/core/dbmvs/classes/epsemboxes.php:196 inc/doo_links.php:276
#: inc/doo_links.php:591 inc/doo_links.php:645 inc/doo_links.php:686
#: inc/doo_scripts.php:209 inc/parts/item_links.php:49
#: inc/parts/item_links_admin.php:45
msgid "Delete"
msgstr "Eliminar"

#: inc/core/dbmvs/classes/filters.php:202
msgid "No date"
msgstr "Sin fecha"

#: inc/core/dbmvs/classes/helpers.php:22 inc/parts/single/report-video.php:13
msgid "Labeling problem"
msgstr "Problema de etiquetado"

#: inc/core/dbmvs/classes/helpers.php:23 inc/parts/single/report-video.php:14
msgid "Wrong title or summary, or episode out of order"
msgstr "Título o resumen erróneos, o episodio fuera de orden"

#: inc/core/dbmvs/classes/helpers.php:29 inc/parts/single/report-video.php:18
msgid "Video Problem"
msgstr "Problema de vídeo"

#: inc/core/dbmvs/classes/helpers.php:30 inc/parts/single/report-video.php:19
msgid "Blurry, cuts out, or looks strange in some way"
msgstr "Imagen borrosa, se corta o no está en buenas condiciones"

#: inc/core/dbmvs/classes/helpers.php:36 inc/parts/single/report-video.php:23
msgid "Sound Problem"
msgstr "Problema de sonido"

#: inc/core/dbmvs/classes/helpers.php:37 inc/parts/single/report-video.php:24
msgid "Hard to hear, not matched with video, or missing in some parts"
msgstr ""
"Difícil de escuchar, no va con la imagen del video, el sonido se pierde en "
"algunas partes"

#: inc/core/dbmvs/classes/helpers.php:43 inc/parts/single/report-video.php:28
msgid "Subtitles or captions problem"
msgstr "Problema de subtítulos o subtítulos ocultos"

#: inc/core/dbmvs/classes/helpers.php:44 inc/parts/single/report-video.php:29
msgid ""
"Missing, hard to read, not matched with sound, misspellings, or poor "
"translations"
msgstr ""
"Faltan, son difíciles de leer, no van con el sonido, tienen faltas de "
"ortografía o malas traducciones"

#: inc/core/dbmvs/classes/helpers.php:50 inc/parts/single/report-video.php:33
msgid "Buffering or connection problem"
msgstr "Problema de almacenamiento en búfer o conexión"

#: inc/core/dbmvs/classes/helpers.php:51 inc/parts/single/report-video.php:34
msgid "Frequent rebuffering, playback won't start, or other problem"
msgstr ""
"Repetición de almacenamiento en búfer, la reproducción no empieza u otro "
"problema"

#: inc/core/dbmvs/classes/helpers.php:57
msgid "Unknown problem"
msgstr "Problema desconocido"

#: inc/core/dbmvs/classes/helpers.php:58
msgid "Problem not specified"
msgstr "Problema no especificado"

#: inc/core/dbmvs/classes/helpers.php:502
msgid "Admin-Ajax"
msgstr "Admin-Ajax"

#: inc/core/dbmvs/classes/helpers.php:513
msgid "Weekly"
msgstr "Semanal"

#: inc/core/dbmvs/classes/helpers.php:514
msgid "Monthly"
msgstr "Mensual"

#: inc/core/dbmvs/classes/helpers.php:515
msgid "Quarterly"
msgstr "Trimestral"

#: inc/core/dbmvs/classes/helpers.php:516
msgid "Never"
msgstr "Nunca"

#: inc/core/dbmvs/classes/helpers.php:526
#: inc/csf/options.featured_titles.php:72 inc/csf/options.main_slider.php:63
#: inc/csf/options.module_episodes.php:60 inc/csf/options.module_movies.php:60
#: inc/csf/options.module_seasons.php:60 inc/csf/options.module_tvshows.php:60
#: inc/widgets/content_related_widget.php:117 inc/widgets/content_widget.php:91
msgid "Ascending"
msgstr "Ascendente"

#: inc/core/dbmvs/classes/helpers.php:527
#: inc/csf/options.featured_titles.php:71 inc/csf/options.main_slider.php:62
#: inc/csf/options.module_episodes.php:59 inc/csf/options.module_movies.php:59
#: inc/csf/options.module_seasons.php:59 inc/csf/options.module_tvshows.php:59
#: inc/widgets/content_related_widget.php:116 inc/widgets/content_widget.php:90
msgid "Descending"
msgstr "Descendente"

#: inc/core/dbmvs/classes/helpers.php:537
msgid "Publish"
msgstr "Publicado"

#: inc/core/dbmvs/classes/helpers.php:538
msgid "Pending"
msgstr "Pendiente"

#: inc/core/dbmvs/classes/helpers.php:539
msgid "Draft"
msgstr "Borrador"

#: inc/core/dbmvs/classes/helpers.php:549 inc/csf/options.comments.php:53
msgid "Arabic"
msgstr "Árabe"

#: inc/core/dbmvs/classes/helpers.php:550 inc/csf/options.comments.php:61
msgid "Bosnian"
msgstr "Bosnio"

#: inc/core/dbmvs/classes/helpers.php:551 inc/csf/options.comments.php:58
msgid "Bulgarian"
msgstr "Búlgaro"

#: inc/core/dbmvs/classes/helpers.php:552 inc/csf/options.comments.php:102
msgid "Croatian"
msgstr "Croata"

#: inc/core/dbmvs/classes/helpers.php:553 inc/csf/options.comments.php:66
msgid "Czech"
msgstr "Checo"

#: inc/core/dbmvs/classes/helpers.php:554 inc/csf/options.comments.php:69
msgid "Danish"
msgstr "Danés"

#: inc/core/dbmvs/classes/helpers.php:555 inc/csf/options.comments.php:139
#: inc/doo_player.php:47
msgid "Dutch"
msgstr "Holandés"

#: inc/core/dbmvs/classes/helpers.php:556 inc/doo_player.php:48
msgid "English"
msgstr "Inglés"

#: inc/core/dbmvs/classes/helpers.php:557 inc/csf/options.comments.php:89
msgid "Finnish"
msgstr "Finlandés"

#: inc/core/dbmvs/classes/helpers.php:558 inc/doo_player.php:51
msgid "French"
msgstr "Francés"

#: inc/core/dbmvs/classes/helpers.php:559 inc/csf/options.comments.php:70
#: inc/doo_player.php:52
msgid "German"
msgstr "Alemán"

#: inc/core/dbmvs/classes/helpers.php:560 inc/csf/options.comments.php:71
msgid "Greek"
msgstr "Griego"

#: inc/core/dbmvs/classes/helpers.php:561 inc/csf/options.comments.php:100
msgid "Hebrew"
msgstr "Hebreo"

#: inc/core/dbmvs/classes/helpers.php:562 inc/csf/options.comments.php:103
msgid "Hungarian"
msgstr "Húngaro"

#: inc/core/dbmvs/classes/helpers.php:563 inc/csf/options.comments.php:107
msgid "Icelandic"
msgstr "Islandés"

#: inc/core/dbmvs/classes/helpers.php:564 inc/csf/options.comments.php:105
#: inc/doo_player.php:53
msgid "Indonesian"
msgstr "Indones"

#: inc/core/dbmvs/classes/helpers.php:565 inc/csf/options.comments.php:108
#: inc/doo_player.php:55
msgid "Italian"
msgstr "Italiano"

#: inc/core/dbmvs/classes/helpers.php:566 inc/csf/options.comments.php:116
#: inc/doo_player.php:57
msgid "Korean"
msgstr "Korenao"

#: inc/core/dbmvs/classes/helpers.php:567
msgid "Letzeburgesch"
msgstr "Luxemburgués"

#: inc/core/dbmvs/classes/helpers.php:568 inc/csf/options.comments.php:124
msgid "Lithuanian"
msgstr "Lituano"

#: inc/core/dbmvs/classes/helpers.php:569
msgid "Mandarin"
msgstr "Mandarín"

#: inc/core/dbmvs/classes/helpers.php:570 inc/csf/options.comments.php:86
msgid "Persian"
msgstr "Persa"

#: inc/core/dbmvs/classes/helpers.php:571 inc/csf/options.comments.php:144
#: inc/doo_player.php:61
msgid "Polish"
msgstr "Polaco"

#: inc/core/dbmvs/classes/helpers.php:572
msgid "Portuguese"
msgstr "Portugués"

#: inc/core/dbmvs/classes/helpers.php:573
msgid "Brazilian Portuguese"
msgstr "Portugués brasileño"

#: inc/core/dbmvs/classes/helpers.php:574 inc/csf/options.comments.php:150
#: inc/doo_player.php:62
msgid "Romanian"
msgstr "Rumano"

#: inc/core/dbmvs/classes/helpers.php:575 inc/csf/options.comments.php:151
msgid "Russian"
msgstr "Ruso"

#: inc/core/dbmvs/classes/helpers.php:576 inc/csf/options.comments.php:157
msgid "Slovak"
msgstr "Eslovaco"

#: inc/core/dbmvs/classes/helpers.php:577
msgid "Spanish"
msgstr "Español"

#: inc/core/dbmvs/classes/helpers.php:578
msgid "Spanish LA"
msgstr "Español (LA)"

#: inc/core/dbmvs/classes/helpers.php:579 inc/csf/options.comments.php:163
msgid "Swedish"
msgstr "Sueco"

#: inc/core/dbmvs/classes/helpers.php:580 inc/csf/options.comments.php:170
msgid "Thai"
msgstr "Tailandés"

#: inc/core/dbmvs/classes/helpers.php:581 inc/csf/options.comments.php:174
#: inc/doo_player.php:71
msgid "Turkish"
msgstr "Turco"

#: inc/core/dbmvs/classes/helpers.php:582
msgid "Twi"
msgstr "Twi"

#: inc/core/dbmvs/classes/helpers.php:583 inc/csf/options.comments.php:177
msgid "Ukrainian"
msgstr "Ucraniano"

#: inc/core/dbmvs/classes/helpers.php:584 inc/csf/options.comments.php:180
msgid "Vietnamese"
msgstr "Vietnamita"

#: inc/core/dbmvs/classes/helpers.php:595
#: inc/core/dbmvs/classes/helpers.php:629
msgid "All genres"
msgstr "Todos los generos"

#: inc/core/dbmvs/classes/helpers.php:596
msgid "Action"
msgstr "Acción"

#: inc/core/dbmvs/classes/helpers.php:597
msgid "Adventure"
msgstr "Aventura"

#: inc/core/dbmvs/classes/helpers.php:598
#: inc/core/dbmvs/classes/helpers.php:631
msgid "Animation"
msgstr "Animación"

#: inc/core/dbmvs/classes/helpers.php:599
#: inc/core/dbmvs/classes/helpers.php:632
msgid "Comedy"
msgstr "Comedia"

#: inc/core/dbmvs/classes/helpers.php:600
#: inc/core/dbmvs/classes/helpers.php:633
msgid "Crime"
msgstr "Crimen"

#: inc/core/dbmvs/classes/helpers.php:601
#: inc/core/dbmvs/classes/helpers.php:634
msgid "Documentary"
msgstr "Documentales"

#: inc/core/dbmvs/classes/helpers.php:602
#: inc/core/dbmvs/classes/helpers.php:635
msgid "Drama"
msgstr "Drama"

#: inc/core/dbmvs/classes/helpers.php:603
#: inc/core/dbmvs/classes/helpers.php:636
msgid "Family"
msgstr "Familia"

#: inc/core/dbmvs/classes/helpers.php:604
msgid "Fantasy"
msgstr "Fantasía"

#: inc/core/dbmvs/classes/helpers.php:605
msgid "History"
msgstr "Historia"

#: inc/core/dbmvs/classes/helpers.php:606
msgid "Horror"
msgstr "Terror"

#: inc/core/dbmvs/classes/helpers.php:607
msgid "Music"
msgstr "Musica"

#: inc/core/dbmvs/classes/helpers.php:608
#: inc/core/dbmvs/classes/helpers.php:638
msgid "Mystery"
msgstr "Misterio"

#: inc/core/dbmvs/classes/helpers.php:609
msgid "Romance"
msgstr "Romance"

#: inc/core/dbmvs/classes/helpers.php:610
msgid "Science Fiction"
msgstr "Ciencia ficción"

#: inc/core/dbmvs/classes/helpers.php:611
msgid "TV Movie"
msgstr "Películas de la TV"

#: inc/core/dbmvs/classes/helpers.php:612
msgid "Thriller"
msgstr "Thriller"

#: inc/core/dbmvs/classes/helpers.php:613
msgid "War"
msgstr "Guerra"

#: inc/core/dbmvs/classes/helpers.php:614
#: inc/core/dbmvs/classes/helpers.php:645
msgid "Western"
msgstr "Western"

#: inc/core/dbmvs/classes/helpers.php:630
msgid "Action & Adventure"
msgstr "Acción y aventura"

#: inc/core/dbmvs/classes/helpers.php:637
msgid "Kids"
msgstr "Niños"

#: inc/core/dbmvs/classes/helpers.php:639
msgid "News"
msgstr "Noticias"

#: inc/core/dbmvs/classes/helpers.php:640
msgid "Reality"
msgstr "Reality"

#: inc/core/dbmvs/classes/helpers.php:641
msgid "Sci-Fi & Fantasy"
msgstr "Ciencia ficción y fantasía"

#: inc/core/dbmvs/classes/helpers.php:642
msgid "Soap"
msgstr "Soap"

#: inc/core/dbmvs/classes/helpers.php:643
msgid "Talk"
msgstr "Talk"

#: inc/core/dbmvs/classes/helpers.php:644
msgid "War & Politics"
msgstr "Guerra y política"

#: inc/core/dbmvs/classes/helpers.php:902
msgid "Pending process"
msgstr "Proceso pendiente"

#: inc/core/dbmvs/classes/importers.php:71
#: inc/core/dbmvs/classes/inboxes.php:63
msgid "Unknown action"
msgstr "Acción desconocida"

#: inc/core/dbmvs/classes/importers.php:291
#: inc/core/dbmvs/classes/postypes.php:32
#: inc/core/dbmvs/classes/requests.php:137
#: inc/core/dbmvs/classes/requests.php:301 inc/parts/item_b.php:13
#: pages/search.php:30
msgid "Movie"
msgstr "Película"

#: inc/core/dbmvs/classes/importers.php:302
#: inc/core/dbmvs/classes/importers.php:570
#: inc/core/dbmvs/classes/importers.php:688
#: inc/core/dbmvs/classes/importers.php:819
#: inc/core/dbmvs/classes/inboxes.php:145
#: inc/core/dbmvs/classes/inboxes.php:251
msgid "Error WordPress"
msgstr "Error de WordPress"

#: inc/core/dbmvs/classes/importers.php:308
#: inc/core/dbmvs/classes/importers.php:576
msgid "The title is not defined"
msgstr "El título no está definido"

#: inc/core/dbmvs/classes/importers.php:314
#: inc/core/dbmvs/classes/importers.php:588
msgid "This title already exists in the database"
msgstr "Este título ya existe en la base de datos"

#: inc/core/dbmvs/classes/importers.php:320
#: inc/core/dbmvs/classes/importers.php:594
msgid "This link is not valid"
msgstr "Este enlace no es válido"

#: inc/core/dbmvs/classes/importers.php:326
#: inc/core/dbmvs/classes/importers.php:600
msgid "TMDb ID is not defined"
msgstr "El ID de TMDb no está definido"

#: inc/core/dbmvs/classes/importers.php:701
msgid "This season already exists in database"
msgstr "Esta temporada ya existe en la base de datos"

#: inc/core/dbmvs/classes/importers.php:832
msgid "This episode already exists in database"
msgstr "Este episodio ya existe en la base de datos"

#: inc/core/dbmvs/classes/inboxes.php:130
msgid "Contact Key"
msgstr "Clave de Contacto"

#: inc/core/dbmvs/classes/inboxes.php:135
msgid "New contact message"
msgstr "Nuevo mensaje de contacto"

#: inc/core/dbmvs/classes/inboxes.php:140
msgid "Your message was sent successfully."
msgstr "Tu mensaje fue enviado correctamente."

#: inc/core/dbmvs/classes/inboxes.php:151
#: inc/core/dbmvs/classes/inboxes.php:257
msgid "Your IP address has been blocked"
msgstr "Su dirección IP ha sido bloqueada"

#: inc/core/dbmvs/classes/inboxes.php:157
#: inc/core/dbmvs/classes/inboxes.php:263
msgid "Logs limit exceeded, please try again later."
msgstr "Límite de registros excedido, inténtelo de nuevo más tarde."

#: inc/core/dbmvs/classes/inboxes.php:163 inc/doo_auth.php:111
msgid "Google reCAPTCHA Error"
msgstr "Error de Google reCAPTCHA"

#: inc/core/dbmvs/classes/inboxes.php:223
msgid "New report registered"
msgstr "Nuevo reporte registrado"

#: inc/core/dbmvs/classes/inboxes.php:238
msgid "Permalink"
msgstr "Enlace permanente"

#: inc/core/dbmvs/classes/inboxes.php:239
msgid "Edit Content"
msgstr "Editar Contenido"

#: inc/core/dbmvs/classes/inboxes.php:246
msgid "Report sent successfully"
msgstr "El reporte se envió exitosamente"

#: inc/core/dbmvs/classes/metaboxes.php:26
msgid "Movie Info"
msgstr "Información de la Película"

#: inc/core/dbmvs/classes/metaboxes.php:27
msgid "TVShow Info"
msgstr "TVShow Info"

#: inc/core/dbmvs/classes/metaboxes.php:28
msgid "Season Info"
msgstr "Info temporada"

#: inc/core/dbmvs/classes/metaboxes.php:29
msgid "Episode Info"
msgstr "Información del episodio"

#: inc/core/dbmvs/classes/metaboxes.php:49
#: inc/core/dbmvs/classes/metaboxes.php:209
#: inc/core/dbmvs/classes/metaboxes.php:334
#: inc/core/dbmvs/classes/metaboxes.php:385
msgid "Generate data"
msgstr "Generar datos"

#: inc/core/dbmvs/classes/metaboxes.php:50
msgid "Generate data from <strong>imdb.com</strong>"
msgstr "Generar contenido desde <strong>imdb.com</strong>"

#: inc/core/dbmvs/classes/metaboxes.php:51
msgid "E.g. http://www.imdb.com/title/<strong>tt2911666</strong>/"
msgstr "Ejemplo: http://www.imdb.com/title/<strong>tt2911666</strong>/"

#: inc/core/dbmvs/classes/metaboxes.php:58
#: inc/core/dbmvs/classes/metaboxes.php:224
msgid "Featured Title"
msgstr "Titulo destacado"

#: inc/core/dbmvs/classes/metaboxes.php:59
#: inc/core/dbmvs/classes/metaboxes.php:225
msgid "Do you want to mark this title as a featured item?"
msgstr "¿Desea marcar este título como un elemento destacado?"

#: inc/core/dbmvs/classes/metaboxes.php:64
#: inc/core/dbmvs/classes/metaboxes.php:230
msgid "Images and trailer"
msgstr "Imágenes y trailer"

#: inc/core/dbmvs/classes/metaboxes.php:69
#: inc/core/dbmvs/classes/metaboxes.php:235
#: inc/core/dbmvs/classes/metaboxes.php:354
msgid "Poster"
msgstr "Poster"

#: inc/core/dbmvs/classes/metaboxes.php:70
#: inc/core/dbmvs/classes/metaboxes.php:76
#: inc/core/dbmvs/classes/metaboxes.php:236
#: inc/core/dbmvs/classes/metaboxes.php:242
#: inc/core/dbmvs/classes/metaboxes.php:355
#: inc/core/dbmvs/classes/metaboxes.php:405
msgid "Add url image"
msgstr "Agregar url imagen"

#: inc/core/dbmvs/classes/metaboxes.php:75
#: inc/core/dbmvs/classes/metaboxes.php:241
#: inc/core/dbmvs/classes/metaboxes.php:404
msgid "Main Backdrop"
msgstr "Telón de fondo principal"

#: inc/core/dbmvs/classes/metaboxes.php:83
#: inc/core/dbmvs/classes/metaboxes.php:248
#: inc/core/dbmvs/classes/metaboxes.php:411
msgid "Backdrops"
msgstr "Telones"

#: inc/core/dbmvs/classes/metaboxes.php:84
#: inc/core/dbmvs/classes/metaboxes.php:249
#: inc/core/dbmvs/classes/metaboxes.php:412
msgid "Place each image url below another"
msgstr "Coloque cada URL de la imagen debajo de otro"

#: inc/core/dbmvs/classes/metaboxes.php:90
#: inc/core/dbmvs/classes/metaboxes.php:255 inc/parts/single/series.php:112
msgid "Video trailer"
msgstr "Video trailer"

#: inc/core/dbmvs/classes/metaboxes.php:91
#: inc/core/dbmvs/classes/metaboxes.php:256
msgid "Add id Youtube video"
msgstr "Añadir ID del video de youtube"

#: inc/core/dbmvs/classes/metaboxes.php:98
msgid "IMDb.com data"
msgstr "Datos de IMDb.com"

#: inc/core/dbmvs/classes/metaboxes.php:105
msgid "Rating IMDb"
msgstr "Calificación de IMDb"

#: inc/core/dbmvs/classes/metaboxes.php:106
#: inc/core/dbmvs/classes/metaboxes.php:170
#: inc/core/dbmvs/classes/metaboxes.php:294
msgid "Average / votes"
msgstr "Promedio / votos"

#: inc/core/dbmvs/classes/metaboxes.php:114
msgid "Rated"
msgstr "Audiencia"

#: inc/core/dbmvs/classes/metaboxes.php:123
msgid "Country"
msgstr "País"

#: inc/core/dbmvs/classes/metaboxes.php:128
msgid "Themoviedb.org data"
msgstr "Datos de themoviedb.org"

#: inc/core/dbmvs/classes/metaboxes.php:138
#: inc/core/dbmvs/classes/tables.php:126
msgid "ID TMDb"
msgstr "TMDb ID"

#: inc/core/dbmvs/classes/metaboxes.php:148 inc/parts/single/peliculas.php:112
#: inc/parts/single/series.php:131
msgid "Original title"
msgstr "Título original"

#: inc/core/dbmvs/classes/metaboxes.php:157
msgid "Tag line"
msgstr "Eslogan"

#: inc/core/dbmvs/classes/metaboxes.php:162
msgid "Release Date"
msgstr "Fecha de lanzamiento"

#: inc/core/dbmvs/classes/metaboxes.php:169
#: inc/core/dbmvs/classes/metaboxes.php:293
msgid "Rating TMDb"
msgstr "Clasificación TMDb"

#: inc/core/dbmvs/classes/metaboxes.php:176
msgid "Runtime"
msgstr "Duración"

#: inc/core/dbmvs/classes/metaboxes.php:183
#: inc/core/dbmvs/classes/metaboxes.php:306
#: inc/core/dbmvs/classes/taxonomies.php:74
#: inc/core/dbmvs/classes/taxonomies.php:75
#: inc/core/dbmvs/classes/taxonomies.php:76 inc/includes/slugs.php:35
#: inc/parts/single/peliculas.php:94 inc/parts/single/peliculas.php:137
#: inc/parts/single/series.php:86 inc/parts/single/series.php:103
msgid "Cast"
msgstr "Reparto"

#: inc/core/dbmvs/classes/metaboxes.php:188
#: inc/core/dbmvs/classes/taxonomies.php:103
#: inc/core/dbmvs/classes/taxonomies.php:104
#: inc/core/dbmvs/classes/taxonomies.php:105 inc/doo_init.php:536
#: inc/includes/slugs.php:37 inc/parts/single/peliculas.php:133
msgid "Director"
msgstr "Director"

#: inc/core/dbmvs/classes/metaboxes.php:210
#: inc/core/dbmvs/classes/metaboxes.php:335
#: inc/core/dbmvs/classes/metaboxes.php:386
msgid "Generate data from <strong>themoviedb.org</strong>"
msgstr "Generar datos desde <strong>themoviedb.org</strong>"

#: inc/core/dbmvs/classes/metaboxes.php:211
msgid ""
"E.g. https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead"
msgstr ""
"Ejemplo: https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead"

#: inc/core/dbmvs/classes/metaboxes.php:218
msgid "Seasons control"
msgstr "Control de temporadas"

#: inc/core/dbmvs/classes/metaboxes.php:219
msgid "I have generated seasons or I will manually"
msgstr "He generado temporadas o las añadiré manualmente"

#: inc/core/dbmvs/classes/metaboxes.php:262
msgid "More data"
msgstr "Mas datos"

#: inc/core/dbmvs/classes/metaboxes.php:268
msgid "Original Name"
msgstr "Nombre original"

#: inc/core/dbmvs/classes/metaboxes.php:273
msgid "Firt air date"
msgstr "Fecha de primera emisión"

#: inc/core/dbmvs/classes/metaboxes.php:278 inc/parts/single/series.php:148
msgid "Last air date"
msgstr "Última transmisión"

#: inc/core/dbmvs/classes/metaboxes.php:285
msgid "Content total posted"
msgstr "Total contenido publicado"

#: inc/core/dbmvs/classes/metaboxes.php:286
msgid "Seasons / Episodes"
msgstr "Temporadas / Episodios"

#: inc/core/dbmvs/classes/metaboxes.php:300
msgid "Episode runtime"
msgstr "Duración de episodios"

#: inc/core/dbmvs/classes/metaboxes.php:311
#: inc/core/dbmvs/classes/taxonomies.php:132
#: inc/core/dbmvs/classes/taxonomies.php:133
#: inc/core/dbmvs/classes/taxonomies.php:134 inc/doo_init.php:567
#: inc/includes/slugs.php:36 inc/parts/single/series.php:99
msgid "Creator"
msgstr "Creador"

#: inc/core/dbmvs/classes/metaboxes.php:336
msgid ""
"E.g. https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead/"
"season/<strong>1</strong>/"
msgstr ""
"Ejemplo: https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-"
"dead/season/<strong>1</strong>/"

#: inc/core/dbmvs/classes/metaboxes.php:343
msgid "Episodes control"
msgstr "Control de episodios"

#: inc/core/dbmvs/classes/metaboxes.php:344
msgid "I generated episodes or add manually"
msgstr "He generado episodios o los añadiré manualmente"

#: inc/core/dbmvs/classes/metaboxes.php:349
#: inc/core/dbmvs/classes/metaboxes.php:399
msgid "Serie name"
msgstr "Nombre de la serie"

#: inc/core/dbmvs/classes/metaboxes.php:360
#: inc/core/dbmvs/classes/metaboxes.php:417
msgid "Air date"
msgstr "Fecha de transmisión"

#: inc/core/dbmvs/classes/metaboxes.php:387
msgid ""
"E.g. https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-dead/"
"season/<strong>1</strong>/episode/<strong>2</strong>"
msgstr ""
"Ejemplo: https://www.themoviedb.org/tv/<strong>1402</strong>-the-walking-"
"dead/season/<strong>1</strong>/episode/<strong>2</strong>"

#: inc/core/dbmvs/classes/metaboxes.php:394
msgid "Episode title"
msgstr "Titulo del episodio"

#: inc/core/dbmvs/classes/postypes.php:44
msgid "Movies manage"
msgstr "Administrar peliculas"

#: inc/core/dbmvs/classes/postypes.php:84
#: inc/core/dbmvs/classes/requests.php:138
#: inc/core/dbmvs/classes/tables.php:176 pages/rating.php:28
#: pages/trending.php:29
msgid "TV Show"
msgstr "Serie"

#: inc/core/dbmvs/classes/postypes.php:85
msgid "TV series manage"
msgstr "Administrar series"

#: inc/core/dbmvs/classes/postypes.php:126
msgid "Seasons manage"
msgstr "Administrar temporadas"

#: inc/core/dbmvs/classes/postypes.php:167
msgid "Episodes manage"
msgstr "Administrar episodios"

#: inc/core/dbmvs/classes/requests.php:41
#: inc/core/dbmvs/classes/requests.php:42
#: inc/core/dbmvs/classes/requests.php:43
#: inc/core/dbmvs/classes/requests.php:44
#: inc/core/dbmvs/classes/requests.php:45
#: inc/core/dbmvs/classes/requests.php:54 inc/core/dbmvs/tpl/admin_app.php:14
#: inc/core/dbmvs/tpl/admin_settings.php:7 inc/includes/slugs.php:24
msgid "Requests"
msgstr "Peticiones"

#: inc/core/dbmvs/classes/requests.php:43
#, php-format
msgid "Requests %%PENDING_REQUEST%%"
msgstr "Solicitudes %%PENDING_REQUEST%%"

#: inc/core/dbmvs/classes/requests.php:55
msgid "Requests manage"
msgstr "Administrar solicitudes"

#: inc/core/dbmvs/classes/requests.php:117
#: inc/core/dbmvs/classes/tables.php:175 inc/core/dbmvs/classes/tables.php:214
msgid "TMDb ID"
msgstr "TMDb ID"

#: inc/core/dbmvs/classes/requests.php:119
msgid "Controls"
msgstr "Controles"

#: inc/core/dbmvs/classes/requests.php:144
msgid "Add to list of requests"
msgstr "Añadir a la lista de peticiones"

#: inc/core/dbmvs/classes/requests.php:144
msgid "A"
msgstr "A"

#: inc/core/dbmvs/classes/requests.php:147
msgid "Import content and remove request"
msgstr "Importar contenido y eliminar petición"

#: inc/core/dbmvs/classes/requests.php:147
msgid "B"
msgstr "B"

#: inc/core/dbmvs/classes/requests.php:149
msgid "Import content"
msgstr "Importar contenido"

#: inc/core/dbmvs/classes/requests.php:149
msgid "C"
msgstr "C"

#: inc/core/dbmvs/classes/requests.php:151
msgid "Remove request"
msgstr "Eliminar petición"

#: inc/core/dbmvs/classes/requests.php:151
msgid "D"
msgstr "D"

#: inc/core/dbmvs/classes/requests.php:234 inc/doo_ajax.php:262
#: inc/doo_ajax.php:305
msgid "No results"
msgstr "Sin resultados"

#: inc/core/dbmvs/classes/requests.php:237
msgid "results"
msgstr "resultados"

#: inc/core/dbmvs/classes/requests.php:237
msgid "in"
msgstr "en"

#: inc/core/dbmvs/classes/requests.php:237
#: inc/widgets/content_widget_home.php:146
#: inc/widgets/content_widget_home.php:147
#: inc/widgets/content_widget_home.php:148
#: inc/widgets/content_widget_home.php:149
msgid "seconds"
msgstr "segundos"

#: inc/core/dbmvs/classes/requests.php:260
msgid "Request"
msgstr "Solicitar"

#: inc/core/dbmvs/classes/requests.php:260
msgid "already exists"
msgstr "ya existe"

#: inc/core/dbmvs/classes/requests.php:273
msgid "Error verification nonce"
msgstr "Error de verificación nonce"

#: inc/core/dbmvs/classes/requests.php:277
msgid "Please <a class=\"clicklogin\">sign in</a> to continue"
msgstr "Por favor <a class=\"clicklogin\">inicie seción</a> para continuar"

#: inc/core/dbmvs/classes/requests.php:302
msgid "TVShow"
msgstr "TVShow"

#: inc/core/dbmvs/classes/requests.php:367
#, php-format
msgid "New request: %s"
msgstr "Nueva solicitud: %s"

#: inc/core/dbmvs/classes/requests.php:386
#, php-format
msgid "The title %s has been added to the list of requests correctly"
msgstr "El título %s se ha añadido a la lista de solicitudes correctamente"

#: inc/core/dbmvs/classes/requests.php:390
#, php-format
msgid ""
"The title %s has been suggested to be added to the list of requests, enter "
"wp-admin to verify it."
msgstr ""
"Se ha sugerido que el título %s se añada a la lista de solicitudes, ingrese "
"a wp-admin para verificarlo."

#: inc/core/dbmvs/classes/tables.php:57 inc/core/dbmvs/classes/tables.php:115
#: inc/doo_scripts.php:256
msgid "Add"
msgstr "Añadir"

#: inc/core/dbmvs/classes/tables.php:68 inc/core/dbmvs/classes/tables.php:127
msgid "Rating"
msgstr "Rating"

#: inc/core/dbmvs/classes/tables.php:69
msgid "IMDb ID"
msgstr "IMDb ID"

#: inc/core/dbmvs/classes/tables.php:71 inc/core/dbmvs/classes/tables.php:130
#: inc/parts/modules/featured-post-movies.php:58
#: inc/parts/modules/featured-post-tvshows.php:58
#: inc/parts/modules/featured-post.php:59
msgid "Featured"
msgstr "Destacado"

#: inc/core/dbmvs/classes/tables.php:97
msgid "Get seasons"
msgstr "Obtener temporadas"

#: inc/core/dbmvs/classes/tables.php:213 inc/parts/single/listas/seasons.php:25
#: inc/parts/single/listas/seasons_episodes.php:49 pages/search.php:33
msgid "Episode"
msgstr "Epidosio"

#: inc/core/dbmvs/classes/taxonomies.php:40
#: inc/widgets/content_widget_meta_genres.php:45
msgid "Genres"
msgstr "Géneros"

#: inc/core/dbmvs/classes/taxonomies.php:161
#: inc/core/dbmvs/classes/taxonomies.php:162
#: inc/core/dbmvs/classes/taxonomies.php:163 inc/includes/slugs.php:34
msgid "Studio"
msgstr "Estudio"

#: inc/core/dbmvs/classes/taxonomies.php:190
#: inc/core/dbmvs/classes/taxonomies.php:191
#: inc/core/dbmvs/classes/taxonomies.php:192
msgid "Networks"
msgstr "Networks"

#: inc/core/dbmvs/classes/updater.php:59
msgid "Every 5 seconds"
msgstr "Cada 5 segundos"

#: inc/core/dbmvs/classes/updater.php:65
msgid "Every 10 seconds"
msgstr "Cada 10 segundos"

#: inc/core/dbmvs/classes/updater.php:71
msgid "Every 30 seconds"
msgstr "Cada 30 segundos"

#: inc/core/dbmvs/classes/updater.php:77
msgid "Every 1 minute"
msgstr "Cada 1 minuto"

#: inc/core/dbmvs/classes/updater.php:129
msgid "There is no content to update"
msgstr "No hay contenido para actualizar"

#: inc/core/dbmvs/classes/updater.php:143
msgid "Process finished, the metadata update was incomplete"
msgstr ""
"Proceso terminado, la actualización de metadatos se finalizo antes de "
"completarse"

#: inc/core/dbmvs/classes/updater.php:247
msgid "Incomplete data"
msgstr "Datos incompletos"

#: inc/core/dbmvs/classes/updater.php:428
msgid "movie"
msgstr "película"

#: inc/core/dbmvs/classes/updater.php:439
#: inc/core/dbmvs/classes/updater.php:571
msgid "Undefined TMDb ID"
msgstr "TMDb ID no está definido"

#: inc/core/dbmvs/classes/updater.php:560
msgid "show"
msgstr "serie"

#: inc/core/dbmvs/classes/updater.php:606 inc/parts/item_se.php:22
msgid "season"
msgstr "temporada"

#: inc/core/dbmvs/classes/updater.php:617
#: inc/core/dbmvs/classes/updater.php:682
msgid "Undefined data"
msgstr "Datos indefinidos"

#: inc/core/dbmvs/classes/updater.php:671
msgid "episode"
msgstr "episodio"

#: inc/core/dbmvs/tpl/admin_app.php:9
msgid "Shows"
msgstr "Series"

#: inc/core/dbmvs/tpl/admin_app.php:12
msgid "Credits"
msgstr "Créditos"

#: inc/core/dbmvs/tpl/admin_app.php:13
msgid "Used"
msgstr "Usados"

#: inc/core/dbmvs/tpl/admin_app.php:21 inc/core/dbmvs/tpl/admin_settings.php:6
msgid "Meta Updater"
msgstr "Meta Updater"

#: inc/core/dbmvs/tpl/admin_app.php:28
msgid "Finish"
msgstr "Finalizar"

#: inc/core/dbmvs/tpl/admin_app.php:29 inc/csf/options.links_module.php:117
#: single-dt_links.php:34
msgid "Continue"
msgstr "Continuar"

#: inc/core/dbmvs/tpl/admin_app.php:35
msgid "Start"
msgstr "Iniciar"

#: inc/core/dbmvs/tpl/admin_app.php:47
msgid "Expand"
msgstr "Expandir"

#: inc/core/dbmvs/tpl/admin_app.php:53 inc/doo_init.php:333
msgid "Page"
msgstr "Página"

#: inc/core/dbmvs/tpl/admin_app.php:57
msgid "Popularity desc"
msgstr "Popularidad descendente"

#: inc/core/dbmvs/tpl/admin_app.php:58
msgid "Popularity asc"
msgstr "Popularidad ascendente"

#: inc/core/dbmvs/tpl/admin_app.php:72
msgid "Discover"
msgstr "Descubrir"

#: inc/core/dbmvs/tpl/admin_app.php:77
msgid "Bulk import"
msgstr "Importar todo"

#: inc/core/dbmvs/tpl/admin_app.php:83
msgid "Search.."
msgstr "Buscar.."

#: inc/core/dbmvs/tpl/admin_app.php:86
msgid "Search"
msgstr "Buscar"

#: inc/core/dbmvs/tpl/admin_app.php:105
msgid "Clean"
msgstr "Limpiar"

#: inc/core/dbmvs/tpl/admin_app.php:117
#, php-format
msgid "About %s results (%s seconds)"
msgstr "Cerca de %s resultados (%s segundos)"

#: inc/core/dbmvs/tpl/admin_app.php:120
#, php-format
msgid "Loaded pages %s"
msgstr "Páginas cargadas %s"

#: inc/core/dbmvs/tpl/admin_app.php:137
msgid "Globals credits"
msgstr "Créditos globales"

#: inc/core/dbmvs/tpl/admin_app.php:141
msgid "Delivered queries"
msgstr "Consultas entregadas"

#: inc/core/dbmvs/tpl/admin_app.php:145
msgid "Queries stored"
msgstr "Consultas almacenadas"

#: inc/core/dbmvs/tpl/admin_app.php:149
msgid "Sites actives"
msgstr "Sitios activos"

#: inc/core/dbmvs/tpl/admin_settings.php:4
#: inc/csf/options.main_settings.php:119 pages/sections/account.php:150
msgid "General"
msgstr "General"

#: inc/core/dbmvs/tpl/admin_settings.php:5
msgid "Titles and Content"
msgstr "Títulos y contenido"

#: inc/core/dbmvs/tpl/admin_settings.php:8
msgid "Advanced"
msgstr "Avanzado"

#: inc/core/dbmvs/tpl/admin_settings.php:9
msgid "Stats"
msgstr "Estadísticas"

#: inc/core/dbmvs/tpl/admin_settings.php:14
msgid "Register on our platform to obtain an API key"
msgstr "Regístrese en nuestra plataforma para obtener una clave de API"

#: inc/core/dbmvs/tpl/admin_settings.php:14
#: inc/core/dbmvs/tpl/admin_settings.php:22 inc/doo_notices.php:69
msgid "Click here"
msgstr "Click aquí"

#: inc/core/dbmvs/tpl/admin_settings.php:22
msgid "Get API Key (v3 auth) for Themoviedb"
msgstr "Obtener clave de API (v3 auth) para Themoviedb"

#: inc/core/dbmvs/tpl/admin_settings.php:52
msgid "Delete cache"
msgstr "Eliminar cache"

#: inc/core/dbmvs/tpl/dashboard_widget.php:3
#: inc/core/dbmvs/tpl/dashboard_widget.php:51
#: inc/csf/options.report_contact.php:40
msgid "Reports"
msgstr "Reportes"

#: inc/core/dbmvs/tpl/dashboard_widget.php:4
#: inc/core/dbmvs/tpl/dashboard_widget.php:53
#: inc/csf/options.main_settings.php:201 inc/csf/options.report_contact.php:47
#: inc/doo_database.php:104
msgid "Contact"
msgstr "Contacto"

#: inc/core/dbmvs/tpl/dashboard_widget.php:5
msgid "More"
msgstr "Más"

#: inc/core/dbmvs/tpl/dashboard_widget.php:13
msgid "Reported content"
msgstr "Contenido reportado"

#: inc/core/dbmvs/tpl/dashboard_widget.php:31
msgid "Contact messages"
msgstr "Mensajes de contacto"

#: inc/core/dbmvs/tpl/dashboard_widget.php:49
msgid "Delete all messages"
msgstr "Eliminar todos los mensajes"

#: inc/core/dbmvs/tpl/dashboard_widget.php:51
#: inc/core/dbmvs/tpl/dashboard_widget.php:53
msgid "Do you really want to continue?"
msgstr "¿realmente deseas continuar?"

#: inc/core/dbmvs/tpl/dashboard_widget.php:52
msgid "or"
msgstr "ó"

#: inc/core/dbmvs/tpl/dashboard_widget.php:57
msgid "Quick access"
msgstr "Acceso rápido"

#: inc/core/dbmvs/tpl/dashboard_widget.php:60
msgid "Dbmovies importer"
msgstr "Importador de Dbmovies"

#: inc/core/dbmvs/tpl/dashboard_widget.php:61
msgid "Dbmovies settings"
msgstr "Ajustes de Dbmovies"

#: inc/core/dbmvs/tpl/dashboard_widget.php:62
msgid "Theme options"
msgstr "Opciones del theme"

#: inc/core/dbmvs/tpl/dashboard_widget.php:63
msgid "Theme license"
msgstr "Licencia del theme"

#: inc/core/dbmvs/tpl/dashboard_widget.php:64
msgid "Database tool"
msgstr "Herramienta de base de datos"

#: inc/core/dbmvs/tpl/dashboard_widget.php:65
msgid "Permalinks"
msgstr "Enlaces permanentes"

#: inc/core/dbmvs/tpl/dashboard_widget.php:66 inc/parts/admin/ads_tool.php:2
msgid "Ad code manager"
msgstr "Gestor de código de anuncios"

#: inc/core/dbmvs/tpl/dashboard_widget.php:69
msgid "Support"
msgstr "Soporte"

#: inc/core/dbmvs/tpl/dashboard_widget.php:72
msgid "Support Forums"
msgstr "Foros de Soporte"

#: inc/core/dbmvs/tpl/dashboard_widget.php:73
msgid "Extended documentation"
msgstr "Documentación ampliada"

#: inc/core/dbmvs/tpl/dashboard_widget.php:74
msgid "Changelog"
msgstr "Registro de cambios"

#: inc/core/dbmvs/tpl/dashboard_widget.php:75
msgid "Telegram Group"
msgstr "Grupo de Telegram"

#: inc/core/dbmvs/tpl/episodes_generator.php:2
#: inc/core/dbmvs/tpl/seasons_generator.php:2
msgid "Get data"
msgstr "Obtener datos"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:1
msgid "PHP Limits"
msgstr "Límites PHP"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:2
msgid ""
"These adjustments will only be established for the functions of Dbmovies"
msgstr "Estos ajustes sólo se establecerán para las funciones de Dbmovies"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:8
msgid "Set time limit"
msgstr "Establecer límite de tiempo"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:11
msgid "Help extend the execution time"
msgstr "Ayuda a extender el tiempo de ejecución"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:16
msgid "Set memory limit"
msgstr "Establecer límite de memoria"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:25
msgid "Seasons and Episodes Order"
msgstr "Orden de temporadas y episodios"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:26
msgid "Set order in which you want to display the lists of these contents"
msgstr "Establezca en el orden que desea mostrar la lista de estos contenidos"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:30
msgid "Order Seasons"
msgstr "Ordenar temporadas"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:36
msgid "Order Episodes"
msgstr "Ordenar episodios"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:44
msgid "Importers Post Status"
msgstr "Estados del contenido importado"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:45
msgid "Define Post Status after using the importers"
msgstr "Definir estado de publicación después de utilizar los importadores"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:49
msgid "Movies Importer"
msgstr "Importador de películas"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:55
msgid "TVShows Importer"
msgstr "Importador de series"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:61
msgid "Seasons Importer"
msgstr "Importador de temporadas"

#: inc/core/dbmvs/tpl/form_setting_advanced.php:67
msgid "Episodes Importer"
msgstr "Importador de episodios"

#: inc/core/dbmvs/tpl/form_setting_general.php:1
msgid "Authorize Application"
msgstr "Autorizar aplicación"

#: inc/core/dbmvs/tpl/form_setting_general.php:2
#: inc/core/dbmvs/tpl/form_setting_requests.php:2
msgid ""
"For this application to function correctly add the required API credentials"
msgstr ""
"Para que esta aplicación funcione correctamente, añada las credenciales API "
"requeridas"

#: inc/core/dbmvs/tpl/form_setting_general.php:2
#: inc/core/dbmvs/tpl/form_setting_titles.php:2
#: inc/core/dbmvs/tpl/form_setting_titles.php:46
#: inc/core/dbmvs/tpl/form_setting_updater.php:2
msgid "Learn more"
msgstr "Saber más"

#: inc/core/dbmvs/tpl/form_setting_general.php:8
msgid "API Key for Dbmovies"
msgstr "Clave API para Dbmovies"

#: inc/core/dbmvs/tpl/form_setting_general.php:11
msgid "Your API key will give you access to all our services"
msgstr "Su clave de API le dará acceso a todos nuestros servicios"

#: inc/core/dbmvs/tpl/form_setting_general.php:16
msgid "API Key for Themoviedb"
msgstr "Clave API para Themoviedb"

#: inc/core/dbmvs/tpl/form_setting_general.php:19
msgid "Add your API key to be able to generate data with our importers"
msgstr ""
"Añada su clave de API para poder generar datos con nuestros importadores"

#: inc/core/dbmvs/tpl/form_setting_general.php:25
msgid "Import settings"
msgstr "Ajustes de importación"

#: inc/core/dbmvs/tpl/form_setting_general.php:26
msgid "Set the settings of your preference for data import"
msgstr ""
"Establecer la configuración de su preferencia para la importación de datos"

#: inc/core/dbmvs/tpl/form_setting_general.php:31
msgid "Set Language"
msgstr "Establecer idioma"

#: inc/core/dbmvs/tpl/form_setting_general.php:39
msgid "App capabilities"
msgstr "Capacidades de la aplicación"

#: inc/core/dbmvs/tpl/form_setting_general.php:42
msgid "Upload poster image to server"
msgstr "Cargar posters e imágenes al servidor"

#: inc/core/dbmvs/tpl/form_setting_general.php:43
msgid "Do you want to autocomplete genres"
msgstr "Auto-completar géneros"

#: inc/core/dbmvs/tpl/form_setting_general.php:44
msgid "Publish content with the release date"
msgstr "Publicar contenido con la fecha de lanzamiento"

#: inc/core/dbmvs/tpl/form_setting_general.php:45
msgid "Activate Auto scroll for results"
msgstr "Activar desplazamiento automático en los resultados de la aplicación"

#: inc/core/dbmvs/tpl/form_setting_general.php:46
msgid "Do not import data until after publishing"
msgstr "No importar datos solo hasta después de publicar"

#: inc/core/dbmvs/tpl/form_setting_general.php:47
msgid "Allow repeated content"
msgstr "Permitir contenido repetido"

#: inc/core/dbmvs/tpl/form_setting_general.php:48
msgid "Massively import safely"
msgstr "Importación masiva segura"

#: inc/core/dbmvs/tpl/form_setting_general.php:53
msgid "Auto Scroll Results limits"
msgstr "Limitar resultados"

#: inc/core/dbmvs/tpl/form_setting_general.php:56
msgid ""
"Set the maximum number of results that can be obtained with the Infinite "
"Scroll"
msgstr ""
"Establecer el número máximo de resultados que se pueden obtener con el "
"desplazamiento automatico"

#: inc/core/dbmvs/tpl/form_setting_general.php:61
msgid "Delay time"
msgstr "Tiempo retardo"

#: inc/core/dbmvs/tpl/form_setting_general.php:64
msgid "Time in milliseconds to execute the next importation"
msgstr "Tiempo en milisegundos para ejecutar la próxima importación"

#: inc/core/dbmvs/tpl/form_setting_requests.php:1
msgid "User Requests"
msgstr "Peticiones de usuarios"

#: inc/core/dbmvs/tpl/form_setting_requests.php:8
#: inc/csf/options.report_contact.php:29 inc/doo_comments.php:95
#: pages/contact.php:20 pages/contact.php:29
msgid "Email"
msgstr "Correo electrónico"

#: inc/core/dbmvs/tpl/form_setting_requests.php:11
msgid "Establish an email where you want to be notified of new requests"
msgstr ""
"Establezca un correo electrónico en el que desee recibir notificaciones de "
"nuevas solicitudes"

#: inc/core/dbmvs/tpl/form_setting_requests.php:16
#: inc/core/dbmvs/tpl/form_setting_requests.php:32
msgid "Unknown user"
msgstr "Usuario desconocido"

#: inc/core/dbmvs/tpl/form_setting_requests.php:19
msgid "Unknown users can publish requests?"
msgstr "¿Los usuarios desconocidos pueden publicar solicitudes?"

#: inc/core/dbmvs/tpl/form_setting_requests.php:24
msgid "Auto publish"
msgstr "Publicación automática"

#: inc/core/dbmvs/tpl/form_setting_requests.php:27
#: inc/csf/options.links_module.php:36 inc/csf/options.links_module.php:50
msgid "Administrator"
msgstr "Administrador"

#: inc/core/dbmvs/tpl/form_setting_requests.php:28
#: inc/csf/options.links_module.php:37 inc/csf/options.links_module.php:51
msgid "Editor"
msgstr "Editor"

#: inc/core/dbmvs/tpl/form_setting_requests.php:29
#: inc/csf/options.links_module.php:38 inc/csf/options.links_module.php:52
msgid "Author"
msgstr "Autor"

#: inc/core/dbmvs/tpl/form_setting_requests.php:30
#: inc/csf/options.links_module.php:39 inc/csf/options.links_module.php:53
msgid "Contributor"
msgstr "Colaborador"

#: inc/core/dbmvs/tpl/form_setting_requests.php:31
#: inc/csf/options.links_module.php:40 inc/csf/options.links_module.php:54
msgid "Subscriber"
msgstr "Suscriptor"

#: inc/core/dbmvs/tpl/form_setting_requests.php:33
msgid "Mark user roles that do not require content moderation"
msgstr "Marcar roles de usuario que no requieren moderación de contenido"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:1
msgid "My stats"
msgstr "Mis estadísticas"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:2
msgid "A brief summary of the statistics related to your api key."
msgstr "Un breve resumen de las estadísticas relacionadas con la clave de api."

#: inc/core/dbmvs/tpl/form_setting_statistics.php:8
msgid "Available credits"
msgstr "Créditos disponibles"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:16
#: inc/core/dbmvs/tpl/form_setting_statistics.php:48
msgid "Credits used"
msgstr "Créditos utilizados"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:24
#: inc/core/dbmvs/tpl/form_setting_statistics.php:56
msgid "Total requests"
msgstr "Total de solicitudes"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:33
msgid "Global Statistics"
msgstr "Estadística Global"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:34
msgid "Global summary of the metric status of our services for Dbmovies."
msgstr "Resumen global del estado métrico de nuestros servicios para Dbmovies."

#: inc/core/dbmvs/tpl/form_setting_statistics.php:40
msgid "Credits in reserve"
msgstr "Créditos en reserva"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:64
msgid "Stored requests"
msgstr "Consultas almacenadas"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:72
msgid "Active licenses"
msgstr "Licencias activadas"

#: inc/core/dbmvs/tpl/form_setting_statistics.php:80
msgid "Active sites"
msgstr "Sitios activos"

#: inc/core/dbmvs/tpl/form_setting_titles.php:1
msgid "Customize titles"
msgstr "Personalizar títulos"

#: inc/core/dbmvs/tpl/form_setting_titles.php:2
msgid "Configure the titles that are generated in importers"
msgstr "Configurar los títulos que se generan en los importadores"

#: inc/core/dbmvs/tpl/form_setting_titles.php:12
#: inc/core/dbmvs/tpl/form_setting_titles.php:21
#: inc/core/dbmvs/tpl/form_setting_titles.php:30
#: inc/core/dbmvs/tpl/form_setting_titles.php:39
#: inc/core/dbmvs/tpl/form_setting_titles.php:56
#: inc/core/dbmvs/tpl/form_setting_titles.php:71
msgid "Usable tags"
msgstr "Etiquetas utilizables"

#: inc/core/dbmvs/tpl/form_setting_titles.php:45
msgid "Customize Content"
msgstr "Personalizar contenido"

#: inc/core/dbmvs/tpl/form_setting_titles.php:46
msgid "Customize how content for movies and tvshows will be imported"
msgstr "Personalizar cómo se importará el contenido de películas y programas"

#: inc/core/dbmvs/tpl/form_setting_titles.php:51
msgid "Content for movies"
msgstr "Contenido para películas"

#: inc/core/dbmvs/tpl/form_setting_titles.php:66
msgid "Content for shows"
msgstr "Contenido para Series"

#: inc/core/dbmvs/tpl/form_setting_updater.php:1
msgid "Metadata Updater"
msgstr "Actualizador"

#: inc/core/dbmvs/tpl/form_setting_updater.php:2
msgid ""
"This tool updates and repairs metadata of all content published or imported "
"by Dbmovies"
msgstr ""
"Esta herramienta actualiza y repara los metadatos de todo el contenido "
"publicado o importado por Dbmovies"

#: inc/core/dbmvs/tpl/form_setting_updater.php:7
msgid "Method"
msgstr "Método"

#: inc/core/dbmvs/tpl/form_setting_updater.php:14
#: inc/csf/options.main_slider.php:21
msgid "Post Types"
msgstr "Tipos de Publicaciones"

#: inc/core/dbmvs/tpl/form_setting_updater.php:19
#: inc/csf/options.module_seasons.php:8
msgid "TV Shows > Seasons"
msgstr "Series > Temporadas"

#: inc/core/dbmvs/tpl/form_setting_updater.php:20
#: inc/csf/options.module_episodes.php:8
msgid "TV Shows > Episodes"
msgstr "Series > Episodios"

#: inc/core/dbmvs/tpl/import_movies.php:6
msgid "Paste URL of IMDb or TMDb"
msgstr "Pegar URL de IMDb o TMDb"

#: inc/core/dbmvs/tpl/import_tvshows.php:6
msgid "Paste URL of TMDb"
msgstr "Pegar URL de TMDb"

#: inc/core/doothemes/class.php:94
msgid "Dooplay license"
msgstr "Licencia Dooplay"

#: inc/core/doothemes/class.php:117
msgid "requires a valid license to activate all functions."
msgstr "requiere una licencia valida para activar todas las funciones."

#: inc/core/doothemes/class.php:119 inc/core/doothemes/class.php:154
#: inc/parts/admin/database_tool.php:37
msgid "Activate license"
msgstr "Activar licencia"

#: inc/core/doothemes/class.php:122
msgid "Details"
msgstr "Detalles"

#: inc/core/doothemes/class.php:132
msgid "License"
msgstr "Licencia"

#: inc/core/doothemes/class.php:141
msgid "License activated, thanks."
msgstr "Licencia activa, Gracias."

#: inc/core/doothemes/class.php:143
msgid "Activate your license to install future updates."
msgstr "Active su licencia para poder instalar actualizaciones futuras."

#: inc/core/doothemes/class.php:147 inc/doo_links.php:239
#: inc/parts/admin/ads_tool.php:65
msgid "Save changes"
msgstr "Guardar cambios"

#: inc/core/doothemes/class.php:152
msgid "Deactivate license"
msgstr "Desactivar Licencia"

#: inc/core/doothemes/class.php:162
msgid "License status"
msgstr "Estado de la licencia"

#: inc/core/doothemes/class.php:163
msgid "Customer name"
msgstr "Nombre del cliente"

#: inc/core/doothemes/class.php:164
msgid "Customer email"
msgstr "Correo electrónico"

#: inc/core/doothemes/class.php:165
msgid "Payment id"
msgstr "ID de pago"

#: inc/core/doothemes/class.php:166
msgid "Activations limit"
msgstr "Limite de activaciones"

#: inc/core/doothemes/class.php:167
msgid "Activations count"
msgstr "Conteo de activaciones"

#: inc/core/doothemes/class.php:168
msgid "Activations left"
msgstr "Activaciones disponibles"

#: inc/core/doothemes/class.php:169
msgid "Expires"
msgstr "Caducidad"

#: inc/core/doothemes/init.php:43
msgid "license"
msgstr "licencia"

#: inc/core/doothemes/init.php:44
msgid "Enter your theme license key."
msgstr "Introduzca su código de licencia del tema."

#: inc/core/doothemes/init.php:45 inc/csf/options.video_player.php:137
#: inc/csf/options.video_player.php:145
msgid "License Key"
msgstr "Codigo de licencia"

#: inc/core/doothemes/init.php:46
msgid "License Action"
msgstr "Acción de licencia"

#: inc/core/doothemes/init.php:47
msgid "Deactivate License"
msgstr "Desactivar Licencia"

#: inc/core/doothemes/init.php:48
msgid "Activate License"
msgstr "Activar licencia"

#: inc/core/doothemes/init.php:49 inc/core/doothemes/init.php:61
msgid "License status is unknown."
msgstr "El estado de la licencia es desconocido."

#: inc/core/doothemes/init.php:50
msgid "Renew?"
msgstr "Renovar?"

#: inc/core/doothemes/init.php:51
msgid "unlimited"
msgstr "ilimitado"

#: inc/core/doothemes/init.php:52
msgid "License key is active"
msgstr "La clave de licencia está activa"

#: inc/core/doothemes/init.php:53
#, php-format
msgid "since %s."
msgstr "desde %s."

#: inc/core/doothemes/init.php:54
#, php-format
msgid "You have %1$s / %2$s sites activated."
msgstr "Tiene %1$s de %2$s activaciones disponibles."

#: inc/core/doothemes/init.php:55
#, php-format
msgid "License key expired %s."
msgstr "La clave de licencia caducó el %s."

#: inc/core/doothemes/init.php:56
msgid "License key has expired."
msgstr "Su licencia esta expirada."

#: inc/core/doothemes/init.php:57
msgid "License keys do not match."
msgstr "El código no es valido, verifique su autenticidad."

#: inc/core/doothemes/init.php:58
msgid "License is inactive."
msgstr "Licencia inactiva."

#: inc/core/doothemes/init.php:59
msgid "License key is disabled."
msgstr "Código de licencia deshabilitado."

#: inc/core/doothemes/init.php:60
msgid "Please activate a valid license."
msgstr "Por favor, active una licencia válida."

#: inc/core/doothemes/init.php:62
msgid ""
"Updating this theme will lose any customizations you have made. 'Cancel' to "
"stop, 'OK' to update."
msgstr ""
"La actualización de este tema podría perjudicar cualquier personalización "
"que haya realizado 'Cancelar' para detener, 'OK' para actualizar."

#: inc/core/doothemes/init.php:63
#, php-format
msgid ""
"<strong>%1$s %2$s</strong> is available. <a href=\"%3$s\" class=\"thickbox\" "
"title=\"%4s\">Check out what's new</a> or <a href=\"%5$s\"%6$s>update now</"
"a>."
msgstr ""
"<strong>%1$s %2$s</strong> está disponible. <a href=\"%3$s\" class=\"thickbox"
"\" title=\"%4s\">Registro de cambios</a> / <a href=\"%5$s\"%6$s>Actualizar "
"Ahora</a>."

#: inc/csf/options.blog_entries.php:8 inc/csf/options.customize.php:219
#: inc/csf/options.main_settings.php:215
msgid "Blog entries"
msgstr "Entradas del blog"

#: inc/csf/options.blog_entries.php:15 inc/csf/options.featured_titles.php:27
#: inc/csf/options.module_episodes.php:15 inc/csf/options.module_movies.php:15
#: inc/csf/options.module_seasons.php:15 inc/csf/options.module_tvshows.php:15
#: inc/csf/options.top_imdb.php:15
msgid "Module Title"
msgstr "Titulo del modulo"

#: inc/csf/options.blog_entries.php:16
msgid "Last entries"
msgstr "Ultimas Entradas"

#: inc/csf/options.blog_entries.php:17 inc/csf/options.featured_titles.php:29
#: inc/csf/options.module_episodes.php:17 inc/csf/options.module_movies.php:17
#: inc/csf/options.module_seasons.php:17 inc/csf/options.module_tvshows.php:17
#: inc/csf/options.top_imdb.php:17
msgid "Add title to show"
msgstr "Agregar titulo para mostrar"

#: inc/csf/options.blog_entries.php:22 inc/csf/options.featured_titles.php:34
#: inc/csf/options.main_slider.php:34 inc/csf/options.module_episodes.php:22
#: inc/csf/options.module_movies.php:22 inc/csf/options.module_seasons.php:22
#: inc/csf/options.module_tvshows.php:22 inc/csf/options.top_imdb.php:56
#: inc/widgets/content_related_widget.php:110 inc/widgets/content_widget.php:84
#: inc/widgets/content_widget_home.php:153
#: inc/widgets/content_widget_views.php:83
msgid "Items number"
msgstr "Numero de articulos"

#: inc/csf/options.blog_entries.php:23 inc/csf/options.featured_titles.php:35
#: inc/csf/options.main_slider.php:35 inc/csf/options.module_episodes.php:23
#: inc/csf/options.module_movies.php:23 inc/csf/options.module_seasons.php:23
#: inc/csf/options.module_tvshows.php:23 inc/csf/options.top_imdb.php:57
msgid "Number of items to show"
msgstr "Numero de artículos para mostrar"

#: inc/csf/options.blog_entries.php:33
msgid "Number of words"
msgstr "Número de palabras"

#: inc/csf/options.blog_entries.php:34
msgid "Number of words for describing the entry"
msgstr "Número de palabras para la descripción de la entrada"

#: inc/csf/options.comments.php:15
msgid "Comments system"
msgstr "Sistema de comentarios"

#: inc/csf/options.comments.php:18
msgid "WordPress"
msgstr "WordPress"

#: inc/csf/options.comments.php:19 inc/doo_init.php:712
msgid "Facebook"
msgstr "Facebook"

#: inc/csf/options.comments.php:20
msgid "Disqus"
msgstr "Disqus"

#: inc/csf/options.comments.php:27
msgid "Comments on pages"
msgstr "Comentarios en las páginas"

#: inc/csf/options.comments.php:28
msgid "Allow comments on all pages?"
msgstr "Habilitar comentarios en las paginas?"

#: inc/csf/options.comments.php:33
msgid "Facebook comments"
msgstr "Facebook comentarios"

#: inc/csf/options.comments.php:39
msgid "App ID"
msgstr "App ID"

#: inc/csf/options.comments.php:40
msgid "Insert you Facebook app ID"
msgstr "Insertar la ID de tu aplicación de Facebook"

#: inc/csf/options.comments.php:41
msgid "Facebook developers"
msgstr "Desarrolladores de Facebook"

#: inc/csf/options.comments.php:47
msgid "APP language"
msgstr "APP idioma"

#: inc/csf/options.comments.php:48
msgid "Select language for the app of facebook"
msgstr "Seleccionar idioma para la Aplicación de facebook"

#: inc/csf/options.comments.php:50
msgid "Afrikaans"
msgstr "Afrikaans"

#: inc/csf/options.comments.php:51
msgid "Akan"
msgstr "Akan"

#: inc/csf/options.comments.php:52
msgid "Amharic"
msgstr "Amárico"

#: inc/csf/options.comments.php:54
msgid "Assamese"
msgstr "Assamese"

#: inc/csf/options.comments.php:55
msgid "Aymara"
msgstr "Aymara"

#: inc/csf/options.comments.php:56
msgid "Azerbaijani"
msgstr "Azerbaijani"

#: inc/csf/options.comments.php:57
msgid "Belarusian"
msgstr "Belarusian"

#: inc/csf/options.comments.php:59
msgid "Bengali"
msgstr "Bengali"

#: inc/csf/options.comments.php:60
msgid "Breton"
msgstr "Breton"

#: inc/csf/options.comments.php:62
msgid "Catalan"
msgstr "Catalan"

#: inc/csf/options.comments.php:63
msgid "Sorani Kurdish"
msgstr "Sorani Kurdish"

#: inc/csf/options.comments.php:64
msgid "Cherokee"
msgstr "Cherokee"

#: inc/csf/options.comments.php:65
msgid "Corsican"
msgstr "Corsican"

#: inc/csf/options.comments.php:67
msgid "Cebuano"
msgstr "Cebuano"

#: inc/csf/options.comments.php:68
msgid "Welsh"
msgstr "Welsh"

#: inc/csf/options.comments.php:72
msgid "English (UK)"
msgstr "Ingles (UK)"

#: inc/csf/options.comments.php:73
msgid "English (India)"
msgstr "Ingles (India)"

#: inc/csf/options.comments.php:74
msgid "English (Pirate)"
msgstr "Ingles (Pirata)"

#: inc/csf/options.comments.php:75
msgid "English (Upside Down)"
msgstr "Ingles (Upside Down)"

#: inc/csf/options.comments.php:76
msgid "English (US)"
msgstr "Ingles (US)"

#: inc/csf/options.comments.php:77
msgid "Esperanto"
msgstr "Esperanto"

#: inc/csf/options.comments.php:78
msgid "Spanish (Chile)"
msgstr "Español (Chile)"

#: inc/csf/options.comments.php:79
msgid "Spanish (Colombia)"
msgstr "Español (Colombia)"

#: inc/csf/options.comments.php:80
msgid "Spanish (Spain)"
msgstr "Español (España)"

#: inc/csf/options.comments.php:81
msgid "Spanish (Latin America)"
msgstr "Español (Latino America)"

#: inc/csf/options.comments.php:82
msgid "Spanish (Mexico)"
msgstr "Español (México)"

#: inc/csf/options.comments.php:83
msgid "Spanish (Venezuela)"
msgstr "Español (Venezuela)"

#: inc/csf/options.comments.php:84
msgid "Estonian"
msgstr "Estonian"

#: inc/csf/options.comments.php:85
msgid "Basque"
msgstr "Basque"

#: inc/csf/options.comments.php:87
msgid "Leet Speak"
msgstr "Leet Speak"

#: inc/csf/options.comments.php:88
msgid "Fulah"
msgstr "Fulah"

#: inc/csf/options.comments.php:90
msgid "Faroese"
msgstr "Faroese"

#: inc/csf/options.comments.php:91
msgid "French (Canada)"
msgstr "Francés (Canada)"

#: inc/csf/options.comments.php:92
msgid "French (France)"
msgstr "Francés (Francia)"

#: inc/csf/options.comments.php:93
msgid "Frisian"
msgstr "Frisian"

#: inc/csf/options.comments.php:94
msgid "Irish"
msgstr "Irish"

#: inc/csf/options.comments.php:95
msgid "Galician"
msgstr "Galician"

#: inc/csf/options.comments.php:96
msgid "Guarani"
msgstr "Guarani"

#: inc/csf/options.comments.php:97
msgid "Gujarati"
msgstr "Gujarati"

#: inc/csf/options.comments.php:98
msgid "Classical Greek"
msgstr "Classical Greek"

#: inc/csf/options.comments.php:99
msgid "Hausa"
msgstr "Hausa"

#: inc/csf/options.comments.php:101 inc/doo_player.php:54
msgid "Hindi"
msgstr "Hindi"

#: inc/csf/options.comments.php:104
msgid "Armenian"
msgstr "Armenio"

#: inc/csf/options.comments.php:106
msgid "Igbo"
msgstr "Igbo"

#: inc/csf/options.comments.php:109 inc/doo_player.php:56
msgid "Japanese"
msgstr "Japonés"

#: inc/csf/options.comments.php:110
msgid "Japanese (Kansai)"
msgstr "Japonés (Kansai)"

#: inc/csf/options.comments.php:111
msgid "Javanese"
msgstr "Javanese"

#: inc/csf/options.comments.php:112
msgid "Georgian"
msgstr "Georgian"

#: inc/csf/options.comments.php:113
msgid "Kazakh"
msgstr "Kazakh"

#: inc/csf/options.comments.php:114
msgid "Khmer"
msgstr "Khmer"

#: inc/csf/options.comments.php:115
msgid "Kannada"
msgstr "Kannada"

#: inc/csf/options.comments.php:117
msgid "Kurdish (Kurmanji)"
msgstr "Kurdish (Kurmanji)"

#: inc/csf/options.comments.php:118
msgid "Kyrgyz"
msgstr "Kyrgyz"

#: inc/csf/options.comments.php:119
msgid "Latin"
msgstr "Latin"

#: inc/csf/options.comments.php:120
msgid "Ganda"
msgstr "Ganda"

#: inc/csf/options.comments.php:121
msgid "Limburgish"
msgstr "Limburgish"

#: inc/csf/options.comments.php:122
msgid "Lingala"
msgstr "Lingala"

#: inc/csf/options.comments.php:123
msgid "Lao"
msgstr "Lao"

#: inc/csf/options.comments.php:125
msgid "Latvian"
msgstr "Latvian"

#: inc/csf/options.comments.php:126
msgid "Malagasy"
msgstr "Malagasy"

#: inc/csf/options.comments.php:127
msgid "Maori"
msgstr "Maori"

#: inc/csf/options.comments.php:128
msgid "Macedonian"
msgstr "Macedonian"

#: inc/csf/options.comments.php:129
msgid "Malayalam"
msgstr "Malayalam"

#: inc/csf/options.comments.php:130
msgid "Mongolian"
msgstr "Mongol"

#: inc/csf/options.comments.php:131
msgid "Marathi"
msgstr "Marathi"

#: inc/csf/options.comments.php:132
msgid "Malay"
msgstr "Malay"

#: inc/csf/options.comments.php:133
msgid "Maltese"
msgstr "Maltese"

#: inc/csf/options.comments.php:134
msgid "Burmese"
msgstr "Burmese"

#: inc/csf/options.comments.php:135
msgid "Norwegian (bokmal)"
msgstr "Norwegian (bokmal)"

#: inc/csf/options.comments.php:136
msgid "Ndebele"
msgstr "Ndebele"

#: inc/csf/options.comments.php:137
msgid "Nepali"
msgstr "Nepali"

#: inc/csf/options.comments.php:138
msgid "Dutch (Belgie)"
msgstr "Dutch (Belgie)"

#: inc/csf/options.comments.php:140
msgid "Norwegian (nynorsk)"
msgstr "Norwegian (nynorsk)"

#: inc/csf/options.comments.php:141
msgid "Chewa"
msgstr "Chewa"

#: inc/csf/options.comments.php:142
msgid "Oriya"
msgstr "Oriya"

#: inc/csf/options.comments.php:143
msgid "Punjabi"
msgstr "Punjabi"

#: inc/csf/options.comments.php:145
msgid "Pashto"
msgstr "Pashto"

#: inc/csf/options.comments.php:146
msgid "Portuguese (Brazil)"
msgstr "Portugués (Brasil)"

#: inc/csf/options.comments.php:147
msgid "Portuguese (Portugal)"
msgstr "Portugués (Portugal)"

#: inc/csf/options.comments.php:148
msgid "Quechua"
msgstr "Quechua"

#: inc/csf/options.comments.php:149
msgid "Romansh"
msgstr "Romansh"

#: inc/csf/options.comments.php:152
msgid "Kinyarwanda"
msgstr "Kinyarwanda"

#: inc/csf/options.comments.php:153
msgid "Sanskrit"
msgstr "Sanskrit"

#: inc/csf/options.comments.php:154
msgid "Sardinian"
msgstr "Sardinian"

#: inc/csf/options.comments.php:155
msgid "Northern Sami"
msgstr "Northern Sami"

#: inc/csf/options.comments.php:156
msgid "Sinhala"
msgstr "Sinhala"

#: inc/csf/options.comments.php:158
msgid "Slovenian"
msgstr "Slovenian"

#: inc/csf/options.comments.php:159
msgid "Shona"
msgstr "Shona"

#: inc/csf/options.comments.php:160
msgid "Somali"
msgstr "Somali"

#: inc/csf/options.comments.php:161
msgid "Albanian"
msgstr "Albanés"

#: inc/csf/options.comments.php:162
msgid "Serbian"
msgstr "Serbian"

#: inc/csf/options.comments.php:164
msgid "Swahili"
msgstr "Swahili"

#: inc/csf/options.comments.php:165
msgid "Syriac"
msgstr "Syriac"

#: inc/csf/options.comments.php:166
msgid "Silesian"
msgstr "Silesian"

#: inc/csf/options.comments.php:167
msgid "Tamil"
msgstr "Tamil"

#: inc/csf/options.comments.php:168
msgid "Telugu"
msgstr "Telugu"

#: inc/csf/options.comments.php:169
msgid "Tajik"
msgstr "Tajik"

#: inc/csf/options.comments.php:171
msgid "Turkmen"
msgstr "Turkmen"

#: inc/csf/options.comments.php:172
msgid "Filipino"
msgstr "Filipino"

#: inc/csf/options.comments.php:173
msgid "Klingon"
msgstr "Klingon"

#: inc/csf/options.comments.php:175
msgid "Tatar"
msgstr "Tatar"

#: inc/csf/options.comments.php:176
msgid "Tamazight"
msgstr "Tamazight"

#: inc/csf/options.comments.php:178
msgid "Urdu"
msgstr "Urdu"

#: inc/csf/options.comments.php:179
msgid "Uzbek"
msgstr "Uzbek"

#: inc/csf/options.comments.php:181
msgid "Wolof"
msgstr "Wolof"

#: inc/csf/options.comments.php:182
msgid "Xhosa"
msgstr "Xhosa"

#: inc/csf/options.comments.php:183
msgid "Yiddish"
msgstr "Yiddish"

#: inc/csf/options.comments.php:184
msgid "Yoruba"
msgstr "Yoruba"

#: inc/csf/options.comments.php:185
msgid "Simplified Chinese (China)"
msgstr "Chino simplificado (China)"

#: inc/csf/options.comments.php:186
msgid "Traditional Chinese (Hong Kong)"
msgstr "Chino tradicional (Hong Kong)"

#: inc/csf/options.comments.php:187
msgid "Traditional Chinese (Taiwan)"
msgstr "Chino tradicional (Taiwán)"

#: inc/csf/options.comments.php:188
msgid "Zulu"
msgstr "Zulu"

#: inc/csf/options.comments.php:189
msgid "Zazaki"
msgstr "Zazaki"

#: inc/csf/options.comments.php:197
msgid "Admin user"
msgstr "Admin user"

#: inc/csf/options.comments.php:198
msgid "Add user or user ID to manage comment"
msgstr "Agregar usuario o identificador de usuario para administrar comentario"

#: inc/csf/options.comments.php:204 inc/csf/options.customize.php:31
msgid "Color Scheme"
msgstr "Combinación de colores"

#: inc/csf/options.comments.php:207
msgid "Light color"
msgstr "Colores claros"

#: inc/csf/options.comments.php:208
msgid "Dark color"
msgstr "Colores oscuros"

#: inc/csf/options.comments.php:215
msgid "Number of Posts"
msgstr "Número de publicaciones"

#: inc/csf/options.comments.php:225
msgid "Disqus comments"
msgstr "Disqus comentarios"

#: inc/csf/options.comments.php:231
msgid "Shortname"
msgstr "Shorname"

#: inc/csf/options.comments.php:232
msgid ""
"This is used to uniquely identify your website on Disqus. It cannot be "
"changed"
msgstr ""
"Esto se utiliza para identificar de forma única su sitio web en Disqus. No "
"se puede cambiar"

#: inc/csf/options.comments.php:233 inc/parts/comments.php:32
msgid "more info"
msgstr "mas información"

#: inc/csf/options.comments.php:238
msgid "Discussion Settings"
msgstr "Ajustes de comentarios"

#: inc/csf/options.cookies.php:9
msgid "Cookies Law"
msgstr "Ley de Cookies"

#: inc/csf/options.cookies.php:17
msgid "Cookie Key"
msgstr "Clave de cookies"

#: inc/csf/options.cookies.php:22
msgid "This Website uses cookies to make its website easier to use."
msgstr "Este sitio web utiliza cookies para facilitar el uso de su sitio web."

#: inc/csf/options.cookies.php:23
msgid "Text notice"
msgstr "Aviso de texto"

#: inc/csf/options.cookies.php:28
msgid "Learn more about cookies."
msgstr "Obtén más información sobre las cookies."

#: inc/csf/options.cookies.php:29
msgid "Text Read more"
msgstr "Texto Leer más"

#: inc/csf/options.cookies.php:34
msgid "Details page type"
msgstr "Tipo de página de detalles"

#: inc/csf/options.cookies.php:37
msgid "Page link"
msgstr "Enlace de página"

#: inc/csf/options.cookies.php:38 inc/csf/options.cookies.php:51
msgid "Custom Link"
msgstr "Enlace personalizado"

#: inc/csf/options.cookies.php:44 inc/csf/options.cookies.php:59
msgid "Page Link"
msgstr "Link de la página"

#: inc/csf/options.cookies.php:66
msgid "none"
msgstr "ninguno"

#: inc/csf/options.cookies.php:71
msgid "Opens the linked document in a new window or tab."
msgstr "Abre el documento vinculado en una nueva ventana o pestaña."

#: inc/csf/options.cookies.php:76
msgid ""
"Opens the linked document in the same frame as it was clicked (this is "
"default)."
msgstr ""
"Abre el documento vinculado en el misma ventana en el que se ha hace clic "
"(esto es el valor predeterminado)."

#: inc/csf/options.cookies.php:81
msgid "Opens the linked document in the parent frame."
msgstr "Abre el documento vinculado en la ventana primaria."

#: inc/csf/options.cookies.php:86
msgid "Opens the linked document in the full body of the window."
msgstr "Abre el documento vinculado en todo el cuerpo de la ventana."

#: inc/csf/options.cookies.php:92
msgid "Cookie expiry"
msgstr "Caducidad de la Cookie"

#: inc/csf/options.cookies.php:95
msgid "1 week"
msgstr "1 semana"

#: inc/csf/options.cookies.php:96
msgid "1 month"
msgstr "1 mes"

#: inc/csf/options.cookies.php:97
msgid "3 months"
msgstr "3 meses"

#: inc/csf/options.cookies.php:98
msgid "6 months"
msgstr "6 meses"

#: inc/csf/options.cookies.php:99
msgid "1 year"
msgstr "1 año"

#: inc/csf/options.cookies.php:100
msgid "infinity"
msgstr "infinidad"

#: inc/csf/options.cookies.php:105
msgid "Customize Notice"
msgstr "Personalizar aviso"

#: inc/csf/options.cookies.php:110
msgid "Background color"
msgstr "Color de fondo"

#: inc/csf/options.cookies.php:116
msgid "Text color"
msgstr "Color del texto"

#: inc/csf/options.cookies.php:122
msgid "Link color"
msgstr "Color de los enlaces"

#: inc/csf/options.cookies.php:128
msgid "Link hover color"
msgstr "Color de enlace al pasar el cursor"

#: inc/csf/options.customize.php:24
msgid "Customize"
msgstr "Personalizar"

#: inc/csf/options.customize.php:32
msgid "Select the default color scheme"
msgstr "Seleccionar combinación de colores por defecto"

#: inc/csf/options.customize.php:43
msgid "Max width"
msgstr "Ancho máximo"

#: inc/csf/options.customize.php:44
msgid "Set max width of the page"
msgstr "Establecer el ancho máximo de la página"

#: inc/csf/options.customize.php:54
msgid "Single background"
msgstr "Single background"

#: inc/csf/options.customize.php:55 inc/csf/options.main_settings.php:120
msgid "Check whether to activate or deactivate"
msgstr "Compruebe si se debe activar o desactivar"

#: inc/csf/options.customize.php:56
msgid "Enable dynamic background"
msgstr "Habilitar fondo dinámico"

#: inc/csf/options.customize.php:62
msgid "Font family"
msgstr "Font family"

#: inc/csf/options.customize.php:63
msgid "Select font-family by Google Fonts"
msgstr "Seleccionar font-family por Google Fonts"

#: inc/csf/options.customize.php:94
msgid "Primary color"
msgstr "Color principal"

#: inc/csf/options.customize.php:95 inc/csf/options.customize.php:102
#: inc/csf/options.customize.php:109 inc/csf/options.customize.php:121
#: inc/csf/options.customize.php:129 inc/csf/options.customize.php:137
#: inc/csf/options.video_player.php:131
msgid "Choose a color"
msgstr "Elegir color"

#: inc/csf/options.customize.php:101
msgid "Background container"
msgstr "Fondo del contenedor"

#: inc/csf/options.customize.php:108
msgid "Featured marker"
msgstr "Marcador destacado"

#: inc/csf/options.customize.php:114
msgid "Fusion alternative colors"
msgstr "Colores alternativos de fusión"

#: inc/csf/options.customize.php:120
msgid "Background header bar"
msgstr "Fondo header bar"

#: inc/csf/options.customize.php:128
msgid "Header link color"
msgstr "Color de enlace en el header"

#: inc/csf/options.customize.php:136
msgid "Header link hover color"
msgstr "Color de enlace en hover para el header"

#: inc/csf/options.customize.php:143
msgid "Poster Play icon"
msgstr "Poster Play icon"

#: inc/csf/options.customize.php:148
msgid "Hover Play Icon"
msgstr "Hover Play Icon"

#: inc/csf/options.customize.php:159
msgid "Sidebar position"
msgstr "Posición de la barra lateral"

#: inc/csf/options.customize.php:164
msgid "Home page"
msgstr "Página de inicio"

#: inc/csf/options.customize.php:174
msgid "Archives"
msgstr "Archivos"

#: inc/csf/options.customize.php:184 inc/parts/admin/ads_tool.php:10
msgid "Single Post"
msgstr "Entrada individual"

#: inc/csf/options.customize.php:193 inc/parts/admin/ads_tool.php:9
msgid "Homepage"
msgstr "Pagina de inicio"

#: inc/csf/options.customize.php:198
msgid "Full width"
msgstr "Ancho completo"

#: inc/csf/options.customize.php:199
msgid "Enable full width only in homepage"
msgstr "Habilitar el ancho completo sólo en página de inicio"

#: inc/csf/options.customize.php:204
msgid ""
"<strong>NOTE:</strong> Drag and drop the modules in the order you want them"
msgstr ""
"<strong>NOTA:</strong> Puede arrastrar y soltar los módulos en el orden de "
"su preferencia"

#: inc/csf/options.customize.php:211
msgid "Slider"
msgstr "Slider"

#: inc/csf/options.customize.php:212 inc/csf/options.featured_titles.php:20
#: inc/csf/options.featured_titles.php:28
msgid "Featured titles"
msgstr "Títulos destacados"

#: inc/csf/options.customize.php:214 inc/csf/options.more.php:79
msgid "Advertising"
msgstr "Anuncios"

#: inc/csf/options.customize.php:216
msgid "TV Show > Season"
msgstr "Series > Temporada"

#: inc/csf/options.customize.php:217
msgid "TV Show > Episode"
msgstr "Series >Episodio"

#: inc/csf/options.customize.php:218 inc/csf/options.top_imdb.php:8
#: inc/csf/options.top_imdb.php:16 inc/doo_database.php:114
msgid "TOP IMDb"
msgstr "Raking IMDb"

#: inc/csf/options.customize.php:222
msgid "Genres Widget"
msgstr "Widget de Géneros"

#: inc/csf/options.customize.php:223
msgid "Slider Movies"
msgstr "Slider Movies"

#: inc/csf/options.customize.php:224
msgid "Slider TV Shows"
msgstr "Slider TV Shows"

#: inc/csf/options.customize.php:227
msgid "Modules enabled"
msgstr "Módulos habilitados"

#: inc/csf/options.customize.php:228
msgid "Modules disabled"
msgstr "Módulos deshabilitados"

#: inc/csf/options.customize.php:232
msgid "Customize logos"
msgstr "Personalizar logos"

#: inc/csf/options.customize.php:237
msgid "Logo header"
msgstr "Logo header"

#: inc/csf/options.customize.php:238 inc/csf/options.customize.php:256
msgid "Upload your logo using the Upload Button"
msgstr "Sube tu logo usando el botón Carga"

#: inc/csf/options.customize.php:243
msgid "Favicon"
msgstr "Favicon"

#: inc/csf/options.customize.php:244
msgid "Upload a 16 x 16 px image that will represent your website's favicon"
msgstr "Cargar una imagen de 16*16 pixeles"

#: inc/csf/options.customize.php:249
msgid "Touch icon APP"
msgstr "Icono táctil APP"

#: inc/csf/options.customize.php:250
msgid "Upload a 152 x 152 px image that will represent your Web APP"
msgstr "Subir una imagen de 152 x 152 px que representará tu aplicación Web"

#: inc/csf/options.customize.php:255
msgid "Login / Register / WP-Admin"
msgstr "Inicio de sesión / Registro / WP-Admin Login"

#: inc/csf/options.customize.php:260
msgid "Footer settings"
msgstr "Ajustes del footer"

#: inc/csf/options.customize.php:265
msgid "Footer"
msgstr "Footer"

#: inc/csf/options.customize.php:268
msgid "Simple"
msgstr "Simple"

#: inc/csf/options.customize.php:269
msgid "Complete"
msgstr "Completo"

#: inc/csf/options.customize.php:275
msgid "Footer copyright"
msgstr "Footer copyright"

#: inc/csf/options.customize.php:276
msgid "Modify or remove copyright text"
msgstr "Modificar o eliminar texto de copyright"

#: inc/csf/options.customize.php:281
msgid "Logo footer"
msgstr "Logo footer"

#: inc/csf/options.customize.php:287
msgid "Footer text"
msgstr "Texto del footer"

#: inc/csf/options.customize.php:288
msgid "Text under footer logo"
msgstr "Texto bajo el logotipo del footer"

#: inc/csf/options.customize.php:294
msgid "Title column 1"
msgstr "Título columna 1"

#: inc/csf/options.customize.php:295 inc/csf/options.customize.php:302
#: inc/csf/options.customize.php:309
msgid "Footer menu"
msgstr "Footer menu"

#: inc/csf/options.customize.php:301
msgid "Title column 2"
msgstr "Título columna 2"

#: inc/csf/options.customize.php:308
msgid "Title column 3"
msgstr "Título columna 3"

#: inc/csf/options.customize.php:314
msgid "Custom CSS"
msgstr "CSS Personalizado"

#: inc/csf/options.customize.php:323
msgid "Add only CSS code"
msgstr "Añadir solo código CSS"

#: inc/csf/options.featured_titles.php:10
msgid "Homepage Modules"
msgstr "Página de inicio"

#: inc/csf/options.featured_titles.php:45
msgid "Module control"
msgstr "Control del modulo"

#: inc/csf/options.featured_titles.php:46
#: inc/csf/options.module_episodes.php:34 inc/csf/options.module_movies.php:34
#: inc/csf/options.module_seasons.php:34 inc/csf/options.module_tvshows.php:34
msgid "Check to enable option."
msgstr "Marcar para activar la opción."

#: inc/csf/options.featured_titles.php:48
#: inc/csf/options.module_episodes.php:36 inc/csf/options.module_movies.php:36
#: inc/csf/options.module_seasons.php:36 inc/csf/options.module_tvshows.php:36
msgid "Activate Slider"
msgstr "Activar slider"

#: inc/csf/options.featured_titles.php:49
#: inc/csf/options.module_episodes.php:37 inc/csf/options.module_movies.php:37
#: inc/csf/options.module_seasons.php:37 inc/csf/options.module_tvshows.php:37
msgid "Auto play Slider"
msgstr "Autoplay Slider"

#: inc/csf/options.featured_titles.php:56 inc/csf/options.main_slider.php:46
#: inc/csf/options.module_episodes.php:44 inc/csf/options.module_movies.php:44
#: inc/csf/options.module_seasons.php:44 inc/csf/options.module_tvshows.php:44
msgid "Order by"
msgstr "Ordenar por"

#: inc/csf/options.featured_titles.php:57 inc/csf/options.main_slider.php:47
#: inc/csf/options.module_episodes.php:45 inc/csf/options.module_movies.php:45
#: inc/csf/options.module_seasons.php:45 inc/csf/options.module_tvshows.php:45
msgid "Order items for this module"
msgstr "Ordenar artículos para este módulo"

#: inc/csf/options.featured_titles.php:60 inc/csf/options.main_slider.php:50
#: inc/csf/options.module_episodes.php:48 inc/csf/options.module_movies.php:48
#: inc/csf/options.module_seasons.php:48 inc/csf/options.module_tvshows.php:48
msgid "Post date"
msgstr "Fecha de publicación"

#: inc/csf/options.featured_titles.php:61 inc/csf/options.main_slider.php:51
#: inc/csf/options.module_episodes.php:49 inc/csf/options.module_movies.php:49
#: inc/csf/options.module_seasons.php:49 inc/csf/options.module_tvshows.php:49
msgid "Post title"
msgstr "Titulo de publicación"

#: inc/csf/options.featured_titles.php:62 inc/csf/options.main_slider.php:52
#: inc/csf/options.module_episodes.php:50 inc/csf/options.module_movies.php:50
#: inc/csf/options.module_seasons.php:50 inc/csf/options.module_tvshows.php:50
msgid "Last modified"
msgstr "Última modificación"

#: inc/csf/options.featured_titles.php:63 inc/csf/options.main_slider.php:53
#: inc/csf/options.module_episodes.php:51 inc/csf/options.module_movies.php:51
#: inc/csf/options.module_seasons.php:51 inc/csf/options.module_tvshows.php:51
msgid "Random entry"
msgstr "Orden aleatorio"

#: inc/csf/options.featured_titles.php:69 inc/csf/options.main_slider.php:60
#: inc/csf/options.module_episodes.php:57 inc/csf/options.module_movies.php:57
#: inc/csf/options.module_seasons.php:57 inc/csf/options.module_tvshows.php:57
msgid "Order"
msgstr "Orden"

#: inc/csf/options.links_module.php:8
msgid "Links Module"
msgstr "Módulo de enlaces"

#: inc/csf/options.links_module.php:15
msgid "Set languages"
msgstr "Establecer idiomas"

#: inc/csf/options.links_module.php:16 inc/csf/options.links_module.php:25
msgid "Add comma separated values"
msgstr "Añadir elementos separados por comas sin espacios"

#: inc/csf/options.links_module.php:24
msgid "Set resolutions quality"
msgstr "Establecer resoluciones de calidad"

#: inc/csf/options.links_module.php:33
msgid "Front-End Links publishers"
msgstr "Publicadores de enlace en Frontend"

#: inc/csf/options.links_module.php:34
msgid "Check the user roles that can be published from Front-end"
msgstr "Marcar los roles de usuario que pueden publicar enlaces desde frontend"

#: inc/csf/options.links_module.php:47
msgid "Auto Publish"
msgstr "Publicación automática"

#: inc/csf/options.links_module.php:48
msgid "Mark the roles of users who can post links without being moderated"
msgstr ""
"Marque los roles de los usuarios que pueden publicar enlaces sin ser "
"moderados"

#: inc/csf/options.links_module.php:61
msgid "Show in list"
msgstr "Mostrar en la lista"

#: inc/csf/options.links_module.php:62
msgid "Select the items that you want to show in the links table"
msgstr "Seleccione los elementos que desea mostrar en la tabla enlaces"

#: inc/csf/options.links_module.php:66 inc/doo_links.php:566
#: inc/parts/links_editor.php:48
msgid "Size"
msgstr "Tamaño"

#: inc/csf/options.links_module.php:67 inc/doo_links.php:567
#: inc/doo_links.php:712 inc/parts/links_editor.php:49
#: pages/sections/account.php:105 pages/sections/account.php:130
msgid "Clicks"
msgstr "Clicks"

#: inc/csf/options.links_module.php:69 inc/doo_links.php:569
#: inc/parts/links_editor.php:50 pages/sections/account.php:129
msgid "User"
msgstr "Usuario"

#: inc/csf/options.links_module.php:76
msgid "Links Editor"
msgstr "Editor de enlaces"

#: inc/csf/options.links_module.php:77
msgid "Show link editor, if the main entry has not yet been published"
msgstr ""
"Mostrar editor de enlaces, si la entrada principal aún no ha sido publicada"

#: inc/csf/options.links_module.php:82
msgid ""
"This is not a secure method of adding links, there is a very high "
"probability of data loss."
msgstr ""
"Este no es un método seguro para agregar enlaces, hay una probabilidad muy "
"alta de pérdida de datos."

#: inc/csf/options.links_module.php:87
msgid "Redirection page"
msgstr "Página de redireccionamiento"

#: inc/csf/options.links_module.php:92 inc/csf/options.video_player.php:59
msgid "Timeout"
msgstr "Tiempo de espera"

#: inc/csf/options.links_module.php:93
msgid "Timeout in seconds before redirecting the page automatically"
msgstr ""
"Tiempo de espera en segundos antes de redirigir la página automáticamente"

#: inc/csf/options.links_module.php:103
msgid "Type Output"
msgstr "Tipo de salida"

#: inc/csf/options.links_module.php:104
msgid "Select an output type upon completion of the wait time"
msgstr "Seleccione un tipo de salida al finalizar el tiempo de espera"

#: inc/csf/options.links_module.php:106
msgid "Clicking on a button"
msgstr "Hacer clic en un botón"

#: inc/csf/options.links_module.php:107
msgid "Redirecting the page automatically"
msgstr "Redireccionar la página automáticamente"

#: inc/csf/options.links_module.php:115
msgid "Button text"
msgstr "Texto del botón"

#: inc/csf/options.links_module.php:116
msgid "Customize the button"
msgstr "Personalizar el botón"

#: inc/csf/options.links_module.php:123
msgid "Text under button"
msgstr "Texto bajo el botón"

#: inc/csf/options.links_module.php:124 single-dt_links.php:35
msgid "Click on the button to continue"
msgstr "Haga clic en el botón para continuar"

#: inc/csf/options.links_module.php:130 inc/csf/options.video_player.php:130
msgid "Main color"
msgstr "Color principal"

#: inc/csf/options.links_module.php:131
msgid "Select a color for customize redirection page"
msgstr "Seleccione un color para personalizar la página de redireccionamiento"

#: inc/csf/options.links_module.php:137
msgid "Shorteners"
msgstr "Acortadores"

#: inc/csf/options.links_module.php:142
msgid "To obtain the link, use the <code>{{url}}</code> tag"
msgstr "Para obtener el enlace, utilice la etiqueta <code> {{url}} </code>"

#: inc/csf/options.links_module.php:143
msgid "To invalidate this function do not add any shortener"
msgstr ""
"Para invalidar esta función no agregue ningún acortador y con eso será "
"suficiente"

#: inc/csf/options.links_module.php:149 inc/csf/options.links_module.php:150
msgid "Add new shortener"
msgstr "Agregar nuevo acortador"

#: inc/csf/options.main_settings.php:20
msgid "Main settings"
msgstr "Configuración principal"

#: inc/csf/options.main_settings.php:27
msgid "Site Online"
msgstr "Sitio en línea"

#: inc/csf/options.main_settings.php:28
msgid "Keep this field activated"
msgstr "Mantener este campo activado"

#: inc/csf/options.main_settings.php:34
msgid "Currently your website is in <strong>offline mode</strong>"
msgstr "Actualmente su sitio web está en <strong>Modo fuera de línea</strong>"

#: inc/csf/options.main_settings.php:40
msgid "Offline Message"
msgstr "Mensaje fuera de línea"

#: inc/csf/options.main_settings.php:41
msgid "We are in maintenance, please try it later"
msgstr "Estamos en mantenimiento, por favor inténtelo más tarde"

#: inc/csf/options.main_settings.php:43
msgid "Offline mode message here"
msgstr "Mensaje de modo fuera de línea aquí"

#: inc/csf/options.main_settings.php:51
msgid "Classic Editor"
msgstr "Editor clásico"

#: inc/csf/options.main_settings.php:52
msgid "Enable classic editor in content editors"
msgstr "Habilitar editor clásico en editores de contenido"

#: inc/csf/options.main_settings.php:57
msgid "WordPress Gutenberg editor has been disabled"
msgstr "WordPress Gutenberg editor ha sido desactivado"

#: inc/csf/options.main_settings.php:63
msgid "Google Analytics"
msgstr "Google Analytics"

#: inc/csf/options.main_settings.php:64
msgid "Insert tracking code to use this function"
msgstr "Insertar código de seguimiento para usar esta función"

#: inc/csf/options.main_settings.php:73
msgid "Items per page"
msgstr "Artículos por página"

#: inc/csf/options.main_settings.php:74 inc/csf/options.main_settings.php:85
msgid "Archive pages show at most"
msgstr "Las páginas de archivo muestran como máximo"

#: inc/csf/options.main_settings.php:84
msgid "Post per page in blog"
msgstr "Entradas por pagina en Blog"

#: inc/csf/options.main_settings.php:95
msgid "TOP IMDb items"
msgstr "Artículos TOP IMDb"

#: inc/csf/options.main_settings.php:96
msgid "Select the number of items to the page TOP IMDb"
msgstr "Seleccione el número de elementos a la página TOP IMDb"

#: inc/csf/options.main_settings.php:106
msgid "Pagination Range"
msgstr "Rango de paginación"

#: inc/csf/options.main_settings.php:107
msgid "Set a range of items to display in the paginator"
msgstr "Establecer un rango de elementos para mostrar en el paginador"

#: inc/csf/options.main_settings.php:122
msgid "User register enable"
msgstr "Habilitar registro de usuario"

#: inc/csf/options.main_settings.php:123
msgid "Live search enable"
msgstr "Habilitar Búsqueda en Tiempo Real"

#: inc/csf/options.main_settings.php:124
msgid "Show similar titles enable"
msgstr "Habilitar títulos similares"

#: inc/csf/options.main_settings.php:125
msgid "Emoji disable"
msgstr "Desactivar los emoji"

#: inc/csf/options.main_settings.php:126
msgid "Minify HTML"
msgstr "Minificar HTML"

#: inc/csf/options.main_settings.php:127
msgid "Show Letters glossary"
msgstr "Mostrar glosario de letras"

#: inc/csf/options.main_settings.php:134
msgid "View count"
msgstr "Conteo de vistas"

#: inc/csf/options.main_settings.php:135
msgid "Methods for counting views in content"
msgstr "Métodos para contar vistas en el contenido"

#: inc/csf/options.main_settings.php:138
msgid "Regular"
msgstr "Regular"

#: inc/csf/options.main_settings.php:139
msgid "Ajax"
msgstr "Ajax"

#: inc/csf/options.main_settings.php:140
msgid "Disable view counting"
msgstr "Deshabilitar el conteo de vistas"

#: inc/csf/options.main_settings.php:146
msgid ""
"Regular view count may consume resources from your server in a moderate way, "
"consider disabling it if your server has limited processes."
msgstr ""
"El conteo de vistas regular puede consumir recursos de su servidor de forma "
"moderada, considere desactivarlo si su servidor tiene procesos limitados."

#: inc/csf/options.main_settings.php:152
msgid ""
"View count by Ajax consumes resources from your server on each user visit, "
"if your server has limited processes we recommend disabling this function."
msgstr ""
"El conteo de vistas por Ajax consume recursos de su servidor en cada visita "
"de usuario, si su servidor tiene procesos limitados le recomendamos "
"desactivar esta función."

#: inc/csf/options.main_settings.php:157
msgid "Google reCAPTCHA v3"
msgstr "Google reCAPTCHA v3"

#: inc/csf/options.main_settings.php:162
msgid "Site key"
msgstr "Clave del sitio"

#: inc/csf/options.main_settings.php:167
msgid "Secret key"
msgstr "Clave secreta"

#: inc/csf/options.main_settings.php:171
msgid "Get Google reCAPTCHA"
msgstr "Obtener Google reCAPTCHA"

#: inc/csf/options.main_settings.php:175
msgid "Pages for DooPlay"
msgstr "Páginas para DooPlay"

#: inc/csf/options.main_settings.php:180 inc/doo_database.php:99
msgid "Account"
msgstr "Cuenta"

#: inc/csf/options.main_settings.php:181
msgid "Set User Account page"
msgstr "Establecer página de cuenta de usuario"

#: inc/csf/options.main_settings.php:187 inc/doo_database.php:89
#: pages/trending.php:25
msgid "Trending"
msgstr "Tendencias"

#: inc/csf/options.main_settings.php:188
msgid "Set page to show trend content"
msgstr "Definir página para mostrar contenido en tendencia"

#: inc/csf/options.main_settings.php:194 inc/doo_database.php:94
#: pages/rating.php:24
msgid "Ratings"
msgstr "Ratings"

#: inc/csf/options.main_settings.php:195
msgid "Set page to show content rated by users"
msgstr "Definir página para mostrar el contenido calificado por los usuarios"

#: inc/csf/options.main_settings.php:202
msgid "Set page to display the contact form"
msgstr "Establecer la página para mostrar el formulario de contacto"

#: inc/csf/options.main_settings.php:208
msgid "Top IMDb"
msgstr "Top IMDb"

#: inc/csf/options.main_settings.php:209
msgid "Set page to show the best qualified content in IMDb"
msgstr "Defina la página para mostrar el mejor contenido calificado en IMDb"

#: inc/csf/options.main_settings.php:216
msgid "Set page to show the entries in blog"
msgstr "Establecer la página para mostrar las entradas en el blog"

#: inc/csf/options.main_settings.php:221
msgid "Database cache"
msgstr "Caché de base de datos"

#: inc/csf/options.main_settings.php:226
msgid "Scheduler"
msgstr "Programar"

#: inc/csf/options.main_settings.php:227
msgid "Cache cleaning"
msgstr "Limpieza de caché"

#: inc/csf/options.main_settings.php:228
msgid "It is important to clean expired cache at least once a day"
msgstr ""
"Es importante limpiar la memoria caché caducada al menos una vez al día"

#: inc/csf/options.main_settings.php:231
msgid "Daily"
msgstr "Diariamente"

#: inc/csf/options.main_settings.php:232
msgid "Twice daily"
msgstr "Dos veces al día"

#: inc/csf/options.main_settings.php:233
msgid "Hourly"
msgstr "Cada hora"

#: inc/csf/options.main_settings.php:239
msgid "Storing cache as long as possible can be a very good idea"
msgstr ""
"Almacenar la caché el mayor tiempo posible puede ser una muy buena idea"

#: inc/csf/options.main_settings.php:245
msgid "Cache Timeout"
msgstr "Tiempo de espera de caché"

#: inc/csf/options.main_settings.php:246
msgid "Set the time in seconds"
msgstr "Establecer el tiempo en segundos"

#: inc/csf/options.main_settings.php:248
msgid "We recommend storing this cache for at least 86400 seconds"
msgstr "Se recomienda almacenar esta caché durante al menos 86400 segundos"

#: inc/csf/options.main_slider.php:8
msgid "Main Slider"
msgstr "Slider principal"

#: inc/csf/options.main_slider.php:15
msgid "Get content automatically"
msgstr "Obtener contenido automáticamente"

#: inc/csf/options.main_slider.php:22
msgid "Select the type of content you want to display"
msgstr "Seleccione el tipo de contenido que desea mostrar"

#: inc/csf/options.main_slider.php:25 inc/csf/options.top_imdb.php:25
msgid "Movies and TV Shows"
msgstr "Películas y Series"

#: inc/csf/options.main_slider.php:26 inc/csf/options.top_imdb.php:26
msgid "Only Movies"
msgstr "Solo peliculas"

#: inc/csf/options.main_slider.php:27 inc/csf/options.top_imdb.php:27
msgid "Only TV Shows"
msgstr "Solo series"

#: inc/csf/options.main_slider.php:71
msgid ""
"To show content in the Slider you must add manually using only the ID "
"numbers of each publication you want to show."
msgstr ""
"Para mostrar contenido en el Slider Principal, debera agregar manualmente "
"solo los números de ID de cada publicación que desee mostrar."

#: inc/csf/options.main_slider.php:77
msgid "Posts ID"
msgstr "ID de publicación"

#: inc/csf/options.main_slider.php:78
msgid "Use the numeric IDs of the posts."
msgstr "Utilice los ID numéricos de las publicaciones."

#: inc/csf/options.main_slider.php:83
msgid ""
"Numeric IDs must be separated by a comma, use only numeric IDs of content "
"that are established as Movies or TV Shows."
msgstr ""
"Los identificadores numéricos deben estar separados por una coma, usar solo "
"identificadores numéricos de contenido que se establezcan como Películas o "
"Programas de TV."

#: inc/csf/options.main_slider.php:89
msgid "Autoplay slider control"
msgstr "Control de autoplay slider"

#: inc/csf/options.main_slider.php:90
msgid "Check to enable auto-play slider."
msgstr "Marcar para activar autoplay slider."

#: inc/csf/options.main_slider.php:92
msgid "Autoplay Slider"
msgstr "Autoplay Slider"

#: inc/csf/options.main_slider.php:93
msgid "Autoplay Slider Movies"
msgstr "Películas autoplay slider"

#: inc/csf/options.main_slider.php:94
msgid "Autoplay Slider TV Shows"
msgstr "Autoplay Slider TV Shows"

#: inc/csf/options.main_slider.php:100
msgid "Speed Slider"
msgstr "Velocidad slider"

#: inc/csf/options.main_slider.php:101
msgid "Select speed slider in secons"
msgstr "Seleccionar velocidad de slider en segundos"

#: inc/csf/options.main_slider.php:103
msgid "4 seconds"
msgstr "4 segundos"

#: inc/csf/options.main_slider.php:104
msgid "3.5 seconds"
msgstr "3.5 segundos"

#: inc/csf/options.main_slider.php:105
msgid "3 seconds"
msgstr "3 segundos"

#: inc/csf/options.main_slider.php:106
msgid "2.5 seconds"
msgstr "2.5 segundos"

#: inc/csf/options.main_slider.php:107
msgid "2 seconds"
msgstr "2 segundos"

#: inc/csf/options.main_slider.php:108
msgid "1.5 seconds"
msgstr "1,5 segundos"

#: inc/csf/options.main_slider.php:109
msgid "1 second"
msgstr "1 segundo"

#: inc/csf/options.main_slider.php:110
msgid "0.5 seconds"
msgstr "0.5 segundos"

#: inc/csf/options.module_episodes.php:33 inc/csf/options.module_movies.php:33
#: inc/csf/options.module_seasons.php:33 inc/csf/options.module_tvshows.php:33
msgid "Slider control"
msgstr "Control deslizante"

#: inc/csf/options.more.php:8
msgid "SEO"
msgstr "SEO"

#: inc/csf/options.more.php:15
msgid "SEO Features"
msgstr "Características SEO"

#: inc/csf/options.more.php:16
msgid "Basic SEO"
msgstr "SEO basico"

#: inc/csf/options.more.php:21
msgid ""
"Uncheck this to disable SEO features in the theme, highly recommended if you "
"use any other SEO plugin, that way the themes SEO features won't conflict "
"with the plugin"
msgstr ""
"Desactive esta opción para desactivar las características de SEO en el tema, "
"muy recomendable si se utiliza cualquier otro plugin de SEO, de esa manera "
"las características de SEO del tema no entrarán en conflicto con el plugin"

#: inc/csf/options.more.php:27
msgid "Alternative name"
msgstr "Nombre alternativo"

#: inc/csf/options.more.php:33
msgid "Main keywords"
msgstr "Keywords principales"

#: inc/csf/options.more.php:34
msgid "add main keywords for site info"
msgstr "añadir principales keywords de tu sitio web"

#: inc/csf/options.more.php:40
msgid "Meta description"
msgstr "Meta descripción"

#: inc/csf/options.more.php:45
msgid "Site verification"
msgstr "Verificación del sitio"

#: inc/csf/options.more.php:51
msgid "Google Search Console"
msgstr "Google Search Console"

#: inc/csf/options.more.php:52 inc/csf/options.more.php:59
#: inc/csf/options.more.php:66
msgid "Settings here"
msgstr "Configuración aquí"

#: inc/csf/options.more.php:58
msgid "Bing Webmaster Tools"
msgstr "Bing Webmaster Tools"

#: inc/csf/options.more.php:65
msgid "Yandex Webmaster Tools"
msgstr "Yandex Webmaster Tools"

#: inc/csf/options.more.php:85
msgid "Manage integration codes and ads"
msgstr "Gestionar los códigos de integración y los anuncios"

#: inc/csf/options.more.php:97
msgid "Backup"
msgstr "Backup"

#: inc/csf/options.report_contact.php:8
msgid "Report and contact"
msgstr "Reportes y contacto"

#: inc/csf/options.report_contact.php:15
msgid "Reports Form"
msgstr "Formulario de informes"

#: inc/csf/options.report_contact.php:16
msgid "Enable report form"
msgstr "Habilitar formulario de informes"

#: inc/csf/options.report_contact.php:22 pages/contact.php:8
msgid "Contact Form"
msgstr "Formulario de contacto"

#: inc/csf/options.report_contact.php:23
msgid "Enable contact form"
msgstr "Habilitar formulario de contacto"

#: inc/csf/options.report_contact.php:30
msgid "Assign an email address if you want to be notified"
msgstr ""
"Asigne una dirección de correo electrónico si desea recibir notificaciones "
"por e-mail"

#: inc/csf/options.report_contact.php:35
msgid "Email notifications"
msgstr "Notificaciones de Email"

#: inc/csf/options.report_contact.php:41
msgid "Notify new reports by email"
msgstr "Notificar nuevos informes por correo electrónico"

#: inc/csf/options.report_contact.php:48
msgid "Notify new contact messages by email"
msgstr "Notificar nuevos mensajes de contacto por correo electrónico"

#: inc/csf/options.report_contact.php:53
msgid "Firewall"
msgstr "Cortafuegos"

#: inc/csf/options.report_contact.php:58
msgid ""
"We recommend not enabling more than 10 unread records per IP address, "
"consider that this function could be used maliciously and could compromise "
"the good status of your website."
msgstr ""
"Recomendamos no habilitar más de 10 registros no leídos por dirección IP, "
"considerar que esta función podría utilizarse maliciosamente y podría "
"comprometer el buen estado de su sitio web."

#: inc/csf/options.report_contact.php:63
msgid "Report limit"
msgstr "Límite de Reportes"

#: inc/csf/options.report_contact.php:64
msgid "Set limit of unread reports by IP address"
msgstr "Establecer límite de reportes no leídos por dirección IP"

#: inc/csf/options.report_contact.php:74 inc/csf/options.report_contact.php:91
msgid "Caution, you have enabled more than 50 unread records per IP address."
msgstr ""
"Precaución, ha habilitado más de 50 reportes no leídos por dirección IP."

#: inc/csf/options.report_contact.php:80
msgid "Contact messages limit"
msgstr "Límite de mensajes de contacto"

#: inc/csf/options.report_contact.php:81
msgid "Set limit of unread contact messages by IP address"
msgstr "Establecer límite de mensajes de contacto no leídos por dirección IP"

#: inc/csf/options.report_contact.php:97
msgid "Whitelist"
msgstr "Lista blanca"

#: inc/csf/options.report_contact.php:98
msgid "Create a safe list of IP addresses that can send reports without limits"
msgstr ""
"Cree una lista segura de direcciones IP que puedan enviar informes sin "
"límites"

#: inc/csf/options.report_contact.php:103
#: inc/csf/options.report_contact.php:116
msgid "IP adress"
msgstr "Dirección IP"

#: inc/csf/options.report_contact.php:110
msgid "Blacklist"
msgstr "Lista negra"

#: inc/csf/options.report_contact.php:111
msgid "Block the sending of reports and contact messages"
msgstr "Bloquear el envío de reportes y mensajes de contacto"

#: inc/csf/options.top_imdb.php:22
msgid "Select Layout"
msgstr "Seleccionar diseño"

#: inc/csf/options.top_imdb.php:23
msgid "Select the type of module to display"
msgstr "Seleccione el tipo de módulo para mostrar"

#: inc/csf/options.top_imdb.php:34
msgid "Last months"
msgstr "Últimos meses"

#: inc/csf/options.top_imdb.php:35
msgid "Verify content in the following time range in months"
msgstr "Verificar el contenido en el siguiente intervalo de tiempo en meses"

#: inc/csf/options.top_imdb.php:45
msgid "Minimum votes"
msgstr "Votos mínimos"

#: inc/csf/options.top_imdb.php:46
msgid "Set the minimum number of votes so that the content appears in the list"
msgstr ""
"Establecer el número mínimo de votos para que el contenido aparezca en la "
"lista"

#: inc/csf/options.video_player.php:8 inc/csf/options.video_player.php:107
#: inc/doo_player.php:111 inc/doo_player.php:112
#: inc/parts/admin/ads_tool.php:11
msgid "Video Player"
msgstr "Reproductor de video"

#: inc/csf/options.video_player.php:15
msgid "Ajax Mode"
msgstr "Modo Ajax"

#: inc/csf/options.video_player.php:17
msgid "This function delivers data safely and agile with WP-JSON"
msgstr "Esta función ofrece datos de forma segura y ágil con WP-JSON"

#: inc/csf/options.video_player.php:22
msgid "Delivery method"
msgstr "Método de entrega"

#: inc/csf/options.video_player.php:23
msgid "Select the most convenient delivery method for your website."
msgstr "Seleccione el método de entrega más conveniente para su sitio web."

#: inc/csf/options.video_player.php:26
msgid "This method is safe but not very agile"
msgstr "Este método es seguro pero no muy ágil"

#: inc/csf/options.video_player.php:27
msgid "This method is simplified and very agile."
msgstr "Este método es simplificado y muy ágil."

#: inc/csf/options.video_player.php:34
msgid ""
"If you have important traffic it would be advisable not to activate this "
"function, if it is activated we recommend deactivating the Auto Load"
msgstr ""
"Si tiene tráfico importante sería aconsejable no activar esta función, si "
"está activada le recomendamos desactivar la carga automática"

#: inc/csf/options.video_player.php:40
msgid ""
"The selected delivery method is unsafe but very agile, if you have "
"significant traffic we recommend disabling automatic loading"
msgstr ""
"El método de entrega seleccionado es inseguro pero muy ágil, si tiene "
"tráfico importante le recomendamos deshabilitar la carga automática"

#: inc/csf/options.video_player.php:46
msgid "Auto Load"
msgstr "Carga automática"

#: inc/csf/options.video_player.php:47
msgid "Load the first element of video player with the page"
msgstr "Cargar el primer elemento del reproductor de vídeo con la página"

#: inc/csf/options.video_player.php:53
msgid ""
"The first element of the player will be loaded between 0 and 4 seconds after "
"completing the total load of the page"
msgstr ""
"El primer elemento del reproductor se cargará entre 0 y 4 segundos después "
"de completar la carga total de la página"

#: inc/csf/options.video_player.php:60
msgid "Time to wait in seconds before displaying Video Player"
msgstr ""
"Tiempo para esperar en segundos antes de mostrar el reproductor de vídeo"

#: inc/csf/options.video_player.php:71
msgid "Auto Play"
msgstr "Auto Play"

#: inc/csf/options.video_player.php:72
msgid "Check if you want the videos to play automatically"
msgstr "Marcar si deseas que los vídeos se reproduzcan automáticamente"

#: inc/csf/options.video_player.php:74
msgid "Auto-play YouTube videos"
msgstr "Reproducir automáticamente los videos de YouTube"

#: inc/csf/options.video_player.php:75
msgid "Auto-play JWPlayer videos"
msgstr "Reproducir automáticamente los videos en JWPlayer"

#: inc/csf/options.video_player.php:81
msgid "Player size"
msgstr "Tamaño del reproductor"

#: inc/csf/options.video_player.php:82
msgid "Select a specific size for video player"
msgstr "Seleccione un tamaño específico para el reproductor de vídeo"

#: inc/csf/options.video_player.php:84
msgid "Regular size"
msgstr "Regular"

#: inc/csf/options.video_player.php:85
msgid "Bigger size"
msgstr "Grande"

#: inc/csf/options.video_player.php:92 inc/doo_player.php:275
msgid "Video Sources"
msgstr "Fuentes de vídeo"

#: inc/csf/options.video_player.php:93
msgid "Uncheck to hide the source"
msgstr "Desmarque para ocultar el origen"

#: inc/csf/options.video_player.php:94
msgid "Show the domain or source of the video"
msgstr "Mostrar el dominio o el origen del vídeo"

#: inc/csf/options.video_player.php:100
msgid "Video Sources Scrolling"
msgstr "Scrolling de fuentes de video"

#: inc/csf/options.video_player.php:101
msgid "Scrolling by default is disabled from mobile devices"
msgstr ""
"El desplazamiento de forma predeterminada está deshabilitado desde "
"dispositivos móviles"

#: inc/csf/options.video_player.php:102
msgid "Enable scrolling of video sources"
msgstr "Habilitar el desplazamiento de fuentes de vídeo"

#: inc/csf/options.video_player.php:112
msgid "Page jwplayer"
msgstr "Página jwplayer"

#: inc/csf/options.video_player.php:113
msgid "Select page to display player"
msgstr "Seleccione la página para mostrar el reproductor"

#: inc/csf/options.video_player.php:119
msgid "Player"
msgstr "Player"

#: inc/csf/options.video_player.php:121
msgid "JW Player 8"
msgstr "JW Player 8"

#: inc/csf/options.video_player.php:122
msgid "JW Player 7"
msgstr "JW Player 7"

#: inc/csf/options.video_player.php:123
msgid "Plyr.io"
msgstr "Plyr.io"

#: inc/csf/options.video_player.php:138
msgid "JW Player 7 (Self-Hosted)"
msgstr "JW Player 7 (Autohospedado)"

#: inc/csf/options.video_player.php:146
msgid "JW Player 8 (Self-Hosted)"
msgstr "JW Player 8 (Autohospedado)"

#: inc/csf/options.video_player.php:153
msgid "About text"
msgstr "Texto de Acerca de"

#: inc/csf/options.video_player.php:154
msgid "JW Player About text in right click"
msgstr "JW Player Acerca del texto con el botón derecho del ratón"

#: inc/csf/options.video_player.php:161
msgid "Logo player"
msgstr "Logo"

#: inc/csf/options.video_player.php:162
msgid "Upload your logo using the Upload Button or insert image URL"
msgstr "Cargar su logo usando el botón Subir o insertar URL de la imagen"

#: inc/csf/options.video_player.php:168
msgid "Logo position"
msgstr "Posición del logo"

#: inc/csf/options.video_player.php:169
msgid "Select a postion for logo player"
msgstr "Seleccione una posición para el logo del reproductor"

#: inc/csf/options.video_player.php:171
msgid "Top left"
msgstr "Arriba a la izquierda"

#: inc/csf/options.video_player.php:172
msgid "Top right"
msgstr "Arriba a la derecha"

#: inc/csf/options.video_player.php:173
msgid "Bottom left"
msgstr "Abajo a la izquierda"

#: inc/csf/options.video_player.php:174
msgid "Bottom right"
msgstr "Abajo a la derecha"

#: inc/csf/options.video_player.php:180
msgid "Fake Player"
msgstr "Reproductor Falso"

#: inc/csf/options.video_player.php:185
msgid "This module does not work if Auto-Load is activated"
msgstr ""
"Este módulo no funcionara si la carga automática del player está activada"

#: inc/csf/options.video_player.php:191 inc/doo_scripts.php:207
#: inc/parts/item_links.php:47 inc/parts/item_links_admin.php:43
msgid "Enable"
msgstr "Habilitar"

#: inc/csf/options.video_player.php:192
msgid "Enable Fake Player"
msgstr "Habilitar Reproductor Falso"

#: inc/csf/options.video_player.php:197
msgid "Backdrop URL"
msgstr "URL Imagen de fondo"

#: inc/csf/options.video_player.php:198
msgid ""
"Show background image by default if the system did not find an image in the "
"content"
msgstr ""
"Mostrar imagen de fondo de forma predeterminada si el sistema no encuentra "
"una imagen en el contenido"

#: inc/csf/options.video_player.php:207
msgid "Show in Fake Player"
msgstr "Mostrar en el Reproductor falso"

#: inc/csf/options.video_player.php:209
msgid "Play button"
msgstr "Botón de play"

#: inc/csf/options.video_player.php:210
msgid "Notify what is an ad"
msgstr "Notificar que es un anuncio"

#: inc/csf/options.video_player.php:211
msgid "HD Quality"
msgstr "Calidad HD"

#: inc/csf/options.video_player.php:218
msgid "Advertising links for fake player"
msgstr "Enlaces publicitarios para el Reproductor falso"

#: inc/csf/options.video_player.php:223
msgid ""
"Add as many ad links as you wish, these are displayed randomly in the Fake "
"Player"
msgstr ""
"Añade tantos enlaces de anuncios como desee, estos se muestran "
"aleatoriamente en el Reproductor falso"

#: inc/csf/options.video_player.php:229
msgid "Add link"
msgstr "Añadir enlace"

#: inc/csf/options.video_player.php:230
msgid "Add new link"
msgstr "Agregar nuevo enlace"

#: inc/csf/options.wp_mail.php:8
msgid "WP Mail"
msgstr "WP Mail"

#: inc/csf/options.wp_mail.php:14
msgid "Welcome message"
msgstr "Mensaje de bienvenida"

#: inc/csf/options.wp_mail.php:19 pages/contact.php:34
msgid "Subject"
msgstr "Asunto"

#: inc/csf/options.wp_mail.php:20
msgid "Welcome to {sitename}"
msgstr "Bienvenido a {sitename}"

#: inc/csf/options.wp_mail.php:25
msgid "Message"
msgstr "Mensaje"

#: inc/csf/options.wp_mail.php:26
msgid "Hello {username}, Thank you for registering at {sitename}"
msgstr "Hola {username}, gracias por registrarse en {sitename}"

#: inc/csf/options.wp_mail.php:31
msgid "SMTP Settings"
msgstr "Configuración SMTP"

#: inc/csf/options.wp_mail.php:36
msgid "Enable SMTP"
msgstr "Activar SMTP"

#: inc/csf/options.wp_mail.php:37
msgid "Configure an SMTP server for WordPress to send verified emails"
msgstr ""
"Configure un servidor SMTP para que WordPress envíe correos electrónicos "
"verificados"

#: inc/csf/options.wp_mail.php:43
msgid "SMTP Server"
msgstr "Servidor SMTP"

#: inc/csf/options.wp_mail.php:50
msgid "SMTP Port"
msgstr "Puerto SMTP"

#: inc/csf/options.wp_mail.php:60
msgid "Type of Encryption"
msgstr "Tipo de Encriptación"

#: inc/csf/options.wp_mail.php:62
msgid "Plain text"
msgstr "Texto plano"

#: inc/csf/options.wp_mail.php:63
msgid "SSL"
msgstr "SSL"

#: inc/csf/options.wp_mail.php:64
msgid "TSL"
msgstr "TSL"

#: inc/csf/options.wp_mail.php:72
msgid "From Name"
msgstr "Nombre del remitente"

#: inc/csf/options.wp_mail.php:78
msgid "From Email Address"
msgstr "Correo Electrónico del remitente"

#: inc/csf/options.wp_mail.php:83
msgid "SMTP Authentication"
msgstr "Autenticación SMTP"

#: inc/csf/options.wp_mail.php:89 inc/doo_init.php:1121
#: inc/includes/slugs.php:25 inc/parts/login_form.php:6
#: pages/sections/login-form.php:3 pages/sections/register-form.php:3
msgid "Username"
msgstr "Usuario"

#: inc/csf/options.wp_mail.php:95 inc/doo_init.php:1122
#: inc/parts/login_form.php:7 pages/sections/account.php:152
#: pages/sections/login-form.php:7 pages/sections/register-form.php:11
msgid "Password"
msgstr "Contraseña"

#: inc/doo_ads.php:31 inc/doo_ads.php:32
msgid "DooPlay Ad banners"
msgstr "DooPlay Ad banners"

#: inc/doo_ajax.php:43
msgid "The passwords you entered do not match.  Your password was not updated."
msgstr "Las contraseñas no coinciden. Su contraseña no se a podido actualizar."

#: inc/doo_ajax.php:60
msgid "Your profile has been updated."
msgstr "Sus ajustes de perfil se ha actualizado correctamente."

#: inc/doo_ajax.php:103
msgid "No more content to show."
msgstr "No hay mas contenido para mostrar."

#: inc/doo_ajax.php:133 inc/doo_ajax.php:162 inc/doo_ajax.php:189
#: inc/doo_init.php:1052 inc/doo_init.php:1074 inc/doo_init.php:1094
#: inc/parts/modules/widgethome.php:13
msgid "No content"
msgstr "Sin contenido"

#: inc/doo_ajax.php:212
msgid "Link enabled"
msgstr "Enlace habilitado"

#: inc/doo_ajax.php:214
msgid "Link disabled"
msgstr "Enlace desactivado"

#: inc/doo_ajax.php:216
msgid "Link moved to trash"
msgstr "Enlace movido a la papelera"

#: inc/doo_ajax.php:235 inc/doo_ajax.php:277
msgid "No data nonce"
msgstr "Sin datos nonce"

#: inc/doo_auth.php:70
msgid "Welcome, reloading page"
msgstr "Bienvenido, redireccionando pagina"

#: inc/doo_auth.php:75
msgid "Wrong username or password"
msgstr "Usuario o contraseña incorrecto"

#: inc/doo_auth.php:97
msgid "A username is required for registration"
msgstr "Se requiere un nombre de usuario para registrarse"

#: inc/doo_auth.php:99
msgid "Sorry, that username already exists"
msgstr "Lo siento, ese nombre de usuario ya existe"

#: inc/doo_auth.php:101
msgid "You must enter a valid email address"
msgstr "Debes ingresar una dirección de correo electrónico válida"

#: inc/doo_auth.php:103
msgid "Sorry, that email address is already used"
msgstr "Lo siento, esta dirección de email ya está en uso"

#: inc/doo_auth.php:108
msgid "Registration completed successfully"
msgstr "Registro completado correctamente"

#: inc/doo_cache.php:107
msgid "Dooplay options"
msgstr "Dooplay options"

#: inc/doo_cache.php:117
msgid "Delete cache this Entry"
msgstr "Eliminar caché esta entrada"

#: inc/doo_cache.php:124 inc/parts/admin/database_tool.php:56
msgid "Clear Transient options"
msgstr "Limpiar opciones transitorias"

#: inc/doo_cache.php:130
msgid "Delete cache expired"
msgstr "Eliminar caché caducada"

#: inc/doo_cache.php:136
msgid "Delete all cache"
msgstr "Eliminar toda la caché"

#: inc/doo_collection.php:70 inc/doo_scripts.php:197
msgid "Remove of favorites"
msgstr "Eliminar de favoritos"

#: inc/doo_collection.php:70 inc/doo_scripts.php:196
msgid "Add to favorites"
msgstr "Añadir a favoritos"

#: inc/doo_collection.php:178 inc/doo_scripts.php:200
msgid "I saw it"
msgstr "Ya lo vi"

#: inc/doo_comments.php:43 taxonomy.php:19
#, php-format
msgid "%s"
msgstr "%s"

#: inc/doo_comments.php:47 inc/doo_init.php:752
#, php-format
msgid "%1$s"
msgstr "%1$s"

#: inc/doo_comments.php:59
msgid "Your comment is awaiting moderation."
msgstr "Su comentario esta esperando ser moderado por un administrador."

#: inc/doo_comments.php:70
msgid "Required"
msgstr "Requerido"

#: inc/doo_comments.php:72
msgid "Post comment"
msgstr "Publicar comentario"

#: inc/doo_comments.php:73
msgid "Leave a comment"
msgstr "Deja un comentario"

#: inc/doo_comments.php:79
msgid "Your comment.."
msgstr "Tu comentario.."

#: inc/doo_comments.php:86 pages/contact.php:16 pages/contact.php:25
#: pages/sections/register-form.php:16
msgid "Name"
msgstr "Nombre"

#: inc/doo_comments.php:87
msgid "Add a display name"
msgstr "Añadir un nombre para mostrar"

#: inc/doo_comments.php:96
msgid "Your email address will not be published"
msgstr "Tu correo electrónico no será publicado"

#: inc/doo_database.php:52 inc/doo_database.php:53
msgid "DooPlay Database"
msgstr "DooPlay Database"

#: inc/doo_database.php:70
msgid "this process was never executed"
msgstr "este proceso nunca se ejecutó"

#: inc/doo_database.php:109
msgid "Blog"
msgstr "Blog"

#: inc/doo_database.php:119
msgid "JW Player"
msgstr "JW Player"

#: inc/doo_database.php:218
msgid "Just now"
msgstr "Justo ahora"

#: inc/doo_init.php:105
msgid "Site offline"
msgstr "Sitio en línea"

#: inc/doo_init.php:134
msgid "Menu main header"
msgstr "Menú header principal"

#: inc/doo_init.php:135
msgid "Menu footer"
msgstr "Menú footer"

#: inc/doo_init.php:137
msgid "Footer - column 1"
msgstr "Pie de página - columna 1"

#: inc/doo_init.php:138
msgid "Footer - column 2"
msgstr "Pie de página - columna 2"

#: inc/doo_init.php:139
msgid "Footer - column 3"
msgstr "Pie de página - columna 3"

#: inc/doo_init.php:333
msgid "of"
msgstr "de"

#: inc/doo_init.php:420
msgid "Unknown resource"
msgstr "Desconocido"

#: inc/doo_init.php:424
msgid "Google Drive"
msgstr "Google Drive"

#: inc/doo_init.php:640
msgid "Twitter URL"
msgstr "Twitter URL"

#: inc/doo_init.php:641
msgid "Facebook URL"
msgstr "Facebook URL"

#: inc/doo_init.php:710
msgid "Shared"
msgstr "Compartido"

#: inc/doo_init.php:714
msgid "Twitter"
msgstr "Twitter"

#: inc/doo_init.php:854
msgid "This content already exists, we recommend not to publish."
msgstr ""
"Este contenido se ha publicado antes, se recomienda no volver a publicar."

#: inc/doo_init.php:856
msgid "Excellent! this content is unique."
msgstr "¡Excelente! este contenido es único."

#: inc/doo_init.php:1028
msgid "No content available on your list."
msgstr "No hay contenido en tu lista."

#: inc/doo_init.php:1119 inc/parts/login_form.php:4
msgid "Login to your account"
msgstr "Ingrese a su cuenta"

#: inc/doo_init.php:1123 inc/parts/login_form.php:8
msgid "Remember Me"
msgstr "Recuérdame"

#: inc/doo_init.php:1124 inc/parts/login_form.php:9
#: pages/sections/login-form.php:15 pages/sections/login.php:10
msgid "Log in"
msgstr "Iniciar sesión"

#: inc/doo_init.php:1125 inc/parts/login_form.php:10
msgid "Register a new account"
msgstr "Registre una nueva cuenta"

#: inc/doo_init.php:1126 inc/parts/login_form.php:11
msgid "Lost your password?"
msgstr "¿Perdiste tu contraseña?"

#: inc/doo_init.php:1240
msgid "Home"
msgstr "Inicio"

#: inc/doo_links.php:70 inc/doo_scripts.php:205 inc/parts/single/links.php:7
#: inc/parts/single/links.php:16
msgid "Download"
msgstr "Descarga"

#: inc/doo_links.php:70 inc/parts/single/links.php:9
#: inc/parts/single/links.php:18
msgid "Watch online"
msgstr "Ver en línea"

#: inc/doo_links.php:70 inc/doo_links.php:452 inc/doo_links.php:489
#: inc/doo_links.php:584 inc/doo_links.php:585 inc/doo_links.php:639
#: inc/doo_links.php:680 inc/doo_links.php:733 inc/doo_links.php:735
#: inc/parts/item_links.php:28 inc/parts/item_links.php:29
#: inc/parts/item_links_admin.php:26 inc/parts/item_links_admin.php:27
#: inc/parts/item_links_profile.php:26 inc/parts/item_links_profile.php:27
#: inc/parts/single/doo_links.php:45 inc/parts/single/links.php:8
#: inc/parts/single/links.php:17
msgid "Torrent"
msgstr "Torrent"

#: inc/doo_links.php:70 inc/parts/single/links.php:10
#: inc/parts/single/links.php:19
msgid "Rent or Buy"
msgstr "Alquiler o comprar"

#: inc/doo_links.php:100
#, php-format
msgid "Links %%PENDING_COUNT_LINK%%"
msgstr "Enlaces %%PENDING_COUNT_LINK%%"

#: inc/doo_links.php:112
msgid "Links manage"
msgstr "Gestión de enlaces"

#: inc/doo_links.php:239
msgid "Save and publish"
msgstr "Guardar y publicar"

#: inc/doo_links.php:270 inc/parts/links_editor.php:23
#: inc/parts/links_editor_type.php:31 inc/parts/single/links.php:37
msgid "File size"
msgstr "Tamaño de archivo"

#: inc/doo_links.php:496
msgid "Invalid verification"
msgstr "Verificación no válida"

#: inc/doo_links.php:563
msgid "Options"
msgstr "Opciones"

#: inc/doo_links.php:571 inc/parts/links_editor.php:52
#: pages/sections/account.php:110 pages/sections/account.php:132
msgid "Manage"
msgstr "Administrar"

#: inc/doo_links.php:585
msgid "Get Torrent"
msgstr "Obtener torrent"

#: inc/doo_links.php:627
msgid "No link has been added yet"
msgstr "Aún no se ha añadido ningún vínculo"

#: inc/doo_links.php:639 inc/doo_links.php:680
msgid "uTorrent"
msgstr "uTorrent"

#: inc/doo_links.php:708 inc/parts/links_editor_type.php:5
msgid "Parent"
msgstr "Padre"

#: inc/doo_metafields.php:190
msgid "Update info"
msgstr "Actualizar info"

#: inc/doo_metafields.php:190
msgid "Generate"
msgstr "Generar"

#: inc/doo_metafields.php:197
msgid "Check duplicate content"
msgstr "Verificar contenido duplicado"

#: inc/doo_metafields.php:253
msgid "Upload now"
msgstr "Cargar ahora"

#: inc/doo_notices.php:45
#, php-format
msgid ""
"DooPlay requires <strong>PHP %1$s+</strong>. Please ask your webhost to "
"upgrade to at least PHP %1$s. Recommended: <strong>PHP 7.2</strong>"
msgstr ""
"DooPlay requiere <strong> PHP%1$s + </strong>. Por favor solicite a su "
"administrador de Webhost que actualice a al menos PHP%1$s. Recomendamos: "
"<strong> PHP 7.2 </strong>"

#: inc/doo_notices.php:53
msgid ""
"Invalid license, it is possible that some of the options may not work "
"correctly"
msgstr ""
"Licencia no válida, es posible que algunas de las opciones no funcionen "
"correctamente, regularice el estado de su licencia"

#: inc/doo_notices.php:53
msgid "here"
msgstr "aquí"

#: inc/doo_notices.php:61
msgid "Dooplay requires you to update the database"
msgstr "DooPlay requiere actualizar la base de datos"

#: inc/doo_notices.php:61
msgid "click here to update"
msgstr "click aquí para actualizar"

#: inc/doo_notices.php:69
msgid "Add API key for Dbmovies"
msgstr "Agregar clave de API para Dbmovies"

#: inc/doo_notices.php:77
msgid "Generate all the required pages"
msgstr "Generar todas las páginas requeridas"

#: inc/doo_notices.php:77 inc/doo_notices.php:85
msgid "click here"
msgstr "click aquí"

#: inc/doo_notices.php:85
msgid "This version requires you to update the links module"
msgstr "Esta versión requiere que actualice el módulo de enlaces"

#: inc/doo_options.php:16
#, php-format
msgid "%s options"
msgstr "%s opciones"

#: inc/doo_player.php:44
msgid "---------"
msgstr "---------"

#: inc/doo_player.php:45
msgid "Chinese"
msgstr "Chino"

#: inc/doo_player.php:46
msgid "Denmark"
msgstr "Denmark"

#: inc/doo_player.php:49
msgid "English British"
msgstr "Inglés Británico"

#: inc/doo_player.php:50
msgid "Egypt"
msgstr "Egipto"

#: inc/doo_player.php:58
msgid "Philippines"
msgstr "Philippines"

#: inc/doo_player.php:59
msgid "Portuguese Portugal"
msgstr "Portugués Portugal"

#: inc/doo_player.php:60
msgid "Portuguese Brazil"
msgstr "Portugués Brasil"

#: inc/doo_player.php:63
msgid "Scotland"
msgstr "Scotland"

#: inc/doo_player.php:64
msgid "Spanish Spain"
msgstr "Español España"

#: inc/doo_player.php:65
msgid "Spanish Mexico"
msgstr "Español México"

#: inc/doo_player.php:66
msgid "Spanish Argentina"
msgstr "Español Argentina"

#: inc/doo_player.php:67
msgid "Spanish Peru"
msgstr "Español Perú"

#: inc/doo_player.php:68
msgid "Spanish Chile"
msgstr "Español Chile"

#: inc/doo_player.php:69
msgid "Spanish Colombia"
msgstr "Español Colombia"

#: inc/doo_player.php:70
msgid "Sweden"
msgstr "Sweden"

#: inc/doo_player.php:72
msgid "Rusian"
msgstr "Ruso"

#: inc/doo_player.php:73
msgid "Vietnam"
msgstr "Vietnam"

#: inc/doo_player.php:83
msgid "URL Embed"
msgstr "URL Embed"

#: inc/doo_player.php:84 inc/doo_player.php:100
msgid "URL MP4"
msgstr "URL MP4"

#: inc/doo_player.php:88 inc/doo_player.php:102
msgid "Shortcode or HTML"
msgstr "Shortcode o HTML"

#: inc/doo_player.php:99
msgid "URL Iframe"
msgstr "Iframe URL"

#: inc/doo_player.php:101
msgid "ID or URL Google Drive"
msgstr "ID o URL Google Drive"

#: inc/doo_player.php:277
msgid "Report Error"
msgstr "Reportar"

#: inc/doo_player.php:282
msgid "Watch trailer"
msgstr "Ver trailer"

#: inc/doo_player.php:343
msgid "Advertisement"
msgstr "Anuncio"

#: inc/doo_scripts.php:201
msgid "Data send.."
msgstr "Enviar datos.."

#: inc/doo_scripts.php:202
msgid "Updating data.."
msgstr "Actualizando datos.."

#: inc/doo_scripts.php:203
msgid "Error"
msgstr "Error"

#: inc/doo_scripts.php:204
msgid "Pending review"
msgstr "Moderación pendiente"

#: inc/doo_scripts.php:206
msgid "Sending data"
msgstr "Enviando datos"

#: inc/doo_scripts.php:208 inc/parts/item_links.php:45
#: inc/parts/item_links_admin.php:41
msgid "Disable"
msgstr "Inhabilitar"

#: inc/doo_scripts.php:210 pages/sections/account.php:92
msgid "Links Shared"
msgstr "Enlaces compartidos"

#: inc/doo_scripts.php:211
msgid "Manage pending links"
msgstr "Administrar links pendentes"

#: inc/doo_scripts.php:212
msgid "Please wait, sending data.."
msgstr "Por favor espere, enviando datos.."

#: inc/doo_scripts.php:213
msgid "Ready"
msgstr "Listo"

#: inc/doo_scripts.php:214
msgid "Do you really want to delete this link?"
msgstr "¿Realmente desea eliminar este enlace?"

#: inc/doo_scripts.php:222
msgid "View all results"
msgstr "Ver todos los resultados"

#: inc/doo_scripts.php:224
msgid "Really you want to restart all data?"
msgstr "¿Realmente quieres reiniciar todos los datos?"

#: inc/doo_scripts.php:225
msgid "They sure have added content manually?"
msgstr "Seguro que ya haz agregado contenido para mostrar?"

#: inc/doo_scripts.php:227
msgid "Loading player.."
msgstr "Cargando video.."

#: inc/doo_scripts.php:228
msgid "Select a video player"
msgstr "Seleccione un vídeo"

#: inc/doo_scripts.php:257 pages/sections/dt_foot.php:7
msgid "Loading..."
msgstr "Cargando..."

#: inc/doo_scripts.php:258
msgid "Reloading.."
msgstr "Recargando..."

#: inc/doo_scripts.php:259
msgid "Domain has already been registered"
msgstr "El dominio ya se ha registrado"

#: inc/doo_scripts.php:260
msgid "Updating database.."
msgstr "Actualizando base de datos.."

#: inc/doo_scripts.php:261
msgid "Action completed"
msgstr "Proceso completado"

#: inc/doo_scripts.php:262
msgid "The links field is empty"
msgstr "El campo links está vacío"

#: inc/doo_scripts.php:263
msgid "Do you really want to delete this item?"
msgstr "¿Realmente desea eliminar este elemento?"

#: inc/doo_scripts.php:264
msgid ""
"Do you really want to delete this register, once completed this action will "
"not recover the data again?"
msgstr ""
"¿Realmente desea eliminar este registro, una vez completada esta acción no "
"recuperará los datos nuevamente?"

#: inc/doo_scripts.php:265
msgid "Do you want to publish the links before continuing?"
msgstr "Deseas publicar los enlaces antes de continuar?"

#: inc/includes/metabox.php:16
msgid "Post meta"
msgstr "Post meta"

#: inc/includes/metabox.php:29
msgid "Short description"
msgstr "Descripción corta"

#: inc/includes/rating/content.php:11
msgid "Log in to vote"
msgstr "Iniciar sesión para votar"

#: inc/includes/rating/content.php:13
msgid "Your rating:"
msgstr "Tu voto:"

#: inc/includes/rating/content.php:18 inc/includes/rating/init.php:262
#: inc/includes/rating/init.php:589
msgid "vote"
msgid_plural "votes"
msgstr[0] "voto"
msgstr[1] "votos"

#: inc/includes/rating/init.php:63
msgid "View Starstruck Settings"
msgstr "Ver la configuración de Starstruck"

#: inc/includes/rating/init.php:158
msgid "0"
msgstr "0"

#: inc/includes/rating/init.php:259
msgid "Thanks for your vote!"
msgstr "Gracias por votar!"

#: inc/includes/rating/init.php:275
msgid "HTTP/1.1 400 Nonce is empty and/or invalid"
msgstr "HTTP/1.1 400 Nonce está vacío y/o inválido"

#: inc/includes/rating/init.php:278
msgid "HTTP/1.1 400 ID is empty"
msgstr "HTTP/1.1 400 ID esta vacio"

#: inc/includes/rating/init.php:306
msgid "Busted!"
msgstr "¡Atrapado!"

#: inc/includes/rating/init.php:321
msgid "User vote deleted"
msgstr "Voto de usuario eliminado"

#: inc/includes/rating/init.php:401
msgid "Anonymous"
msgstr "Anónimo"

#: inc/includes/rating/init.php:589
msgid "Rating:"
msgstr "Clasificación:"

#: inc/includes/slugs.php:31 inc/widgets/content_widget_home.php:128
msgid "Genre"
msgstr "Genero"

#: inc/includes/slugs.php:32
msgid "Release"
msgstr "Lanzamiento"

#: inc/includes/slugs.php:33
msgid "Network"
msgstr "Network"

#: inc/includes/slugs.php:45
msgid "DooPlay: Permalink Settings"
msgstr "DooPlay: Ajustes de los enlaces permanentes"

#: inc/parts/admin/ads_tool.php:8
msgid "Code integrations"
msgstr "Códigos de integración"

#: inc/parts/admin/ads_tool.php:12
msgid "Links redirection"
msgstr "Redireccionamiento de enlaces"

#: inc/parts/admin/ads_tool.php:18
msgid "Header code integration"
msgstr "Integración de código en el encabezado"

#: inc/parts/admin/ads_tool.php:20
msgid ""
"Enter the code which you need to place before closing tag. (ex: Google "
"Webmaster Tools verification, Bing Webmaster Center, BuySellAds Script, "
"Alexa verification etc.)"
msgstr ""
"Introduzca el código que debe colocar antes de cerrar la etiqueta. (ej: "
"verificación de herramientas de webmaster de Google, Bing webmaster Center, "
"BuySellAds script, Alexa Verification etc.)"

#: inc/parts/admin/ads_tool.php:22
msgid "Footer code integration"
msgstr "Integración de código en el pie de pagina"

#: inc/parts/admin/ads_tool.php:24
msgid ""
"Enter the codes which you need to place in your footer. (ex: Google "
"Analytics, Clicky, STATCOUNTER, Woopra, Histats, etc.)"
msgstr ""
"Introducir los códigos que usted necesita para colocar en su pie de página. "
"(Por ejemplo: Google Analytics, Clicky, StatCounter, Woopra, Histats, etc.)"

#: inc/parts/admin/ads_tool.php:28
msgid "Homepage > Ad banner desktop"
msgstr "Página principal > banner de anuncios de escritorio"

#: inc/parts/admin/ads_tool.php:29 inc/parts/admin/ads_tool.php:31
#: inc/parts/admin/ads_tool.php:37 inc/parts/admin/ads_tool.php:39
#: inc/parts/admin/ads_tool.php:45 inc/parts/admin/ads_tool.php:47
#: inc/parts/admin/ads_tool.php:52 inc/parts/admin/ads_tool.php:54
#: inc/parts/admin/ads_tool.php:58 inc/parts/admin/ads_tool.php:60
msgid "Use HTML code"
msgstr "Usar código HTML"

#: inc/parts/admin/ads_tool.php:30
msgid "Homepage > Ad banner mobile"
msgstr "Página principal > banner de anuncio para moviles"

#: inc/parts/admin/ads_tool.php:32 inc/parts/admin/ads_tool.php:40
#: inc/parts/admin/ads_tool.php:48 inc/parts/admin/ads_tool.php:55
#: inc/parts/admin/ads_tool.php:61
msgid "This is an optional field"
msgstr "Este es un campo opcional"

#: inc/parts/admin/ads_tool.php:36
msgid "Single Post > Ad banner desktop"
msgstr "Single post > banner de anuncio de escritorio"

#: inc/parts/admin/ads_tool.php:38
msgid "Single Post > Ad banner mobile"
msgstr "Single post > banner de anuncio para moviles"

#: inc/parts/admin/ads_tool.php:44
msgid "Video Player > Ad banner desktop"
msgstr "Video Player > Banner de anuncio de escritorio"

#: inc/parts/admin/ads_tool.php:46
msgid "Video Player > Ad banner mobile"
msgstr "Video Player > Banner de anuncio para moviles"

#: inc/parts/admin/ads_tool.php:51
msgid "Ad in top for Desktop"
msgstr "Anuncio en la parte superior de escritorio"

#: inc/parts/admin/ads_tool.php:53
msgid "Ad in top for mobile"
msgstr "Anuncio en la parte superior para moviles"

#: inc/parts/admin/ads_tool.php:57
msgid "Ad in bottom for Desktop"
msgstr "Anuncio en la parte inferior de escritorio"

#: inc/parts/admin/ads_tool.php:59
msgid "Ad in bottom for mobile"
msgstr "Anuncio en la parte inferior para moviles"

#: inc/parts/admin/database_tool.php:2
msgid "Database tool for DooPlay"
msgstr "Herramienta de base de datos para DooPlay"

#: inc/parts/admin/database_tool.php:3
msgid "Caution, any process that runs from this tool is irreversible"
msgstr ""
"PRECAUCIÓN, cualquier proceso que se ejecuta desde esta herramienta es "
"irreversible"

#: inc/parts/admin/database_tool.php:4
msgid ""
"Before proceeding with any action that you wish to execute from this tool, "
"we recommend making a backup of your database, all elimination processes are "
"irreversible."
msgstr ""
"Antes de proceder con cualquier acción que desee ejecutar desde esta "
"herramienta, le recomendamos hacer una copia de seguridad de su base de "
"datos, todos los procesos de eliminación son irreversibles."

#: inc/parts/admin/database_tool.php:11
msgid "Update Links Module"
msgstr "Actualizar modulo de enlaces"

#: inc/parts/admin/database_tool.php:13
msgid ""
"This new version require that you update the content for the links module, "
"the process is safe"
msgstr ""
"Esta nueva versión requiere que actualices el contenido del módulo de "
"enlaces, el proceso es seguro"

#: inc/parts/admin/database_tool.php:15
msgid "Update module"
msgstr "Actualizar modulo"

#: inc/parts/admin/database_tool.php:32
msgid "Force license activation"
msgstr "Forzar activación de licencia"

#: inc/parts/admin/database_tool.php:34
msgid ""
"This measure is required when your server does not complete the connection "
"with our repository."
msgstr ""
"Esta medida es necesaria cuando el servidor no completa la conexión con "
"nuestro repositorio."

#: inc/parts/admin/database_tool.php:45
msgid "Reset license"
msgstr "Resetear Licencia"

#: inc/parts/admin/database_tool.php:47
msgid "Delete all the data that your license has registered in your database."
msgstr ""
"Elimine toda la información que su licencia ha registrado en la base de "
"datos."

#: inc/parts/admin/database_tool.php:50 inc/parts/admin/database_tool.php:61
#: inc/parts/admin/database_tool.php:72 inc/parts/admin/database_tool.php:83
#: inc/parts/admin/database_tool.php:94 inc/parts/admin/database_tool.php:105
#: inc/parts/admin/database_tool.php:115 inc/parts/admin/database_tool.php:126
#: inc/parts/admin/database_tool.php:137
msgid "Run process"
msgstr "Ejecutar proceso"

#: inc/parts/admin/database_tool.php:58
msgid "Removing transient options can help solve some system problems."
msgstr ""
"La eliminación de opciones transitorias puede ayudar a resolver algunos "
"problemas del sistema."

#: inc/parts/admin/database_tool.php:67
msgid "Reset User favorites"
msgstr "Restablecer favoritos de usuario"

#: inc/parts/admin/database_tool.php:69
msgid "Reset the list of favorites of all your users."
msgstr "Reinicia la lista de favoritos de todos tus usuarios."

#: inc/parts/admin/database_tool.php:78
msgid "Reset User views"
msgstr "Restablecer vistas de usuario"

#: inc/parts/admin/database_tool.php:80
msgid "Restore the list of views of all your users."
msgstr "Restaure la lista de vistas de todos sus usuarios."

#: inc/parts/admin/database_tool.php:89
msgid "Reset User reports"
msgstr "Restablecer informes de usuario"

#: inc/parts/admin/database_tool.php:91
msgid "Remove all user reports"
msgstr "Eliminar todos los reportes de los usuario"

#: inc/parts/admin/database_tool.php:100
msgid "Reset User ratings"
msgstr "Restablecer las calificaciones de usuario"

#: inc/parts/admin/database_tool.php:102
msgid "Reset rating counter on all content."
msgstr "Restablecer contador de calidicaciones en todo el contenido."

#: inc/parts/admin/database_tool.php:111
msgid "Reset Post featured"
msgstr "Reiniciar Títulos destacados"

#: inc/parts/admin/database_tool.php:113
msgid "Reset all the content that was marked as featured and start a new list."
msgstr ""
"Restablecer todo el contenido que se marcó como estacado y empezar una nueva "
"lista."

#: inc/parts/admin/database_tool.php:121
msgid "Reset Post views"
msgstr "Restablecer vistas de post"

#: inc/parts/admin/database_tool.php:123
msgid "Reset views counter on all content."
msgstr "Restablecer el contador de vistas en todo el contenido."

#: inc/parts/admin/database_tool.php:132
msgid "Generate pages"
msgstr "Generar páginas"

#: inc/parts/admin/database_tool.php:134
msgid "Generate all the required pages."
msgstr "Generar todas las páginas requeridas."

#: inc/parts/comments.php:38
#, php-format
msgid "<strong>Disqus:</strong> add shortname your comunity %s"
msgstr "<strong>Disqus:</strong> agregar Shortname su comunidad %s"

#: inc/parts/item.php:65
msgid "min"
msgstr "min"

#: inc/parts/item.php:66
msgid "views"
msgstr "vistas"

#: inc/parts/item_b.php:13 pages/search.php:31
msgid "TV"
msgstr "TV"

#: inc/parts/item_ep.php:28
#, php-format
msgid "S%s E%s"
msgstr "T%s E%s"

#: inc/parts/item_se.php:29 pages/search.php:34
msgid "Season"
msgstr "Temporada"

#: inc/parts/links_editor.php:30
msgid "Add a link per line"
msgstr "Agrega un enlace por línea"

#: inc/parts/links_editor.php:34 inc/parts/links_editor.php:38
msgid "Add Links"
msgstr "Agregar enlances"

#: inc/parts/links_editor.php:39
msgid "Reload List"
msgstr "Recargar lista"

#: inc/parts/links_editor_single.php:32
msgid "File size (optional)"
msgstr "Tamaño del archivo (opcional)"

#: inc/parts/links_editor_single.php:35
msgid "Save data"
msgstr "Guardar datos"

#: inc/parts/links_editor_type.php:23
msgid "URL Link"
msgstr "URL"

#: inc/parts/modules/blog.php:30 inc/parts/modules/episodes.php:43
#: inc/parts/modules/movies.php:44 inc/parts/modules/seasons.php:43
#: inc/parts/modules/top-imdb.php:86 inc/parts/modules/top-imdb.php:95
#: inc/parts/modules/top-imdb.php:108 inc/parts/modules/top-imdb.php:121
#: inc/parts/modules/tvshows.php:44 inc/widgets/content_widget_home.php:49
#: pages/rating.php:26 pages/trending.php:27
msgid "See all"
msgstr "Ver todo"

#: inc/parts/modules/featured-post-movies.php:36
msgid "Featured Movies"
msgstr "Películas destacadas"

#: inc/parts/modules/featured-post-tvshows.php:36
msgid "Featured TV Shows"
msgstr "Series destacadas"

#: inc/parts/modules/top-imdb.php:86 inc/parts/modules/top-imdb.php:108
msgid "TOP Movies"
msgstr "TOP Movies"

#: inc/parts/modules/top-imdb.php:95 inc/parts/modules/top-imdb.php:121
msgid "TOP TVShows"
msgstr "TOP Series"

#: inc/parts/player_editor.php:8
msgid "URL source or Shortcode"
msgstr "URL del recurso o Shortcode"

#: inc/parts/player_editor.php:9
msgid "Flag Language"
msgstr "Bandera de idioma"

#: inc/parts/player_editor.php:10
msgid "Control"
msgstr "Control"

#: inc/parts/player_editor.php:85
msgid "Add new row"
msgstr "Añadir nueva fila"

#: inc/parts/single/doo_links.php:37
msgid "Please wait until the time runs out"
msgstr "Por favor, espere hasta que acabe el tiempo"

#: inc/parts/single/doo_links.php:46
msgid "Get this torrent"
msgstr "Obtener este torrent"

#: inc/parts/single/doo_links.php:48
#, php-format
msgid "Are you going to %s"
msgstr "Te estamos redireccionando a %s"

#: inc/parts/single/episodios.php:22 inc/parts/single/peliculas.php:23
#, php-format
msgid "%s Views"
msgstr "%s Vistas"

#: inc/parts/single/episodios.php:22 inc/parts/single/peliculas.php:23
msgid "0 Views"
msgstr "0 Vistas"

#: inc/parts/single/links.php:34
msgid "URL"
msgstr "URL"

#: inc/parts/single/links.php:73
msgid "Add row"
msgstr "Añadir nueva fila"

#: inc/parts/single/links.php:74
msgid "Send link(s)"
msgstr "Enviar enlace(s)"

#: inc/parts/single/listas/episode_navigator.php:26
msgid "PREV"
msgstr "ANTERIOR"

#: inc/parts/single/listas/episode_navigator.php:28
msgid "ALL"
msgstr "TODO"

#: inc/parts/single/listas/episode_navigator.php:30
msgid "NEXT"
msgstr "SIGUIENTE"

#: inc/parts/single/listas/seasons.php:38
#: inc/parts/single/listas/seasons_episodes.php:62
msgid "There are still no episodes this season"
msgstr "Todavía no hay episodios para mostrar en esta temporada"

#: inc/parts/single/listas/seasons_episodes.php:16
msgid "Seasons and episodes"
msgstr "Temporadas y episodios"

#: inc/parts/single/listas/seasons_episodes.php:28
msgid "Specials"
msgstr "Especiales"

#: inc/parts/single/listas/seasons_episodes.php:28
#, php-format
msgid "Season %s %s"
msgstr "Temporada %s %s"

#: inc/parts/single/peliculas.php:73
msgid "Min."
msgstr "Min."

#: inc/parts/single/peliculas.php:93 inc/parts/single/series.php:85
msgid "Info"
msgstr "Info"

#: inc/parts/single/peliculas.php:104 inc/parts/single/series.php:123
msgid "Synopsis"
msgstr "Sinopsis"

#: inc/parts/single/peliculas.php:117
msgid "IMDb Rating"
msgstr "IMDb Rating"

#: inc/parts/single/peliculas.php:119 inc/parts/single/peliculas.php:126
#: inc/parts/single/series.php:138
#, php-format
msgid "%s votes"
msgstr "%s votos"

#: inc/parts/single/peliculas.php:120
msgid "Update Rating"
msgstr "Update Rating"

#: inc/parts/single/peliculas.php:125 inc/parts/single/series.php:136
msgid "TMDb Rating"
msgstr "TMDb Rating"

#: inc/parts/single/post.php:34
msgid "Categories"
msgstr "Categorías"

#: inc/parts/single/post.php:39
msgid "Tags"
msgstr "Etiquetas"

#: inc/parts/single/post.php:52
msgid "Add widgets"
msgstr "Añadir widget"

#: inc/parts/single/relacionados.php:17
msgid "Similar titles"
msgstr "Títulos similares"

#: inc/parts/single/report-video.php:6
msgid "What's happening?"
msgstr "¿Sucedió algo?"

#: inc/parts/single/report-video.php:38
msgid "What is the problem? Please explain.."
msgstr "Cual es el problema? por favor explíquenos."

#: inc/parts/single/report-video.php:41
msgid "Email address"
msgstr "Correo electrónico"

#: inc/parts/single/report-video.php:44
msgid "Send report"
msgstr "Enviar reporte"

#: inc/parts/single/series.php:87
msgid "Trailer"
msgstr "Trailer"

#: inc/parts/single/series.php:143
msgid "First air date"
msgstr "Fecha de primera emisión"

#: inc/parts/single/series.php:163
msgid "Average Duration"
msgstr "Duración media"

#: inc/parts/single/series.php:164
#, php-format
msgid "%s minutes"
msgstr "%s minutos"

#: inc/widgets/content_related_widget.php:16
msgid "A widget to show related content in the sidebar"
msgstr "Un widget para mostrar el contenido relacionado"

#: inc/widgets/content_related_widget.php:18
msgid "DooPlay - Sidebar related content"
msgstr "DooPlay - Contenido relacionado"

#: inc/widgets/content_related_widget.php:98 inc/widgets/content_widget.php:63
#: inc/widgets/content_widget_meta_genres.php:48
#: inc/widgets/content_widget_meta_releases.php:49
#: inc/widgets/content_widget_social.php:91
#: inc/widgets/content_widget_views.php:61
msgid "Title:"
msgstr "Título:"

#: inc/widgets/content_related_widget.php:102 inc/widgets/content_widget.php:67
#: inc/widgets/content_widget_views.php:75
msgid "Layout style"
msgstr "Estilo de diseño"

#: inc/widgets/content_related_widget.php:104 inc/widgets/content_widget.php:69
#: inc/widgets/content_widget_views.php:77
msgid "Style 1 - image Backdrop"
msgstr "Estilo 1 - Imagen Brackdrop"

#: inc/widgets/content_related_widget.php:105 inc/widgets/content_widget.php:70
#: inc/widgets/content_widget_views.php:78
msgid "Style 2 - image Poster"
msgstr "Estilo 2 - Imagen poster"

#: inc/widgets/content_related_widget.php:106 inc/widgets/content_widget.php:71
#: inc/widgets/content_widget_views.php:79
msgid "Style 3 - no image"
msgstr "Estilo 3 - sin imagen"

#: inc/widgets/content_related_widget.php:114 inc/widgets/content_widget.php:88
msgid "Content order"
msgstr "Orden del contenido"

#: inc/widgets/content_related_widget.php:122 inc/widgets/content_widget.php:96
#: inc/widgets/content_widget_home.php:158
msgid "Activate random order"
msgstr "Activar orden aleatorio"

#: inc/widgets/content_widget.php:16
msgid "A widget to show content in the sidebar"
msgstr "Un widget para mostrar el contenido de la barra lateral"

#: inc/widgets/content_widget.php:18
msgid "DooPlay - Sidebar content"
msgstr "DooPlay - Sidebar contenido"

#: inc/widgets/content_widget.php:75 inc/widgets/content_widget_home.php:120
msgid "Content type"
msgstr "Tipo de contenido"

#: inc/widgets/content_widget.php:80
msgid "Movies and Shows"
msgstr "Películas y Series"

#: inc/widgets/content_widget_home.php:16
msgid "Sort content by genres"
msgstr "Ordenar contenido por generos"

#: inc/widgets/content_widget_home.php:18
msgid "DooPlay - [widgetgenre] Genres"
msgstr "DooPlay - [widgetgenre] Generos"

#: inc/widgets/content_widget_home.php:92
msgid "Error: only homepage"
msgstr "Error: sólo página de inicio"

#: inc/widgets/content_widget_home.php:140
msgid "Autoplay Carousel"
msgstr "Autoplay Carousel"

#: inc/widgets/content_widget_home.php:143
msgid "Speed Carousel"
msgstr "Velocidad Carousel"

#: inc/widgets/content_widget_home.php:145
msgid "second"
msgstr "segundo"

#: inc/widgets/content_widget_meta_genres.php:15
msgid "Full list of genres"
msgstr "Lista completa de géneros publicados"

#: inc/widgets/content_widget_meta_genres.php:17
msgid "DooPlay - Genres list"
msgstr "DooPlay - Lista de generos"

#: inc/widgets/content_widget_meta_genres.php:53
#: inc/widgets/content_widget_meta_releases.php:54
msgid "Enable scrolling"
msgstr "Habilitar Scrolling"

#: inc/widgets/content_widget_meta_releases.php:16
msgid "Full list release year"
msgstr "Lista completa Años de estreno"

#: inc/widgets/content_widget_meta_releases.php:18
msgid "DooPlay - Release year list"
msgstr "DooPlay - Lista Años de estreno"

#: inc/widgets/content_widget_meta_releases.php:46
msgid "Release year"
msgstr "Año de lanzamiento"

#: inc/widgets/content_widget_social.php:80
msgid "Dooplay - Socialbox"
msgstr "DooPlay - SocialBox"

#: inc/widgets/content_widget_social.php:80
msgid "Displays links to social networks in a stylish manner"
msgstr "Muestra enlaces a las redes sociales de manera elegante"

#: inc/widgets/content_widget_social.php:95
msgid "Insert the URLs to your social networks"
msgstr "Introduzca las direcciones URL a sus redes sociales"

#: inc/widgets/content_widget_views.php:16
msgid "A widget to display Popular content"
msgstr "Un widget para mostrar el contenido Popular"

#: inc/widgets/content_widget_views.php:18
msgid "DooPlay - Sidebar Popular content"
msgstr "DooPlay - Contenido popular"

#: inc/widgets/content_widget_views.php:65
msgid "Popular content by"
msgstr "Contenido popular por"

#: inc/widgets/content_widget_views.php:67
msgid "Visits"
msgstr "Visitas"

#: inc/widgets/content_widget_views.php:68
msgid "Likes"
msgstr "Gustos"

#: inc/widgets/content_widget_views.php:69
msgid "Users rating"
msgstr "Users rating"

#: inc/widgets/content_widget_views.php:70
msgid "IMDb rating"
msgstr "IMDb rating"

#: inc/widgets/content_widget_views.php:71
msgid "TMDb rating"
msgstr "TMDb Promedio"

#: inc/widgets/widgets.php:18
msgid "Sidebar home page"
msgstr "Sidebar pagina principal"

#: inc/widgets/widgets.php:20 inc/widgets/widgets.php:35
#: inc/widgets/widgets.php:49 inc/widgets/widgets.php:63
#: inc/widgets/widgets.php:78
msgid "Add widgets here to appear in your sidebar."
msgstr "Añadir widgets aquí.  Aparecerán en la barra lateral."

#: inc/widgets/widgets.php:33
msgid "Sidebar Movies single"
msgstr "Sidebar Peliculas single"

#: inc/widgets/widgets.php:47
msgid "Sidebar TVShows single"
msgstr "Sidebar Series single"

#: inc/widgets/widgets.php:61
msgid "Sidebar Seasons single"
msgstr "Sidebar Temporadas single"

#: inc/widgets/widgets.php:76
msgid "Sidebar Posts single"
msgstr "Sidebar Posts single"

#: inc/widgets/widgets.php:92
msgid "[widgetgenre] Genres"
msgstr "[widgetgenre] Generos"

#: inc/widgets/widgets.php:94
msgid "Only widgets for the genres in home modules."
msgstr "Solo Widgets destinados a los módulos del home."

#: pages/account.php:23
msgid "You do not have permission to access this page"
msgstr "No tienes permisos para acceder a esta página"

#: pages/account.php:23
msgid "Module disabled"
msgstr "Modulo Deshabilitado"

#: pages/contact.php:9
msgid ""
"Have something to notify our support team, please do not hesitate to use "
"this form."
msgstr ""
"Si tiene algo que notificarnos, no dude en ponerse en contacto con nuestro "
"equipo de soporte."

#: pages/contact.php:35
msgid "How can we help?"
msgstr "¿En qué podemos ayudarte?"

#: pages/contact.php:39
msgid "Your message"
msgstr "Tu mensaje"

#: pages/contact.php:40
msgid "The more descriptive you can be the better we can help."
msgstr "Sea lo más descriptivo que pueda ser para poder ayudarlo mejor."

#: pages/contact.php:44
msgid "Link Reference (optional)"
msgstr "Link de referencia (opcional)"

#: pages/contact.php:49
msgid "Send message"
msgstr "Enviar mensaje"

#: pages/contact.php:57
msgid "Contact form disabled"
msgstr "Formulario de contacto deshabilitado"

#: pages/letter.php:6 pages/search.php:60
msgid "No results to show with"
msgstr "No hay resultados para"

#: pages/letter.php:9 pages/search.php:63
msgid "Make sure all words are spelled correctly."
msgstr "Asegúrese de que todas las palabras estén escritas correctamente."

#: pages/letter.php:10 pages/search.php:64
msgid "Try different keywords."
msgstr "Intenta utilizar otras palabras."

#: pages/letter.php:11 pages/search.php:65
msgid "Try more general keywords."
msgstr "Prueba palabras clave más generales."

#: pages/search.php:2
msgid "Results found:"
msgstr "Resultados encontrados:"

#: pages/search.php:32
msgid "Post"
msgstr "Post"

#: pages/sections/account.php:58
msgid "Seen"
msgstr "Visto"

#: pages/sections/account.php:66
msgid "My favorites"
msgstr "Mis favoritos"

#: pages/sections/account.php:78
msgid "Marked as view"
msgstr "Marcado como visto"

#: pages/sections/account.php:94
msgid "pendings"
msgstr "pendientes"

#: pages/sections/account.php:109 pages/sections/account.php:131
msgid "Status"
msgstr "Estado"

#: pages/sections/account.php:151
msgid "About"
msgstr "Sobre mi"

#: pages/sections/account.php:158
msgid "E-mail"
msgstr "E-mail"

#: pages/sections/account.php:162
msgid "First name"
msgstr "Nombre"

#: pages/sections/account.php:166 pages/sections/register-form.php:20
msgid "Last name"
msgstr "Apellido"

#: pages/sections/account.php:170
msgid "Display name publicly as"
msgstr "Mostrar nombre públicamente como"

#: pages/sections/account.php:197
msgid "Website"
msgstr "Sitio Web"

#: pages/sections/account.php:201
msgid "Facebook url"
msgstr "Facebook url"

#: pages/sections/account.php:205
msgid "Twitter url"
msgstr "Twitter url"

#: pages/sections/account.php:211
msgid "Description"
msgstr "Descripción"

#: pages/sections/account.php:217
msgid "New password *"
msgstr "Nueva contraseña *"

#: pages/sections/account.php:222
msgid "Repeat password *"
msgstr "Repetir contraseña *"

#: pages/sections/account.php:227
msgid "Update account"
msgstr "Actualizar cuenta"

#: pages/sections/dt_foot.php:5
msgid "please wait..."
msgstr "por favor espere..."

#: pages/sections/login-form.php:11
msgid "Stay logged in"
msgstr "Permanecer conectado"

#: pages/sections/login-form.php:16
msgid "Don't you have an account yet?"
msgstr "¿No tienes una cuenta?"

#: pages/sections/login-form.php:16
msgid "Sign up here"
msgstr "Registrarse aquí"

#: pages/sections/login-form.php:17
msgid "I forgot my password"
msgstr "Olvidé mi contraseña"

#: pages/sections/login.php:5
msgid "FAILED: Try again!"
msgstr "ERROR: Inténtelo de nuevo!"

#: pages/sections/register-form.php:7
msgid "E-mail address"
msgstr "Correo electrónico"

#: pages/sections/register-form.php:25
msgid "Sign up"
msgstr "Registrarse"

#: pages/sections/register-form.php:26
msgid "Do you already have an account?"
msgstr "¿Ya tienes una cuenta?"

#: pages/sections/register-form.php:26
msgid "Login here"
msgstr "Iniciar sesión aquí"

#: pages/sections/register.php:3
msgid "Sign up, it's free.."
msgstr "Registrese, es gratis.."

#: tag.php:17
#, php-format
msgid "Tag Archives: %s"
msgstr "Archivos de etiqueta: %s"

#. Theme Name of the plugin/theme
msgid "DooPlay"
msgstr "DooPlay"

#. Theme URI of the plugin/theme
msgid "https://doothemes.com/items/dooplay/"
msgstr "https://doothemes.com/items/dooplay/"

#. Description of the plugin/theme
msgid ""
"Dooplay is undoubtedly the best and most powerful framework, with access to "
"a large volume of content with a single click, completely optimized."
msgstr ""
"Dooplay es, sin duda, el mejor y más potente framework, con acceso a un gran "
"volumen de contenido con un solo clic, completamente optimizado."

#. Author of the plugin/theme
msgid "Doothemes"
msgstr "Doothemes"

#. Author URI of the plugin/theme
msgid "https://doothemes.com/"
msgstr "https://doothemes.com/"

#~ msgid "Quick publisher"
#~ msgstr "Publicador rapido"

#~ msgid "Font Awesome"
#~ msgstr "Font Awesome"

#~ msgid "Inclusion Mode"
#~ msgstr "Modo de inclusión"

#~ msgid "Local"
#~ msgstr "Local"

#~ msgid "CDN"
#~ msgstr "CDN"

#~ msgid "Kit URL"
#~ msgstr "URL del kit"

#~ msgid "Add Your Kit's Code to this Project"
#~ msgstr "Agregue el código de su kit a este proyecto"

#~ msgid "Google+ url"
#~ msgstr "Google+ url"

#~ msgid "Success. Imported backup options."
#~ msgstr "Excelente, todas las opciones han sido importadas."

#~ msgid "Default options restored for only this section."
#~ msgstr "Las opciones por defecto se restauraron sólo para esta sección."

#~ msgid "Settings have changed, you should save them!"
#~ msgstr "La configuración ha cambiado, debe guardarlas!"

#~ msgid "Search option(s)"
#~ msgstr "Buscar opciones"

#~ msgid "Are you sure to reset all options?"
#~ msgstr "¿Está seguro de restablecer todas las opciones?"

#~ msgid "No option provided by developer."
#~ msgstr "No hay opción proporcionada por el desarrollador."

#~ msgid "Restore"
#~ msgstr "Restaurar"

#~ msgid "update post for restore "
#~ msgstr "actualizar la publicación para restaurar "

#~ msgid "Restoring options."
#~ msgstr "Restauración de opciones."

#~ msgid "Importing options."
#~ msgstr "Importación de opciones."

#~ msgid "No results match"
#~ msgstr "No se encontraron resultados"

#, php-format
#~ msgid "Ooops! This field type (%s) can not be used here, yet."
#~ msgstr "Ooops! Este tipo de campo (%s) no se puede utilizar aquí, todavía."

#~ msgid "This field class is not available!"
#~ msgstr "¡Esta clase de campo no está disponible!"

#~ msgid "This type is not found!"
#~ msgstr "¡Este tipo no se encuentra!"

#~ msgid "Add one more"
#~ msgstr "Añadir uno más"

#~ msgid "Error: Nonce verification has failed. Please try again."
#~ msgstr ""
#~ "Error: La verificación de Nonce ha fallado. Por favor, inténtelo de nuevo."

#~ msgid "No background selected"
#~ msgstr "Sin fondo seleccionado"

#~ msgid "copy-paste your backup string here"
#~ msgstr "copie-pegue su cadena de respaldo aquí"

#~ msgid "Please be sure for reset all of options."
#~ msgstr "Por favor este seguro de querer restablecer todas las opciones."

#~ msgid "No data provided for this option type."
#~ msgstr "No se proporcionan datos para este tipo de opción."

#~ msgid "Error: Nested field id can not be same with another nested field id."
#~ msgstr ""
#~ "Error: el ID de campo anidado no puede ser el mismo con otro ID de campo "
#~ "anidado."

#~ msgid "Search a Icon..."
#~ msgstr "Buscar un icono..."

#~ msgid "Search your address..."
#~ msgstr "Busca en tu dirección..."

#~ msgid "Add Shortcode"
#~ msgstr "Agregar Shortcode"

#~ msgid "No data provided by developer"
#~ msgstr "No hay datos proporcionados por el desarrollador"

#~ msgid "Error: Options unique id could not valid."
#~ msgstr "Error: Las opciones unique id no pudieron ser válidas."

#~ msgid "Error: Import data could not valid."
#~ msgstr "Error: Los datos de importación no pueden ser válidos."

#~ msgid "Error: Missing request arguments."
#~ msgstr "Error: faltan argumentos de solicitud."

#~ msgid "You do not have required permissions to access."
#~ msgstr "No tiene permisos necesarios para acceder."

#~ msgid "Please write a numeric data!"
#~ msgstr "Por favor, escriba un dato numérico!"

#~ msgid "WhatsApp"
#~ msgstr "WhatsApp"

#~ msgid "Remove <code>?ver=</code> parameters"
#~ msgstr "Eliminar parámetros <code>?ver=</code>"

#~ msgid "BUG REPORT"
#~ msgstr "INFORME DE ERRORES"

#~ msgid "IP"
#~ msgstr "IP"

#~ msgid "Thank you! Your report was submitted.."
#~ msgstr "¡Gracias! Su reporte ha sido enviado con éxito."

#~ msgid "Remove HTML, Java Script and CSS Commnets"
#~ msgstr "Eliminar comentarios de código en HTML, CSS y Java Script"

#~ msgid "Video Error Report"
#~ msgstr "Error en video"

#~ msgid ""
#~ "An error has been reported in the video of your content, get more details "
#~ "in your WordPress admin panel."
#~ msgstr ""
#~ "Se ha informado de un error en el vídeo de su contenido, obtenga más "
#~ "detalles en su panel de administración de WordPress."

#~ msgid "WP-Cron"
#~ msgstr "WP-Cron"

#~ msgid "Solved"
#~ msgstr "Resuelto"

#~ msgid "Run"
#~ msgstr "Ejecutar"

#~ msgid "Updater"
#~ msgstr "Actualizador"

#~ msgid "previous episode"
#~ msgstr "episodio anterior"

#~ msgid "episodes list"
#~ msgstr "lista de episodios"

#~ msgid "next episode"
#~ msgstr "siguiente episodio"

#~ msgid "Service started"
#~ msgstr "Servicio iniciado"

#~ msgid "Results"
#~ msgstr "Resultados"

#~ msgid "Filter"
#~ msgstr "Filtrar"

#~ msgid "Titles"
#~ msgstr "Titulos"

#~ msgid "Google+ URL"
#~ msgstr "Google+ URL"

#~ msgid "Report"
#~ msgstr "Reporte"

#~ msgid "what going on?"
#~ msgstr "¿Sucedió algo?"

#~ msgid "Your email is only visible to moderators"
#~ msgstr "Correo electrónico, visible solo para moderadores y administradores"

#~ msgid "Message sent, at any time one of our operators will contact you."
#~ msgstr ""
#~ "Mensaje enviado con éxito, en cualquier momento uno de nuestros "
#~ "operadores se pondrán en contacto contigo."

#~ msgid "CONFIG"
#~ msgstr "CONFIG"

#~ msgid "USAGE"
#~ msgstr "USAGE"

#~ msgid "VALUE"
#~ msgstr "VALUE"

#~ msgid "ID"
#~ msgstr "ID"

#, php-format
#~ msgid "You are editing language: ( %s )"
#~ msgstr "Estás editando el idioma : ( %s )"

#~ msgid "Select a shortcode"
#~ msgstr "Seleccionar un shortcode"

#~ msgid "Insert Shortcode"
#~ msgstr "Insertar Shortcode"

#~ msgid "Fusion stye"
#~ msgstr "Fusion stye"

#~ msgid "Use Image"
#~ msgstr "Usar imagen"

#~ msgid "Import a Backup"
#~ msgstr "Importar Backup"

#~ msgid "Adding"
#~ msgstr "Agregando"

#~ msgid "Add Image"
#~ msgstr "Agregar imagen"

#~ msgid "Enabled Modules"
#~ msgstr "Módulos habilitados"

#~ msgid "Disabled Modules"
#~ msgstr "Módulos deshabilitados"

#~ msgid "on"
#~ msgstr "on"

#~ msgid "Error! Can not load json file."
#~ msgstr "¡Error! No se puede cargar el archivo json."

#, fuzzy
#~| msgid "Dooplay cache"
#~ msgid "Empty cache"
#~ msgstr "Caché Dooplay"

#~ msgid "List of seasons and episodes"
#~ msgstr "Lista de temporadas y episodios"

#~ msgid "Seasons List"
#~ msgstr "Lista de temporadas"

#~ msgid "Sort seasons list"
#~ msgstr "Ordenar lista de temporadas"

#~ msgid "Episodes List"
#~ msgstr "Lista de episodios"

#~ msgid "Sort episodes list"
#~ msgstr "Ordenar lista de episodios"

#~ msgid "season x episode"
#~ msgstr "temporada x episodios"

#~ msgid "Gutenberg Editor"
#~ msgstr "Gutenberg Editor "

#~ msgid ""
#~ "At the moment using Gutemberg is optional, check or uncheck to enable or "
#~ "disable"
#~ msgstr ""
#~ "Por el momento, el uso de Gutemberg es opcional, marcar o desmarcar para "
#~ "activar o desactivar"

#~ msgid "‹"
#~ msgstr "‹"

#~ msgid "›"
#~ msgstr "›"

#~ msgid "×"
#~ msgstr "×"

#~ msgid "Import and delete"
#~ msgstr "Importar y eliminar"

#~ msgid "This content already exists in the database"
#~ msgstr "Este contenido ya existe en la base de datos"

#~ msgid "Completed process"
#~ msgstr "Proceso completado"

#~ msgid "Getting data.."
#~ msgstr "Obteniendo datos.."

#~ msgid "Filtering.."
#~ msgstr "Filtrando.."

#~ msgid "Dbmovies license"
#~ msgstr "Licencia para dbmovies"

#~ msgid "Dbmovies for DooPlay"
#~ msgstr "Dbmovies para DooPlay"

#~ msgid ""
#~ "It is important to have a valid product license, keep the file updated "
#~ "and access exclusive content."
#~ msgstr ""
#~ "Es importante tener una licencia de producto válida, mantener el archivo "
#~ "actualizado y acceder a contenido exclusivo."

#~ msgid "Access to all updates."
#~ msgstr "Acceso a todas las actualizaciones."

#~ msgid "Access to dedicated support."
#~ msgstr "Acceso a soporte dedicado."

#~ msgid "Access to all dbmovies tools."
#~ msgstr "Acceso a todas las herramientas dbmovies."

#~ msgid "Compatibility with our themes."
#~ msgstr "Compatibilidad con nuestros temas."

#~ msgid "Security Guaranteed."
#~ msgstr "Seguridad garantizada."

#~ msgid ""
#~ "You can filter content by year of release, if the field where text of the "
#~ "year is empty, will give you results with the most popular content, you "
#~ "can also add the filter by genres and sort by popularity order."
#~ msgstr ""
#~ "Puede filtrar el contenido por año de publicación, si el campo donde el "
#~ "texto del año está vacío, obtendrá resultados con el contenido más "
#~ "popular, también puede agregar el filtro por género y ordenar por orden "
#~ "de popularidad."

#~ msgid "Searching content"
#~ msgstr "Búsqueda de contenido"

#~ msgid ""
#~ "In the search field, you can add a specific title that you want to find, "
#~ "you will find results with the names of actors or directors, it is "
#~ "limited to search titles of movies or TV series."
#~ msgstr ""
#~ "En el campo de búsqueda, puede agregar un título específico que desee "
#~ "buscar, encontrará resultados con los nombres de actores o directores, "
#~ "está limitado a buscar títulos de películas o series de TV."

#~ msgid "Keyboard Shortcuts"
#~ msgstr "Atajos de teclado"

#~ msgid "You can use some shortcuts with the keyboard to make the job easier."
#~ msgstr ""
#~ "Puede usar algunos accesos directos con el teclado para facilitar el "
#~ "trabajo."

#~ msgid "Access plugin settings."
#~ msgstr "Ajustes de Plugin."

#~ msgid "It frees the screen of any popup window."
#~ msgstr "Libera la pantalla de cualquier ventana emergente."

#~ msgid "Go to next page in the results"
#~ msgstr "Ir a la página siguiente en los resultados"

#~ msgid "Return to previous page in the results."
#~ msgstr "Retornar a la página anterior en los resultados."

#~ msgid "Error codes"
#~ msgstr "Códigos de error"

#~ msgid "Access denied."
#~ msgstr "Acceso denegado."

#~ msgid "API key not found."
#~ msgstr "Clave de API no encontrado."

#~ msgid "Internal server error."
#~ msgstr "Error interno del servidor."

#~ msgid "Application in maintenance."
#~ msgstr "Aplicación en mantenimiento."

#~ msgid "Exceeded request limit."
#~ msgstr "Límite de solicitud excedido."

#~ msgid ""
#~ "We recommend that you do not exceed the established server request limits."
#~ msgstr ""
#~ "Le recomendamos que no exceda los límites establecidos de solicitud del "
#~ "servidor."

#~ msgid "domain per license key."
#~ msgstr "dominio por clave de licencia."

#~ msgid "requests per second."
#~ msgstr "solicitudes por segundo."

#~ msgid "requests per minute."
#~ msgstr "solicitudes por minuto."

#~ msgid "requests per hour."
#~ msgstr "solicitudes por hora."

#~ msgid "requests per day."
#~ msgstr "solicitudes por día."

#~ msgid ""
#~ "<strong>NOTE:</strong> Do not share your license key, remember that it "
#~ "can only be used for your domain."
#~ msgstr ""
#~ "<strong>NOTA:</strong> No comparta su clave de licencia, recuerde que "
#~ "solo se puede utilizar para un solo dominio."

#~ msgid "Unexpected error"
#~ msgstr "Error inesperado"

#~ msgid "dbmovies"
#~ msgstr "dbmovies"

#~ msgid "prev"
#~ msgstr "anterior"

#~ msgid "next"
#~ msgstr "siguiente"

#~ msgid "Invalid License"
#~ msgstr "Licencia invalida"

#~ msgid "Dbmovies Options"
#~ msgstr "Dbmovies Opciones"

#~ msgid "Get license key now"
#~ msgstr "Obtener clave de licencia ahora"

#~ msgid "Dbmovies API key"
#~ msgstr "Dbmovies API Key"

#~ msgid ""
#~ "<strong>NOTE:</strong> Do not share this information, this key is only "
#~ "authorized for your website."
#~ msgstr ""
#~ "<strong>IMPORTANTE:</strong> No comparta esta información, la clave solo "
#~ "está autorizada para su sitio web."

#~ msgid "Do you want to activate import tool?"
#~ msgstr "¿Quieres activar la herramienta de importación?"

#~ msgid "Themoviedb API key"
#~ msgstr "Themoviedb API key"

#~ msgid "API Key"
#~ msgstr "API Key"

#~ msgid "Languague"
#~ msgstr "Idioma de importación"

#~ msgid "Upload poster image to server?"
#~ msgstr "¿Subir la imagen del póster al servidor?"

#~ msgid "Do you want to autocomplete genres?"
#~ msgstr "¿Desea autocompletar géneros?"

#~ msgid "Publish content with the release date?"
#~ msgstr "¿Publicar contenido con la fecha de lanzamiento?"

#~ msgid "Get content now"
#~ msgstr "Obtener contenido ahora"

#~ msgid "Add to featured"
#~ msgstr "Añadir a destacados"

#~ msgid "Rating user"
#~ msgstr "Calificación de suario"

#~ msgid "Requests page"
#~ msgstr "Página de solicitudes"

#~ msgid "Unregistered users can add content to the database"
#~ msgstr ""
#~ "Los usuarios no registrados pueden agregar contenido a la base de datos"

#~ msgid "Get api key"
#~ msgstr "Obtener clave API"

#~ msgid "Dbmovies Remote Options"
#~ msgstr "Opciones remotas de Dbmovies"

#~ msgid "Notify by email of new updates"
#~ msgstr "Notificar por correo electrónico las nuevas actualizaciones"

#~ msgid "Add this website to my sync list"
#~ msgstr "Agregar este sitio web a mi lista de sincronización"

#~ msgid "Themoviedb"
#~ msgstr "Themoviedb API key"

#~ msgid "Set api Language"
#~ msgstr "Establecer lenguaje de API"

#~ msgid "Api controls"
#~ msgstr "Controles de API"

#~ msgid "Activate infinite scroll for results"
#~ msgstr "Activar desplazamiento automático para los resultados"

#~ msgid "Infinite Scroll Result limits"
#~ msgstr "Límites de resultados de desplazamiento automático"

#~ msgctxt "Post Type General Name"
#~ msgid "Episodes"
#~ msgstr "Episodios"

#~ msgctxt "Post Type Singular Name"
#~ msgid "Episodes"
#~ msgstr "Episodios"

#~ msgid "Title Episode"
#~ msgstr "Título de episodio"

#~ msgctxt "Post Type General Name"
#~ msgid "Seasons"
#~ msgstr "Temporadas"

#~ msgctxt "Post Type Singular Name"
#~ msgid "Seasons"
#~ msgstr "Temporadas"

#~ msgctxt "Post Type General Name"
#~ msgid "TV Shows"
#~ msgstr "Series"

#~ msgctxt "Post Type Singular Name"
#~ msgid "TV Show"
#~ msgstr "Serie"

#~ msgid "TV Shows %%PENDING_COUNT_TV%%"
#~ msgstr "TV Shows %%PENDING_COUNT_TV%%"

#~ msgid "Generate episodes"
#~ msgstr "Generar episodios"

#~ msgid "Dbmovies Plus"
#~ msgstr "Dbmovies Plus"

#~ msgid "Safe mode"
#~ msgstr "Modo seguro"

#~ msgid ""
#~ "Method safe, the process will continue only if the above process is "
#~ "completed"
#~ msgstr ""
#~ "Método seguro, el proceso continuará solamente si se ha completado el "
#~ "proceso anterior"

#~ msgid ""
#~ "dbmovies API has not been activated, active to be able to generate content"
#~ msgstr "dbmovies API no tiene permiso para poder generar contenido"

#~ msgid "Active Dbmovies now"
#~ msgstr "Activar Dbmovies ahora"

#~ msgid "Generating seasons"
#~ msgstr "Generando temporadas"

#~ msgid "not close this page to complete the upload"
#~ msgstr "no cierre la página hasta completar la carga"

#~ msgid "Generating episodes"
#~ msgstr "Generando episodios"

#~ msgid "no data"
#~ msgstr "no hay datos"

#~ msgid "Coming soon"
#~ msgstr "Próximamente"

#~ msgid "Manually adding content"
#~ msgstr "Añadir manualmente contenido"

#~ msgid "No episodes to show"
#~ msgstr "No hay episodios que mostrar"

#~ msgid "JW Player version"
#~ msgstr "Versión de JW Player"

#~ msgid "Version 8"
#~ msgstr "Versión 8"

#~ msgid "Version 7"
#~ msgstr "Versión 7"

#~ msgid "Library Url"
#~ msgstr "Url de la biblioteca"

#~ msgid "Cloud Hosted Player Libraries"
#~ msgstr "Bibliotecas Cloud Hosted Player"

#~ msgid "Customize player"
#~ msgstr "Personalizar el reproductor"

#~ msgid "Sorry! That was secure, guess you're cheatin huh!"
#~ msgstr "Lo siento, lo que estas tratando de hacer no es seguro!"

#~ msgid "Thank you for registering %s"
#~ msgstr "Gracias por registrarte %s"

#~ msgid "Enable Random order"
#~ msgstr "Habilitar orden aleatorio"

#~ msgid "Check to display content in random order"
#~ msgstr "Marcar para mostrar contenido en orden aleatorio"

#~ msgid "Additional code integration"
#~ msgstr "Integración de código adicional"

#~ msgid "Ad in top"
#~ msgstr "Anuncio en la parte superior"

#~ msgid "Ad in player"
#~ msgstr "Anuncio en el Player"

#~ msgid "Add 300x250 banner to show before loading the video player"
#~ msgstr ""
#~ "Agregar 300x250 banner para mostrar antes de cargar el reproductor de "
#~ "vídeo"

#~ msgid "Add ad that will only show on mobile devices"
#~ msgstr "Agregar anuncio que sólo se mostrará en los dispositivos móviles"

#~ msgid "Ad in homepage"
#~ msgstr "Anuncio en homepage"

#~ msgid "Upload image"
#~ msgstr "Subir imagen"

#~ msgid "Removing.."
#~ msgstr "Removiendo..."

#~ msgid "Saving changes.."
#~ msgstr "Guardando cambios.."

#~ msgid "Clear registers"
#~ msgstr "Limpiar registro"

#~ msgid "Registers"
#~ msgstr "Registros"

#~ msgid "Last revision"
#~ msgstr "Ultima revisión"

#~ msgid "Doothemes License"
#~ msgstr "Licencia Doothemes"

#~ msgid "Tools"
#~ msgstr "Herramientas"

#~ msgid "Performance"
#~ msgstr "Rendimiento"

#~ msgid "Dark Style"
#~ msgstr "Estilo escuro"

#~ msgid "Featured Post"
#~ msgstr "Publicación destacada"

#~ msgid "Basic info"
#~ msgstr "Información básica"

#~ msgid "Ad spot / home module"
#~ msgstr "Bloque / home module"

#~ msgid "Ad spot / redirecting links"
#~ msgstr "Bloque / re-direccionar enlaces"

#~ msgid "Ad spot / single"
#~ msgstr "Bloque / single"

#~ msgid ""
#~ "We recommend enabling these options, they will help in the performance of "
#~ "your website"
#~ msgstr ""
#~ "Recomendamos habilitar estas opciones, le ayudarán en el rendimiento de "
#~ "su sitio web"

#~ msgid ""
#~ "A cache plugin can increase page download speed dramatically. We "
#~ "recommend using %1$s"
#~ msgstr ""
#~ "Un Plugin de caché puede aumentar la velocidad de descarga de la página "
#~ "Web dramáticamente y reduce consultas a la base de datos. Recomendamos "
#~ "usar %1$s"

#~ msgid ""
#~ "<strong>Warning:</strong> This tool deletes information from the database"
#~ msgstr ""
#~ "<strong>Advertencia:</strong> Esta herramienta eliminara registros en la "
#~ "base de datos, recomendamos solo usarla si es necesario"

#~ msgid "CSS code"
#~ msgstr "Código CSS"

#~ msgid "Admin logo"
#~ msgstr "Logo admin"

#~ msgid ""
#~ "Upload your logo for wp-admin login, using the Upload Button or insert "
#~ "image URL"
#~ msgstr ""
#~ "Cargar su logo para wp-admin inicio de sesión, utilizando el botón Subir "
#~ "o insertar URL de la imagen"

#~ msgid "<code>Complete footer</code> require all configuration"
#~ msgstr "<code>Footer completo</code> requiere toda la configuración"

#~ msgid "Footer menu titles"
#~ msgstr "Títulos de menú del footer"

#~ msgid "Footer menu 2"
#~ msgstr "Footer menu 2"

#~ msgid "Footer menu 3"
#~ msgstr "Footer menu 3"

#~ msgid "5"
#~ msgstr "5"

#~ msgid "15"
#~ msgstr "15"

#~ msgid "Ads Home module"
#~ msgstr "Anuncio en Home Page"

#~ msgid "Ad 300x250 pixels"
#~ msgstr "Anuncio de 300x250 pixeles"

#~ msgid "Ad 468x60 pixels"
#~ msgstr "Anuncio de 468x60 pixeles"

#~ msgid "Display ad"
#~ msgstr "Mostrar anuncio en"

#~ msgid "Check to enable ads"
#~ msgstr "Marcar para activar modulo"

#~ msgid ""
#~ "<strong>NOTE:</strong> You can use these tags to personalize your welcome "
#~ "message in sign up."
#~ msgstr ""
#~ "<strong>NOTA:</strong> Puede utilizar estas etiquetas para personalizar "
#~ "su mensaje de bienvenida en el registro."

#~ msgid "Hello {first_name}, welcome to {sitename}."
#~ msgstr "Hola  {first_name}, bienvenido a {sitename}."

#~ msgid "Tagline"
#~ msgstr "Lema"

#~ msgid "In a few words, explain what this site is about."
#~ msgstr "En pocas palabras, explique de qué se trata este sitio."

#~ msgid "Comments default"
#~ msgstr "Comentarios por defecto"

#~ msgid "Choose an option"
#~ msgstr "Elija una opción"

#~ msgid ""
#~ "We recommend setting these fields to moderate the comments facebook, <a "
#~ "href=\"https://developers.facebook.com/docs/plugins/comments\" target="
#~ "\"_blank\">more info</a> "
#~ msgstr ""
#~ "Recomendamos la configuración de estos campos para moderar los "
#~ "comentarios de Facebook, <a href=\"https://developers.facebook.com/docs/"
#~ "plugins/comments\" target=\"_blank\">mas información</a> "

#~ msgid "APP ID"
#~ msgstr "APP ID"

#~ msgid ""
#~ "Insert you Facebook app id here. If you don't have one for your webpage "
#~ "you can create it <a href=\"https://developers.facebook.com/apps/\" "
#~ "target=\"_blank\">here</a>"
#~ msgstr ""
#~ "Insertar la el ID de su APP en Facebook, si no dispone de ninguna ID, "
#~ "puede crear una nueva APP <a href=\"https://developers.facebook.com/apps/"
#~ "\" target=\"_blank\">aquí</a>"

#~ msgid "Choose the color for the comment block"
#~ msgstr "Selecciones la combinación de colores para el bloque de comentarios"

#~ msgid "Number of comments"
#~ msgstr "Numero de comentarios"

#~ msgid "Select number of comments to display "
#~ msgstr "Seleccionar numero de comentarios para mostrar antes de paginar "

#~ msgid ""
#~ "Add your community shortname <a href=\"https://disqus.com/\" target="
#~ "\"_blank\">Disqus</a>"
#~ msgstr ""
#~ "Añadir el shortname de tu comunidad en <a href=\"https://disqus.com/\" "
#~ "target=\"_blank\">Disqus</a>"

#~ msgid "UA-45182606-12"
#~ msgstr "UA-45182606-12"

#~ msgid "Configure pages"
#~ msgstr "Configurar páginas"

#~ msgid "Header code"
#~ msgstr "Código en header"

#~ msgid "Footer code"
#~ msgstr "Código en footer"

#~ msgid "Auto-play video trailers"
#~ msgstr "Auto-play video trailers"

#~ msgid "Enable similar titles"
#~ msgstr "Habilitar títulos similares"

#~ msgid "WordPress Controls"
#~ msgstr "Controles de WordPress"

#~ msgid "Check to disable"
#~ msgstr "Marcar para desactivar"

#~ msgid "Home modular"
#~ msgstr "Homepage modular"

#~ msgid "TOP Imdb"
#~ msgstr "TOP IMDb"

#~ msgid "10"
#~ msgstr "10"

#~ msgid "20"
#~ msgstr "20"

#~ msgid "8"
#~ msgstr "8"

#~ msgid "Activate post links"
#~ msgstr "Activar Publicación de enlaces"

#~ msgid "Check to enable module"
#~ msgstr "Marcar para activar modulo"

#~ msgid "Check to enable"
#~ msgstr "Marque para activar"

#~ msgid "Languages to add links"
#~ msgstr "Idiomas en Agregar enlaces"

#~ msgid "Countdown"
#~ msgstr "Contador regresivo"

#~ msgid "Define timeout for redirect links"
#~ msgstr "Definir tiempo de espera para redirigir enlaces"

#~ msgid "Direct redirect without waiting"
#~ msgstr "Direccionar enlaces de forma directa y sin espera"

#~ msgid "List table"
#~ msgstr "Listar tabla"

#~ msgid ""
#~ "Verification settings <a href=\"https://www.google.com/webmasters/"
#~ "verification/\" target=\"_blank\">here</a>"
#~ msgstr ""
#~ "Ajustes de verificación <a href=\"https://www.google.com/webmasters/"
#~ "verification/\" target=\"_blank\">aquí</a>"

#~ msgid "Alexa Verification ID"
#~ msgstr "ID de verificación en Alexa"

#~ msgid ""
#~ "Verification settings <a href=\"https://www.alexa.com/siteowners/claim/\" "
#~ "target=\"_blank\">here</a>"
#~ msgstr ""
#~ "Ajustes de verificación <a href=\"https://www.alexa.com/siteowners/claim/"
#~ "\" target=\"_blank\">aquí</a>"

#~ msgid ""
#~ "Verification settings <a href=\"https://www.bing.com/toolbox/webmaster/\" "
#~ "target=\"_blank\">here</a>"
#~ msgstr ""
#~ "Ajustes de verificación <a href=\"https://www.bing.com/toolbox/webmaster/"
#~ "\" target=\"_blank\">aquí</a>"

#~ msgid ""
#~ "Verification settings <a href=\"https://yandex.com/support/webmaster/"
#~ "service/rights.xml#how-to\" target=\"_blank\">here</a>"
#~ msgstr ""
#~ "Ajustes de verificación <a href=\"https://yandex.com/support/webmaster/"
#~ "service/rights.xml#how-to\" target=\"_blank\">aquí</a>"

#~ msgid "There are new link(s) added to:"
#~ msgstr "Agregar nuevo enlace(s) a:"

#~ msgid "<strong>PENDING:</strong> requires moderation"
#~ msgstr "<strong>PENDIENTE:</strong> requiere moderación"

#~ msgid "Content submitted, pending moderation .."
#~ msgstr "Contenido enviado, pendiente de moderación ."

#~ msgid "Content published correctly.."
#~ msgstr "Contenido publicado correctamente.."

#~ msgid "account"
#~ msgstr "cuenta"

#~ msgid "Only Key"
#~ msgstr "Solo KEY"

#~ msgid ""
#~ "<strong>Disqus:</strong> add shortname your comunity, <a href=\"https://"
#~ "help.disqus.com/customer/portal/articles/466208-what-s-a-shortname-\" "
#~ "target=\"_blank\">more info</a>"
#~ msgstr ""
#~ "<strong>Disqus:</strong> añadir el shortname de tu comunidad, <a href="
#~ "\"https://help.disqus.com/customer/portal/articles/466208-what-s-a-"
#~ "shortname-\" target=\"_blank\">mas información</a>"

#~ msgid "to post links"
#~ msgstr "para publicar enlaces"

#~ msgid "report"
#~ msgstr "reportar"

#~ msgid "Please wait, redirecting page.."
#~ msgstr "Por favor espere, redirigiendo la página..."

#~ msgid "Turn off the lights"
#~ msgstr "Apagar las luces"

#~ msgid "Show views"
#~ msgstr "Mostrar vistas"

#~ msgid "Check options you want to display."
#~ msgstr "Marque las opciones que desee mostrar."

#~ msgid "Block Ad in player"
#~ msgstr "Bloque de anuncio en el Player"

#~ msgid "Hide ad after clicking"
#~ msgstr "Ocultar anuncios después de hacer clic"

#~ msgid "Hide ad"
#~ msgstr "Ocultar anuncio"

#~ msgid " Time in seconds for show ad "
#~ msgstr " Tiempo en segundos para mostrar el anuncio "

#~ msgid "Ad of 300*250 px"
#~ msgstr "Anuncio de 300*250 pixeles"

#~ msgid "gdrive-player"
#~ msgstr "gdrive-player"

#~ msgid "Google Drive JW Player"
#~ msgstr "Google Drive JW Player"

#~ msgid "Public key"
#~ msgstr "Clave Pública"

#~ msgid "Private Key"
#~ msgstr "Key privado"

#~ msgid "If you can't read the text, click on the image to redraw."
#~ msgstr "Si no puede leer el texto, haga clic en la imagen a dibujar."

#~ msgid "Security check"
#~ msgstr "Control de seguridad"

#~ msgid "please do not skip this step, it is important."
#~ msgstr "por favor no omitir este paso, es importante."

#~ msgid "Page Google Drive jwplayer"
#~ msgstr "Página jwplayer de Google Drive"

#~ msgid "Skin name"
#~ msgstr "Nombre de Skin"

#~ msgid "Inactive"
#~ msgstr "Inactivo"
